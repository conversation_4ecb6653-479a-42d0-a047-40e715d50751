package com.unipus.digitalbook.aop.auth.validator;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.constants.WebConstant;
import com.unipus.digitalbook.model.entity.BackendUserInfo;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Optional;

/**
 * 平台用户JWT校验器
 * - 用于平台用户身份校验，以APPID区分不同的平台
 */
@Component
@Slf4j
public class PlatformJwtValidatorImpl implements JwtValidator {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean canVerify(Map<String, Object> claims) {
        // 取得token中的appid
        Long appId = MapUtils.getLong(claims, WebConstant.JWT_BACKEND_AID);
        // 判断是否允许的appid
        return WebConstant.BACKEND_USER_APPID.equals(appId);
    }

    @Override
    public boolean verify(Map<String, Object> claims, HttpServletRequest request){
        try {
            String accessId = Optional.ofNullable(claims.get(WebConstant.JWT_BACKEND_SID))
                    .map(Object::toString)
                    .filter(StringUtils::hasText)
                    .orElse(null);

            if (accessId == null) {
                log.warn("平台访问ID无效或缺失");
                return false;
            }

            String backendUserInfoJsonStr = stringRedisTemplate.opsForValue().get(CacheConstant.REDIS_PLATFORM_USR_PREFIX + accessId);
            if (!StringUtils.hasText(backendUserInfoJsonStr)) {
                log.warn("平台用户未登录或信息已过期");
                return false;
            }
            BackendUserInfo backendUserInfo = JsonUtil.parseObject(backendUserInfoJsonStr, BackendUserInfo.class);
            if (backendUserInfo == null || backendUserInfo.getOpenId() == null) {
                log.warn("平台用户信息解析失败");
                return false;
            }

            request.setAttribute(WebConstant.JWT_BACKEND_AID, backendUserInfo.getAppId());
            request.setAttribute(WebConstant.JWT_BACKEND_OID, backendUserInfo.getOpenId());
            request.setAttribute(WebConstant.JWT_BACKEND_RID, backendUserInfo.getReaderType().getCode());
            request.setAttribute(WebConstant.JWT_BACKEND_PID, backendUserInfo.getDataPackage());
            request.setAttribute(WebConstant.JWT_ENV_PID, backendUserInfo.getEnvPartition());
            return true;
        } catch (Exception e) {
            log.error("平台用户身份校验失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
