package com.unipus.digitalbook.aop.log;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.WriteListener;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;

/**
 * HttpServletResponse wrapper that caches the response body.
 */
public class CachedBodyHttpServletResponse extends HttpServletResponseWrapper {

    private final ByteArrayOutputStream content = new ByteArrayOutputStream();
    private ServletOutputStream outputStream;
    private PrintWriter writer;
    private String characterEncoding;

    public CachedBodyHttpServletResponse(HttpServletResponse response) {
        super(response);
    }

    @Override
    public ServletOutputStream getOutputStream() throws IOException {
        if (writer != null) {
            throw new IllegalStateException("getWriter() has already been called on this response.");
        }

        if (outputStream == null) {
            outputStream = new TeeServletOutputStream(super.getOutputStream(), content);
        }
        return outputStream;
    }

    @Override
    public PrintWriter getWriter() throws IOException {
        if (outputStream != null) {
            throw new IllegalStateException("getOutputStream() has already been called on this response.");
        }

        if (writer == null) {
            characterEncoding = getCharacterEncoding();
            if (characterEncoding == null) {
                characterEncoding = StandardCharsets.UTF_8.name();
                setCharacterEncoding(characterEncoding);
            }
            writer = new PrintWriter(new OutputStreamWriter(new TeeServletOutputStream(super.getOutputStream(), content), characterEncoding));
        }
        return writer;
    }

    public String getContentAsString() {
        if (writer != null) {
            writer.flush();
        }
        if (characterEncoding != null) {
            return content.toString(StandardCharsets.UTF_8);
        }
        return content.toString();
    }

    private static class TeeServletOutputStream extends ServletOutputStream {

        private final ServletOutputStream original;
        private final ByteArrayOutputStream copy;

        public TeeServletOutputStream(ServletOutputStream original, ByteArrayOutputStream copy) {
            this.original = original;
            this.copy = copy;
        }

        @Override
        public void write(int b) throws IOException {
            original.write(b);
            copy.write(b);
        }

        @Override
        public boolean isReady() {
            return original.isReady();
        }

        @Override
        public void setWriteListener(WriteListener writeListener) {
            original.setWriteListener(writeListener);
        }
    }
}
