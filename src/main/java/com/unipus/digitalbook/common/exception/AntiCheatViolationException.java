package com.unipus.digitalbook.common.exception;

public class AntiCheatViolationException extends RuntimeException {
    private final String openId;
    private final String requestPath;

    public AntiCheatViolationException(String message, String openId, String requestPath) {
        super(message);
        this.openId = openId;
        this.requestPath = requestPath;
    }

    public String getOpenId() { return openId; }
    public String getRequestPath() { return requestPath; }
}
