package com.unipus.digitalbook.common.exception;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.exception.cos.COSCredentialException;
import com.unipus.digitalbook.common.exception.cos.COSException;
import com.unipus.digitalbook.common.exception.db.InsertFailedException;
import com.unipus.digitalbook.common.exception.knowledge.KnowledgeException;
import com.unipus.digitalbook.common.exception.org.OrgListEmptyException;
import com.unipus.digitalbook.common.exception.org.RedisCatchException;
import com.unipus.digitalbook.common.exception.paper.UserPaperNotPassException;
import com.unipus.digitalbook.common.exception.permission.PermissionCheckException;
import com.unipus.digitalbook.common.exception.qrcode.Cms2QrCodeException;
import com.unipus.digitalbook.common.exception.qrcode.QrCodeExportRelationalException;
import com.unipus.digitalbook.common.exception.question.GrpcQuestionInvalidResponseException;
import com.unipus.digitalbook.common.exception.soe.SoeException;
import com.unipus.digitalbook.common.exception.user.*;
import com.unipus.digitalbook.model.common.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.naming.NoPermissionException;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * 异常统一处理拦截
 */

@Slf4j
@ControllerAdvice
public class GlobalExceptionAdvice<T> {

    @ResponseBody
    @ExceptionHandler(value = IllegalArgumentException.class)
    public Response illegalArgumentHandler(IllegalArgumentException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }


    @ResponseBody
    @ExceptionHandler(value = InsertFailedException.class)
    public Response insertFailedExceptionHandler(InsertFailedException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(value = UserNotExistException.class)
    public Response userNotExistExceptionHandler(UserNotExistException e) throws Exception {
        log.error(e.getMessage(), e);
        if (StringUtils.hasText(e.getMessage())){
            return Response.fail(e.getMessage());
        }
        return Response.fail("用户信息不存在");
    }

    @ResponseBody
    @ExceptionHandler(value = UserAlreadyActivatedException.class)
    public Response userAlreadyActivatedExceptionHandler(UserAlreadyActivatedException e) throws Exception {
        log.error(e.getMessage(), e);
        if (StringUtils.hasText(e.getMessage())){
            return Response.fail(e.getMessage());
        }
        return Response.fail("用户已激活，无法操作。");
    }

    @ResponseBody
    @ExceptionHandler(value = UserAlreadyExistException.class)
    public Response userAlreadyExistExceptionHandler(UserAlreadyExistException e) throws Exception {
        log.error(e.getMessage(), e);
        if (StringUtils.hasText(e.getMessage())){
            return Response.fail(e.getMessage());
        }
        return Response.fail("该用户在机构中已存在");
    }

    /**
     * 自定义验证异常
     */
    @ResponseBody
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public Response methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) throws Exception {
        log.error(e.getMessage(), e);
        String message = Objects.requireNonNull(e.getBindingResult().getFieldError()).getDefaultMessage();
        return Response.fail(message);
    }

    @ResponseBody
    @ExceptionHandler(value = NoPermissionException.class)
    public Response noPermissionExceptionHandler(NoPermissionException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }
    @ResponseBody
    @ExceptionHandler(value = IllegalStateException.class)
    public Response illegalStateExceptionHandler(IllegalStateException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }
    @ResponseBody
    @ExceptionHandler(value = NoSuchElementException.class)
    public Response noSuchElementExceptionHandler(NoSuchElementException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(value = DuplicateRecordException.class)
    public Response duplicateRecordExceptionHandler(DuplicateRecordException e) {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 对象存储认证异常
     */
    @ResponseBody
    @ExceptionHandler(value = COSCredentialException.class)
    public Response cosCredentialExceptionHandler(COSCredentialException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 对象存储异常
     */
    @ResponseBody
    @ExceptionHandler(value = COSException.class)
    public Response cosExceptionHandler(COSException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * soe异常
     */
    @ResponseBody
    @ExceptionHandler(value = SoeException.class)
    public Response soeExceptionHandler(SoeException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 组织列表异常
     */
    @ResponseBody
    @ExceptionHandler(value = OrgListEmptyException.class)
    public Response orgExceptionHandler(OrgListEmptyException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 组织缓存异常
     */
    @ResponseBody
    @ExceptionHandler(value = RedisCatchException.class)
    public Response orgExceptionHandler(RedisCatchException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 数据权限异常
     */
    @ResponseBody
    @ExceptionHandler(value = PermissionCheckException.class)
    public Response permissionExceptionHandler(PermissionCheckException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 二维码异常
     */
    @ResponseBody
    @ExceptionHandler(value = Cms2QrCodeException.class)
    public Response cms2QrCodeExceptionHandler(Cms2QrCodeException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 二维码导出异常
     */
    @ResponseBody
    @ExceptionHandler(value = QrCodeExportRelationalException.class)
    public Response qrCodeExportRelationalExceptionHandler(QrCodeExportRelationalException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 题库返回值异常
     */
    @ResponseBody
    @ExceptionHandler(value = GrpcQuestionInvalidResponseException.class)
    public Response grpcQuestionInvalidResponseExceptionHandler(GrpcQuestionInvalidResponseException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 用户身份信息异常
     */
    @ResponseBody
    @ExceptionHandler(value = UserAuthInfoException.class)
    public Response userAuthInfoExceptionHandler(UserAuthInfoException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 用户信息导入异常
     */
    @ResponseBody
    @ExceptionHandler(value = UserImportException.class)
    public Response userImportExceptionHandler(UserImportException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getMessage());
    }

    /**
     * 业务异常
     */
    @ResponseBody
    @ExceptionHandler(value = KnowledgeException.class)
    public Response knowledgeExceptionHandler(KnowledgeException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getCode(), e.getMessage());
    }

    /**
     * 过关率检查异常
     */
    @ResponseBody
    @ExceptionHandler(value = UserPaperNotPassException.class)
    public Response userPaperNotPassException(UserPaperNotPassException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getCode(), e.getMessage());
    }

    /**
     * 业务异常
     */
    @ResponseBody
    @ExceptionHandler(value = BizException.class)
    public Response bizExceptionHandler(BizException e) throws Exception {
        log.error(e.getMessage(), e);
        return Response.fail(e.getCode(), e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public Response exceptionHandler(Exception e) throws Exception {
        log.error(e.getMessage(), e);
        //不能把错误返回给前端
        //因为可以根据报错 知道服务器信息，方便注入攻击者调试
        return Response.fail("服务器开小差去了(((o(*ﾟ▽ﾟ*)o)))");
    }
}
