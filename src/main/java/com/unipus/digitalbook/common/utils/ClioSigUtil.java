package com.unipus.digitalbook.common.utils;

import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;

import java.security.MessageDigest;

/**
 * <AUTHOR>
 * @date 2025/4/3 15:15
 */
@Slf4j
public class ClioSigUtil {
    private ClioSigUtil() {
        super();
    }

    public static final String USER_ID = "usr_ipublish_key";

    private static final String DIG_METHOD_SHA = "sha1";

    @Nullable
    public static String getClioSig(String engineKey, String engineSecret, Long ts) {
        String timestamp = ts.toString().substring(0, 10);
        String sigStr = engineKey + engineSecret + timestamp;
        return getDigest(sigStr, ClioSigUtil.DIG_METHOD_SHA);
    }

    @Nullable
    private static String getDigest(String input, String digestMethod) {
        if (null == input) {
            return null;
        }
        try {
            MessageDigest md = MessageDigest.getInstance(digestMethod);
            md.update(input.getBytes());
            byte[] b = md.digest();
            int i;
            StringBuilder buf = new StringBuilder();
            for (byte value : b) {
                i = value;
                if (i < 0) {
                    i += 256;
                }
                if (i < 16) {
                    buf.append("0");
                }
                buf.append(Integer.toHexString(i));
            }
            return buf.toString();
        } catch (Exception e) {
            log.error("获取digest异常", e);
        }
        return null;
    }
}
