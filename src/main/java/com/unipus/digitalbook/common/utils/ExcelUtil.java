package com.unipus.digitalbook.common.utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/5 16:48
 */
public class ExcelUtil {

    private static final String DEFAULT_PASSWORD = "password";

    /**
     * 向Excel工作表中添加下拉列表框
     * <p>
     * 该方法通过创建一个隐藏的工作表来存储下拉列表的数据，然后在指定的工作表中创建引用这些数据的下拉列表
     *
     * @param workbook        Excel工作簿对象，用于操作Excel文件
     * @param sheet           需要添加下拉列表的工作表对象
     * @param firstRowIndex   下拉列表开始的行索引
     * @param lastRowIndex    下拉列表结束的行索引
     * @param colIndex        下拉列表框的列索引
     * @param data            下拉列表框的数据，每个Map包含键值对，其中key和value分别表示下拉列表中显示的值和对应的隐藏值
     * @param hiddenSheetName 隐藏工作表的名称，如果未提供，则默认为"hidden"加上列索引
     * @throws IllegalArgumentException 如果工作表对象为null，则抛出此异常
     */
    public static void addDropDownList(Workbook workbook, Sheet sheet, int firstRowIndex, int lastRowIndex, int colIndex, List<Map<String, String>> data, String hiddenSheetName) {
        if (workbook == null) {
            throw new IllegalArgumentException("Workbook cannot be null");
        }
        if (sheet == null) {
            throw new IllegalArgumentException("Sheet cannot be null");
        }
        if (firstRowIndex > lastRowIndex || firstRowIndex < 0) {
            throw new IllegalArgumentException("Invalid row indices");
        }
        if (colIndex < 0) {
            throw new IllegalArgumentException("Invalid column index");
        }
        // 创建隐藏sheet，将下拉框数据放到新的sheet里，然后excle通过新的sheet数据加载下拉框数据
        if (!StringUtils.hasText(hiddenSheetName)) {
            hiddenSheetName = "hidden" + colIndex;
        }
        Sheet hiddenSheet = workbook.getSheet(hiddenSheetName);
        if (hiddenSheet == null) {
            hiddenSheet = workbook.createSheet(hiddenSheetName);
        }
        //设置编辑密码
        hiddenSheet.protectSheet(DEFAULT_PASSWORD);
        //sheet设置为隐藏,存储下拉菜单项的sheet页不显示
        workbook.setSheetHidden(workbook.getSheetIndex(hiddenSheet), true);
        //设置字体大小
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);
        cellStyle.setFont(font);
        cellStyle.setLocked(true);
        //设置文本格式
        DataFormat format = workbook.createDataFormat();
        cellStyle.setDataFormat(format.getFormat("@"));
        //设置下拉数据
        if (!ObjectUtils.isEmpty(data)) {
            for (int i = 0; i < data.size(); i++) {
                Map<String, String> dataMap = data.get(i);
                //赋值
                Row row = hiddenSheet.getRow(i);
                if (row == null) {
                    row = hiddenSheet.createRow(i);
                }
                Cell cell0 = row.getCell(0);
                if (cell0 == null) {
                    cell0 = row.createCell(0);
                }
                cell0.setCellValue(dataMap.get("key"));
                cell0.setCellStyle(cellStyle);

                Cell cell1 = row.getCell(1);
                if (cell1 == null) {
                    cell1 = row.createCell(1);
                }
                cell1.setCellValue(dataMap.get("value"));
                cell1.setCellStyle(cellStyle);
            }
        }
        // =IFERROR(VLOOKUP(@C:C,用户角色Code!A:B,2,0),"")&""
        String formula = String.format("IFERROR(VLOOKUP(%s:%s,%s!A:B,2,0),\"\")&\"\"", getExcelColIndex(colIndex), getExcelColIndex(colIndex), hiddenSheetName);
        int nextCellNum = sheet.getRow(firstRowIndex - 1).getLastCellNum();
        //设置value列名
        sheet.getRow(firstRowIndex - 1).createCell(nextCellNum).setCellValue(hiddenSheetName);
        for (int i = firstRowIndex; i <= lastRowIndex; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                row = sheet.createRow(i);
            }
            Cell cell = row.getCell(nextCellNum);
            if (cell == null) {
                cell = row.createCell(nextCellNum);
            }
            cell.setCellFormula(formula);
            cell.setCellStyle(cellStyle);
        }
        //隐藏列
        sheet.setColumnHidden(nextCellNum, true);

        // 创建名称管理器，可被其他单元格引用
        Name namedCell = workbook.createName();
        namedCell.setNameName(hiddenSheetName);
        //设置名称引用的公式, 使用像'A1：B1'这样的相对值会导致在Microsoft Excel中使用工作簿时名称所指向的单元格的意外移动，通常使用绝对引用，例如'$A$1:$B$1'可以避免这种情况。
        if (ObjectUtils.isEmpty(data)) {
            namedCell.setRefersToFormula(hiddenSheetName + "!$A$1:$A$1");
        } else {
            // =OFFSET(用户角色Code!$A$1,0,0,COUNTA(用户角色Code!$A:$A),1)
            namedCell.setRefersToFormula(hiddenSheetName + "!$A$1:$A$" + data.size());
        }
        addValidation(sheet, firstRowIndex, lastRowIndex, colIndex, hiddenSheetName);
        //进行强制公式计算
        sheet.setForceFormulaRecalculation(true);
    }

    private static void addValidation(Sheet sheet, int firstRowIndex, int lastRowIndex, int colIndex, String hiddenSheetName) {
        // 设置下拉列表的行：(开始行，终止行，开始列，终止列)
        CellRangeAddressList addressList = new CellRangeAddressList(firstRowIndex, lastRowIndex, colIndex, colIndex);
        //设置验证数据内容,将刚才设置的sheet引用到你的下拉列表中,helper 由 dataSheet 创建, nameName 中包含了隐藏sheet的引用的下拉项信息, 以此关联二者
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createFormulaListConstraint(hiddenSheetName);
        DataValidation validation = helper.createValidation(constraint, addressList);
        // 是否显示下拉框箭头
        validation.setSuppressDropDownArrow(true);
        // 单元格值和下拉项不符合时提示的值,阻止输入非下拉选项的值
        validation.setShowErrorBox(true);
        validation.setErrorStyle(DataValidation.ErrorStyle.STOP);
        validation.createErrorBox("提示", "请选择下拉选项中的内容");
        // 作用在目标sheet上,将工作表添加验证对象
        sheet.addValidationData(validation);
    }

    /**
     * 将index转为excel的索引，如 A  AA  BA
     * 列名开始时从A-Z 再次出现时为AA AB - AZ 继续重复时为BA - BZ 以此类推
     *
     * @param index 当前列的值
     * @return 当前列在Excel 中的列名
     */
    private static String getExcelColIndex(int index) {
        String result = "";
        if (index / 26 != 0) {
            char x = (char) ('A' + (index / 26 - 1));
            result += x;
        }
        char l = (char) ('A' + (index % 26));
        result += l;
        return result;
    }

    public static void addDropDownList(Workbook workbook, Sheet sheet, List<Map<String, String>> data, String hiddenSheetName) {
        if (workbook == null) {
            throw new IllegalArgumentException("Workbook cannot be null");
        }
        if (sheet == null) {
            throw new IllegalArgumentException("Sheet cannot be null");
        }
        Sheet hiddenSheet = workbook.getSheet(hiddenSheetName);
        //设置编辑密码
        hiddenSheet.protectSheet(DEFAULT_PASSWORD);
        //sheet设置为隐藏,存储下拉菜单项的sheet页不显示
        workbook.setSheetHidden(workbook.getSheetIndex(hiddenSheet), true);
        //设置字体大小
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);
        cellStyle.setFont(font);
        cellStyle.setLocked(true);
        //设置文本格式
        DataFormat format = workbook.createDataFormat();
        cellStyle.setDataFormat(format.getFormat("@"));
        //设置下拉数据
        if (!ObjectUtils.isEmpty(data)) {
            for (int i = 0; i < data.size(); i++) {
                Map<String, String> dataMap = data.get(i);
                //赋值
                Row row = hiddenSheet.getRow(i);
                if (row == null) {
                    row = hiddenSheet.createRow(i);
                }
                Cell cell0 = row.getCell(0);
                if (cell0 == null) {
                    cell0 = row.createCell(0);
                }
                cell0.setCellValue(dataMap.get("key"));
                cell0.setCellStyle(cellStyle);

                Cell cell1 = row.getCell(1);
                if (cell1 == null) {
                    cell1 = row.createCell(1);
                }
                cell1.setCellValue(dataMap.get("value"));
                cell1.setCellStyle(cellStyle);
            }
        }
        //进行强制公式计算
        sheet.setForceFormulaRecalculation(true);
    }
}
