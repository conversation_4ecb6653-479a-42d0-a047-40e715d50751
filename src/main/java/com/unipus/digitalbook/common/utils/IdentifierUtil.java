package com.unipus.digitalbook.common.utils;

import java.nio.ByteBuffer;
import java.util.Base64;
import java.util.UUID;

public class IdentifierUtil {
    public static final String DEFAULT_VERSION_NUMBER = "0";
    public static String getShortUUID() {
        UUID uuid = UUID.randomUUID();
        // 使用 ByteBuffer 转换 UUID 到 byte 数组 (更简洁)
        ByteBuffer bb = ByteBuffer.wrap(new byte[16]);
        bb.putLong(uuid.getMostSignificantBits());
        bb.putLong(uuid.getLeastSignificantBits());
        byte[] uuidBytes = bb.array();
        // Base64 编码 (保持不变，但可以考虑使用静态导入)
        return Base64.getUrlEncoder().withoutPadding().encodeToString(uuidBytes);
    }

    /**
     * 生成版本
     * @return versionNumber
     */
    public static String generateVersion() {
        // 进行Base64编码
        return Base62.encode(System.currentTimeMillis());
    }

    /**
     * 解析版本
     * @return versionNumber时间戳
     */
    public static long parseVersion(String versionNumber) {
        // 进行Base64解码
        return Base62.decode(versionNumber);
    }

    private IdentifierUtil(){
        super();
    }
}
