package com.unipus.digitalbook.common.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;

public class JsonUtil {
    public static String toJsonString(Object object) {
        if (object == null) {
            return null;
        }
        return JSON.toJSONString(object);
    }

    public static String toPrettyString(Object object) {
        if (object == null) {
            return null;
        }
        return JSON.toJSONString(object, com.alibaba.fastjson2.JSONWriter.Feature.PrettyFormat);
    }

    public static <T> T parseObject(String json, Class<T> clazz) {
        return JSON.parseObject(json, clazz);
    }

    public static <T> T parseObject(String json, TypeReference<T> typeReference) {
        return JSON.parseObject(json, typeReference);
    }
}
