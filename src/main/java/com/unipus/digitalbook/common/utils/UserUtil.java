package com.unipus.digitalbook.common.utils;

import com.unipus.digitalbook.common.exception.user.UserAuthInfoException;
import com.unipus.digitalbook.model.constants.WebConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@Component
@Slf4j
public class UserUtil {
    public static boolean isSuperAdmin(Long userId) {
        if (userId == null) {
            return false;
        }
        return 1 == userId;
    }

    public static boolean isSuperAdmin() {
        return isSuperAdmin(getCurrentUser());
    }

    public static Long getCurrentUser() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            throw new UserAuthInfoException("获取当前用户ID失败");
        }
        Object userId = requestAttributes.getRequest().getAttribute(WebConstant.JWT_USER_ID);
        if(!(userId instanceof Long)){
            throw new UserAuthInfoException("获取当前用户ID失败");
        }
        return (Long)userId;
    }

    public static String desensitization(String mobile) {
        if (mobile == null || mobile.isEmpty()) {
            return null;
        }
        return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }
}
