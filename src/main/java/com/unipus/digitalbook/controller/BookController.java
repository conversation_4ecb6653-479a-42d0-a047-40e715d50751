package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.aop.permission.DataPermission;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.BooleanDTO;
import com.unipus.digitalbook.model.dto.DataListDTO;
import com.unipus.digitalbook.model.dto.IdDTO;
import com.unipus.digitalbook.model.dto.book.*;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.book.*;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.permission.ResourceUser;
import com.unipus.digitalbook.model.enums.PermissionTypeEnum;
import com.unipus.digitalbook.model.enums.ResourceTypeEnum;
import com.unipus.digitalbook.model.params.book.*;
import com.unipus.digitalbook.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/book")
@Tag(name = "教材相关功能", description = "教材相关接口")
public class BookController extends BaseController {

    @Resource
    private BookOperationLogService bookOperationLogService;

    @Resource
    private BookService bookService;

    @Resource
    private BookPermissionService bookPermissionService;

    @Resource
    private UserService userService;

    @Resource
    private BookVersionService bookVersionService;

    @GetMapping("/getRecentBook")
    @Operation(summary = "获取用户最近打开的教材列表",
            description = "该接口用于获取用户最近打开的教材列表，返回一个包含最近打开教材信息的列表",
            method = "GET"
    )
    public Response<UserBookListDTO> getRecentBookList(BookSearchParam param) {
        UserBookList userBookList = bookService.getUserBooks(getCurrentUserId(), param, getCurrentOrgId(), 4);
        return buildUserBookListResponse(userBookList);
    }


    @GetMapping("/getMyBookStatistics")
    @Operation(summary = "获取我的教材统计列表",
            description = "获取我的教材统计列表，包括状态及相对应的数量",
            method = "GET"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功获取最近打开的教材列表"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Response<BookStatusStatisticListDTO> getMyBookStatistics() {
        // 假设这里调用服务层获取最近打开的教材列表
        // 示例中直接返回null，实际开发中需要替换为实际逻辑
        return null;
    }

    @PostMapping("/searchBookOperationLog")
    @Operation(summary = "查询教材操作日志列表", description = "查询教材操作日志列表，根据教材id，用户名称，操作时间联合查询", method = "POST")
    public Response<SearchOperationLogListDTO> searchBookOperationLog(@RequestBody SearchOperationLogParam param) {
        SearchOperationLogList bookOperationLogList = bookOperationLogService.searchOperationLogList(param.getBookId(), param.getUserName(),
                param.getOperationStartTime() != null ? new Date(param.getOperationStartTime()) : null,
                param.getOperationEndTime() != null ? new Date(param.getOperationEndTime()) : null, param.getPageParams());
        Book book = bookService.getBookById(param.getBookId());
        SearchOperationLogListDTO searchOperationLogListDTO = new SearchOperationLogListDTO(bookOperationLogList);
        searchOperationLogListDTO.setBookName(book != null ? book.getChineseName() : null);
        return Response.success(searchOperationLogListDTO);
    }

    @GetMapping("/getMyBookList")
    @Operation(summary = "获取我的教材列表", description = "获取我的教材列表，分页查询")
    public Response<UserBookListDTO> getMyBookList(BookSearchParam param) {
        UserBookList userBookList = bookService.getUserBooks(getCurrentUserId(), param, getCurrentOrgId(), null);
        return buildUserBookListResponse(userBookList);
    }

    @GetMapping("/getMyLastVersionBookList")
    @Operation(summary = "获取我的教材列表", description = "获取我的教材列表，分页查询")
    public Response<UserBookListDTO> getMyLastVersionBookList(BookSearchParam param) {
        UserBookList userBookList = bookService.getMyLastVersionBookList(getCurrentUserId(), param, getCurrentOrgId());
        return buildUserBookListResponse(userBookList);
    }

    @GetMapping("/getMyPreviewBookList")
    @Operation(summary = "获取我的预览教材列表", description = "获取我的预览教材列表，分页查询")
    public Response<UserBookListDTO> getMyPreviewBookList(BookSearchParam param) {
        UserBookList userBookList = bookService.getUserPreviewBooks(getCurrentUserId(), param);
        if (userBookList == null || CollectionUtils.isEmpty(userBookList.getUserBooks())) {
            return Response.success(new UserBookListDTO());
        }
        return Response.success(new UserBookListDTO(userBookList, userService));
    }

    @GetMapping("/getBookDetail")
    @Operation(summary = "获取教材详情", description = "获取教材详情，根据教材id获取教材详情")
    public Response<BookDetailDTO> getBookDetail(String bookId) {
        if (StringUtils.isBlank(bookId)) {
            return Response.fail("教材ID不能为空");
        }
        Book book = bookService.getBookById(bookId);
        if (book == null) {
            return Response.fail("教材不存在");
        }
        Map<Long, UserInfo> userMap = userService.getUserMap(List.of(book.getEditorId(), book.getCreateBy()));
        Map<String, List<ResourceUser>> bookCollaborators = bookService.getBookCollaborators(List.of(bookId), getCurrentOrgId());
        Map<String, Long> bookVersionMap = bookVersionService.bookMaxPublishedTime(List.of(bookId));
        return Response.success(new BookDetailDTO(book, userMap).fillCollaborators(bookCollaborators.get(bookId)).fillPublishTimeAndFlag(bookVersionMap));
    }

    @GetMapping("/getBookCoverIntro")
    @Operation(summary = "根据教材id获取教材封面和简介信息", description = "获取教材封面和简介信息，根据教材id查询")
    public Response<BookCoverIntroDTO> getBookCoverIntro(@RequestParam("bookId") String bookId) {
        Book book = bookService.getBookCoverIntro(bookId);
        return Response.success(new BookCoverIntroDTO(book));
    }

    @PostMapping("/editBookCoverIntro")
    @Operation(summary = "编辑教材封面和简介信息", description = "编辑教材封面和简介信息")
    @DataPermission(resourceType = ResourceTypeEnum.BOOK, permissionTypes = PermissionTypeEnum.OWNER, resourceId = "#param.bookId")
    public Response<Boolean> editBookCoverIntro(@RequestBody BookCoverIntroParam param) {
        Boolean result = bookService.editBookCoverIntro(getCurrentUserId(), param.toEntity());
        return Boolean.TRUE.equals(result) ? Response.success("编辑成功", true) : Response.fail();
    }

    @PostMapping("/addBook")
    @Operation(summary = "添加教材", description = "添加教材，返回教材ID")
    public Response<IdDTO<String>> addBook(@RequestBody AddBookParam param) {
        String bookId = bookService.addBook(param.toEntity(getCurrentOrgId()), getCurrentUserId());
        return Response.success(new IdDTO<>(bookId));
    }

    @PostMapping("/editBook")
    @Operation(summary = "编辑教材基本信息", description = "编辑教材基本信息")
    @DataPermission(resourceType = ResourceTypeEnum.BOOK, permissionTypes = PermissionTypeEnum.OWNER, resourceId = "#param.bookId")
    public Response<Boolean> editBook(@RequestBody EditBookParam param) {
        boolean result = bookService.editBook(param.toEntity(getCurrentUserId()));
        return Response.success("编辑成功", result);
    }

    @PostMapping("/addChapterToBook")
    @Operation(summary = "添加教材的章节", description = "添加教材的章节")
    @DataPermission(resourceType = ResourceTypeEnum.BOOK, permissionTypes = PermissionTypeEnum.OWNER, resourceId = "#param.bookId")
    public Response<IdDTO<String>> addChapter(@RequestBody AddChapterParam param) {
        String bookId = bookService.addChapter(param.toEntity(), getCurrentUserId());
        if (StringUtils.isEmpty(bookId)){
            return Response.fail("新增章节失败。");
        }
        return Response.success("章节创建成功",new IdDTO<>(bookId));
    }

    @PostMapping("/sortChapterInBook")
    @Operation(summary = "编辑教材中章节的顺序", description = "编辑教材中章节的顺序")
    @DataPermission(resourceType = ResourceTypeEnum.BOOK, permissionTypes = PermissionTypeEnum.OWNER, resourceId = "#param.bookId")
    public Response<IdDTO<String>> sortChapterInBook(@RequestBody SortChapterParam param) {
        List<Chapter> chapterList = param.getChapterList().stream().map(SortChapterParam.ChapterSort::toEntity).toList();
        Boolean res = bookService.sortChapterListInBook(param.getBookId(), chapterList, getCurrentUserId());
        if (Boolean.FALSE.equals(res)){
            return Response.fail("排序失败。");
        }
        return Response.success();
    }

    @PostMapping("/getNonCollaboratorUser")
    @Operation(summary = "查询当前组织当前教材非协作者(用于权限分配)", description = "查询当前组织当前教材非协作者(用于权限分配)")
    public Response<BookUserListDTO> getNonCollaboratorUser(@RequestBody CollaboratorUserParam param) {
        return Response.success(BookUserListDTO.build(
                bookPermissionService.getNonCollaboratorUser(param, getCurrentOrgId(), getCurrentUserId())));
    }

    @PostMapping("/getChapterCollaborator")
    @Operation(summary = "查询章节的协作者(用于权限分配)", description = "查询教材的协作者(用于权限分配)")
    public Response<BookUserListDTO> getChapterCollaborator(@RequestBody CollaboratorUserParam param) {
        return Response.success(BookUserListDTO.build(
                bookPermissionService.getBookCollaborator(param, getCurrentOrgId(), getCurrentUserId())));
    }

    @PostMapping("/getBatchChapterCollaborator")
    @Operation(summary = "查询章节的协作者列表", description = "查询章节的协作者列表")
    public Response<ChapterCollaboratorDTO> getBatchChapterCollaborator(@RequestBody ChapterCollaboratorParam param) {
        // bookPermissionService.getBookCollaborator(param, getCurrentOrgId(), getCurrentUserId()))
        Map<String, List<ResourceUser>> chapterEditorMap = bookService.getChapterCollaborators(param.getChapterIds(), getCurrentOrgId());
        return Response.success(new ChapterCollaboratorDTO(chapterEditorMap));
    }

    @PostMapping("/updateBookCollaborator")
    @Operation(summary = "设置教材的协作者(用于权限分配)", description = "设置教材的协作者(用于权限分配)")
    public Response<Boolean> updateBookCollaborator(@RequestBody BookCollaboratorParam param) {
        Boolean result = bookPermissionService.updateBookCollaborator(param, getCurrentUserId());
        return Response.success(Boolean.TRUE.equals(result) ? "分配协作人成功" : "分配协作人失败" ,result);
    }

    @GetMapping("/getByBookName")
    @Operation(summary = "根据教材名称获取教材列表", description = "根据教材名称获取教材列表")
    public Response<DataListDTO<BookNameDTO>> getByBookName(@RequestParam(value = "bookName", required = false) String bookName) {
        List<Book> bookList = bookService.getByBookName(bookName);
        return Response.success(new DataListDTO<>(bookList.stream().map(BookNameDTO::new).toList()));
    }

    @PostMapping("/editBookCopyright")
    @Operation(summary = "编辑版权信息", description = "编辑版权信息")
    public Response<BooleanDTO> editBookCopyright(@RequestBody EditBookCopyrightParam param) {
        boolean edited = bookService.editBookCopyright(param.toEntity(getCurrentUserId()));
        return Response.success("版权信息保存成功", new BooleanDTO(edited));
    }

    @GetMapping("/getBookCopyright")
    @Operation(summary = "获取版权信息", description = "获取版权信息")
    public Response<BookCopyrightDTO> editBookCopyright(String bookId) {
        // 获取书的信息
        BookBasic bookBasic = bookService.getBookBasicByBookId(bookId);
        if (bookBasic == null) {
            throw new IllegalArgumentException("教材不存在");
        }
        BookCopyright bookCopyright = bookService.getBookCopyright(bookId);
        return Response.success("版权信息保存成功", new BookCopyrightDTO(bookCopyright, bookBasic));
    }

    private Response<UserBookListDTO> buildUserBookListResponse(UserBookList userBookList) {
        if (userBookList == null || CollectionUtils.isEmpty(userBookList.getUserBooks())) {
            return Response.success(new UserBookListDTO());
        }
        List<String> bookIds = userBookList.getUserBooks().stream().map(UserBook::getBookId).toList();
        Map<String, List<ResourceUser>> bookEditorMap = bookService.getBookCollaborators(bookIds, getCurrentOrgId());
        Map<String, Boolean> publishFlagMap = bookVersionService.isBookPublishedVersion(bookIds);
        List<String> publishBookIds = publishFlagMap.entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).toList();
        Map<String, Long> publishTimeMap = new HashMap<>();
        if (!publishBookIds.isEmpty()){
            publishTimeMap = bookVersionService.bookMaxPublishedTime(publishBookIds);
        }

        return Response.success(new UserBookListDTO(userBookList, userService)
                .fillCollaborators(bookEditorMap).fillPublishFlag(publishFlagMap).fillPublishTime(publishTimeMap));
    }
}
