package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.common.utils.COSUtil;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.cos.COSCredential;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/cos")
@Tag(name = "COS相关功能", description = "COS相关接口")
public class COSController {

    @Resource
    private COSUtil cosUtil;

    @GetMapping("getCredential")
    @Operation(summary = "临时密钥", description = "临时密钥", method = "GET")
    public Response<COSCredential> getCredential() {
        String[] actions = {
                //简单上传操作
                "name/cos:PutObject",
                //表单上传对象
                "name/cos:PostObject",
                //分块上传：初始化分块操作
                "name/cos:InitiateMultipartUpload",
                //分块上传：List 进行中的分块上传
                "name/cos:ListMultipartUploads",
                //分块上传：List 已上传分块操作
                "name/cos:ListParts",
                //分块上传：上传分块操作
                "name/cos:UploadPart",
                //分块上传：完成所有分块上传操作
                "name/cos:CompleteMultipartUpload",
                //取消分块上传操作
                "name/cos:AbortMultipartUpload"
        };
        return Response.success(cosUtil.getCredential(actions));
    }

    @GetMapping("getCICredential")
    @Operation(summary = "数据万象临时密钥", description = "数据万象临时密钥", method = "GET")
    public Response<COSCredential> getCICredential() {
        String[] actions = {
                // 处理相关接口一般为数据万象产品 权限中以ci开头
                // 提交文档处理任务
                "ci:CreateDocProcessJobs"
        };
        return Response.success(cosUtil.getCredential(actions));
    }

    @GetMapping("getPresignedUrlByUrl")
    @Operation(summary = "获取预签名URL", description = "获取预签名URL", method = "GET")
    public Response<String> getPresignedUrlByUrl(@RequestParam String url) {
        return Response.success(cosUtil.getPresignedUrlByUrl(url, null));
    }

    @GetMapping("getDownloadContentByUrl")
    @Operation(summary = "根据URL获取文件内容", description = "根据URL获取文件内容", method = "GET")
    public Response<String> getDownloadContentByUrl(@RequestParam String url) {
        return Response.success(cosUtil.getDownloadContentByUrl(url));
    }
}
