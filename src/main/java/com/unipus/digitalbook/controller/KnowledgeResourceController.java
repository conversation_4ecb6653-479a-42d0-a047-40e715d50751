package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.DataListDTO;
import com.unipus.digitalbook.model.dto.knowledge.KnowledgeSourceCheckDTO;
import com.unipus.digitalbook.model.params.knowledge.*;
import com.unipus.digitalbook.service.KnowledgeResourceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/knowledge/resource")
@Tag(name = "知识图谱子图关系相关功能", description = "知识图谱子图关系相关功能")
public class KnowledgeResourceController extends BaseController {
    @Resource
    KnowledgeResourceService knowledgeResourceService;

    @PostMapping("/add")
    @Operation(summary = "新增知识图谱资源挂载知识",
            description = "新增知识图谱资源挂载知识",
            method = "Post"
    )
    public Response<String> knowledgeSourceAdd(@RequestBody KnowledgeSourceAddParam param) {
        return Response.success(knowledgeResourceService.knowledgeSourceAdd(param, getCurrentUserId()));
    }

    @PostMapping("/update")
    @Operation(summary = "更新资源信息", description = "更新资源信息")
    public Response update(@RequestBody KnowledgeSourceUpdateParam param) {
        knowledgeResourceService.knowledgeSourceUpdate(param, getCurrentUserId());
        return Response.success();
    }

    @PostMapping("/disable")
    @Operation(summary = "失效资源信息", description = "失效资源信息")
    public Response disable(@RequestBody KnowledgeSourceIdParam param) {
        knowledgeResourceService.knowledgeSourceDisable(param, getCurrentUserId());
        return Response.success();
    }

    @PostMapping("/dir/update")
    @Operation(summary = "更新资源目录信息", description = "更新资源目录信息")
    public Response knowledgeSourceDirUpdate(@RequestBody KnowledgeSourceDirUpdateParam param) {
        knowledgeResourceService.knowledgeSourceDirUpdate(param, getCurrentUserId());
        return Response.success();
    }

    /**
     * 删除关系
     *
     * @param param
     * @return 基础响应
     */
    @PostMapping("/delete")
    @Operation(summary = "删除资源信息", description = "删除资源信息")
    public Response delete(@RequestBody KnowledgeSourceDeleteParam param) {
        knowledgeResourceService.knowledgeSourceDelete(param, getCurrentUserId());
        return Response.success();
    }
    /**
     * 删除关系
     *
     * @param param
     * @return 基础响应
     */
    @PostMapping("/batchDelete")
    @Operation(summary = "批量删除资源信息", description = "批量删除资源信息")
    public Response delete(@RequestBody KnowledgeSourceBatchDeleteParam param) {
        knowledgeResourceService.knowledgeSourceBatchDelete(param, getCurrentUserId());
        return Response.success();
    }

    /**
     * 查询基本数据
     *
     * @param param 关系ID
     * @return 基础响应
     */
    @PostMapping("/query")
    @Operation(summary = "查询资源信息", description = "查询资源信息")
    public Response query(@RequestBody KnowledgeSourceQueryParam param) {
        return Response.success(new DataListDTO<>(knowledgeResourceService.knowledgeSourceQuery(param, getCurrentUserId())));
    }

    /**
     * 查询分组数据
     *
     * @param param 关系ID
     * @return 基础响应
     */
    @PostMapping("/groupQuery")
    @Operation(summary = "查询分组资源信息", description = "查询分组资源信息")
    public Response groupQuery(@RequestBody KnowledgeSourceQueryGroupParam param) {
        return Response.success(new DataListDTO<>(knowledgeResourceService.knowledgeSourceQueryGroup(param, getCurrentUserId())));
    }

    /**
     * 删除关系
     *
     * @param param 关系ID
     * @return 基础响应
     */
    @PostMapping("/check")
    @Operation(summary = "检查资源是否重复", description = "检查资源是否重复")
    public Response<KnowledgeSourceCheckDTO> check(@RequestBody KnowledgeSourceCheckParam param) {
        return Response.success(knowledgeResourceService.check(param, getCurrentUserId()));
    }
}
