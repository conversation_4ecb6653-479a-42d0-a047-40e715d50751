package com.unipus.digitalbook.controller.backend;

import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.params.knowledge.KnowledgeSourceDeleteByThirdIdsParam;
import com.unipus.digitalbook.service.KnowledgeResourceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/backend/knowledge")
@Tag(name = "教材知识图谱相关功能", description = "为了权限控制，该控制器特意未后端服务提供接口")
public class BEKnowledgeController extends BaseController {

    @Resource
    private KnowledgeResourceService knowledgeResourceService;

    /**
     * 删除三方资源的关系
     *
     * @param param
     * @return 基础响应
     */
    @PostMapping("/resource/batchDelete")
    @Tag(name = "教材标注资源批量删除", description = "教材标注资源批量删除")
    @Operation(summary = "三方知识点删除，孤立教材资源的标注信息，也需要批量删除", description = "删除三方资源相关信息")
    public Response sourceDeleteByThirdIds(@RequestBody KnowledgeSourceDeleteByThirdIdsParam param) {
        knowledgeResourceService.knowledgeSourceDeleteByThirdIds(param, getCurrentUserId());
        return Response.success();
    }

}
