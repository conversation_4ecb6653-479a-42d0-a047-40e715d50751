package com.unipus.digitalbook.controller.reader;

import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.book.BookBasicInfoDTO;
import com.unipus.digitalbook.model.dto.chapter.ChapterCatalogListDTO;
import com.unipus.digitalbook.model.dto.chapter.ChapterWithContentDTO;
import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.entity.book.BookBasic;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.enums.ReaderTypeEnum;
import com.unipus.digitalbook.service.BookService;
import com.unipus.digitalbook.service.BookVersionService;
import com.unipus.digitalbook.service.ChapterService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("reader/book")
@Tag(name = "读者学习教材相关接口", description = "读者学习教材相关接口")
public class ReaderBookController extends BaseController {
    @Resource
    private BookService bookService;

    @GetMapping("/getBookBasicInfo")
    @Tag(name = "获取教材的基本信息", description = "获取教材的基本信息")
    public Response<BookBasicInfoDTO> getBookBasicInfo(String bookId, String bookVersionNumber) {
        Book book = bookService.getBookBasicInfoByIdAndVersion(bookId, bookVersionNumber);
        return Response.success(new BookBasicInfoDTO(book, Collections.emptyMap()));
    }
}
