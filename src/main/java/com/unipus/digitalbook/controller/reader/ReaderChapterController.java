package com.unipus.digitalbook.controller.reader;

import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.chapter.ChapterCatalogListDTO;
import com.unipus.digitalbook.model.dto.chapter.ChapterDTO;
import com.unipus.digitalbook.model.dto.chapter.ChapterListDTO;
import com.unipus.digitalbook.model.dto.chapter.ChapterResourceListDTO;
import com.unipus.digitalbook.model.dto.chapter.ChapterWithContentDTO;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.enums.ReaderTypeEnum;
import com.unipus.digitalbook.service.BookVersionService;
import com.unipus.digitalbook.service.ChapterService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("reader/chapter")
@Tag(name = "读者学习教材章节相关接口", description = "读者学习教材章节相关接口")
public class ReaderChapterController extends BaseController {
    @Resource
    private ChapterService chapterService;

    @Resource
    private BookVersionService bookVersionService;

    @GetMapping("/getChapterList")
    @Tag(name = "获取教材章节列表", description = "获取教材章节列表")
    public Response<ChapterListDTO> getChapterList(String bookId, String bookVersionNumber) {
        ReaderTypeEnum readerType = getReaderType();
        if (readerType == null) {
            return Response.fail("用户信息校验失败");
        }
        BookVersion bookVersionByBookIdAndVersion = bookVersionService.getBookVersionByBookIdAndVersion(bookId, bookVersionNumber);
        Long bookVersionId = bookVersionByBookIdAndVersion.getId();
        List<Chapter> chapters = chapterService.getChapterNamesByBookVersionId(bookVersionId);
        if (CollectionUtils.isEmpty(chapters)) {
            return Response.success(new ChapterListDTO());
        }
        return Response.success(new ChapterListDTO(chapters));
    }

    @GetMapping("/getChapterInfo")
    @Tag(name = "获取教材章节信息", description = "获取教材章节信息")
    public Response<ChapterWithContentDTO> getChapterInfo(String bookId, String bookVersionNumber, String chapterId) {
        ReaderTypeEnum readerType = getReaderType();
        if (readerType == null) {
            return Response.fail("用户信息校验失败");
        }
        BookVersion bookVersionByBookIdAndVersion = bookVersionService.getBookVersionByBookIdAndVersion(bookId, bookVersionNumber);
        if (bookVersionByBookIdAndVersion == null) {
            return Response.fail("教材版本不存在");
        }
        Long bookVersionId = bookVersionByBookIdAndVersion.getId();
        Chapter chapter = chapterService.getChapterByBookVersionIdAndChapterId(bookVersionId, chapterId);
        if (chapter == null) {
            return Response.fail("章节内容不存在");
        }
        ChapterWithContentDTO chapterWithContent = new ChapterWithContentDTO();
        chapterWithContent.fillChapterInfo(chapter);
        chapterWithContent.fillVersionInfoWithReaderType(chapter.getChapterVersion(), readerType);
        return Response.success(chapterWithContent);
    }

    @GetMapping("/getBookResource")
    @Tag(name = "获取教材资源信息", description = "获取教材资源信息")
    public Response<ChapterResourceListDTO> getBookResource(String bookId, String bookVersionNumber) {
        ReaderTypeEnum readerType = getReaderType();
        if (readerType == null) {
            return Response.fail("用户信息校验失败");
        }
        BookVersion bookVersionByBookIdAndVersion = bookVersionService.getBookVersionByBookIdAndVersion(bookId, bookVersionNumber);
        Long bookVersionId = bookVersionByBookIdAndVersion.getId();
        if (bookVersionId == null) {
            return Response.fail("教材版本不存在");
        }
        List<ChapterVersion> chapterVersions = chapterService.getResourceByBookVersionId(bookVersionId);
        return Response.success(new ChapterResourceListDTO(bookId, chapterVersions));
    }
    @GetMapping("/getBookCatalog")
    @Tag(name = "获取教材的目录列表", description = "获取教材的目录列表")
    public Response<ChapterCatalogListDTO> getBookCatalog(String bookId, String bookVersionNumber) {
        ReaderTypeEnum readerType = getReaderType();
        if (readerType == null) {
            return Response.fail("用户信息校验失败");
        }
        BookVersion bookVersionByBookIdAndVersion = bookVersionService.getBookVersionByBookIdAndVersion(bookId, bookVersionNumber);
        Long bookVersionId = bookVersionByBookIdAndVersion.getId();
        if (bookVersionId == null) {
            return Response.fail("教材版本不存在");
        }
        List<ChapterVersion> catalogByBookVersionId = chapterService.getCatalogByBookVersionId(bookVersionId);
        return Response.success(new ChapterCatalogListDTO(bookId, catalogByBookVersionId));
    }

}
