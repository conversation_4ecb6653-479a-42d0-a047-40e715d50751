package com.unipus.digitalbook.controller.reader;

import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.content.CopyCustomContentResultDTO;
import com.unipus.digitalbook.model.dto.content.CustomContentDTO;
import com.unipus.digitalbook.model.dto.content.CustomContentListDTO;
import com.unipus.digitalbook.model.entity.ThirdPartyUserInfo;
import com.unipus.digitalbook.model.entity.content.CustomContent;
import com.unipus.digitalbook.model.params.content.*;
import com.unipus.digitalbook.service.CustomContentService;
import com.unipus.digitalbook.service.ThirdPartyUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("reader/customContent")
@Tag(name = "教材自建内容相关功能", description = "教材自建内容相关功能接口")
public class ReaderCustomContentController extends BaseController {

    @Resource
    private CustomContentService customContentService;
    @Resource
    private ThirdPartyUserService thirdPartyUserService;

    @PostMapping("/saveCustomContent")
    @Operation(summary = "保存编写中的自建内容信息", description = "保存编写中的自建内容信息")
    public Response<Boolean> saveCustomContent(@RequestBody CustomContentParam param) {
        CustomContent customContent = param.toEntity(getTenantId(), getOpenId(), getDataPackage(), getThirdPartyUserId());
        Boolean result = customContentService.saveCustomContent(customContent);
        return Response.success("保存自建内容信息成功", result);
    }

    @PostMapping("/saveBatchCustomContent")
    @Operation(summary = "批量保存编写中的自建内容信息", description = "批量保存编写中的自建内容信息")
    public Response<Boolean> saveBatchCustomContent(@RequestBody CustomContentListParam param) {
        Long userId = getThirdPartyUserId();

        List<CustomContent> customContentList = param.getCustomContentList().stream()
                .map(customContentParam -> customContentParam.toEntity(getTenantId(), getOpenId(), getDataPackage(), userId))
                .toList();
        Boolean result = customContentService.saveBatchCustomContent(customContentList);
        return Response.success("批量保存自建内容信息成功", result);
    }

    @PostMapping("/updateCustomContentName")
    @Operation(summary = "更新编写中的自建内容名称", description = "更新编写中的自建内容名称")
    public Response<Boolean> updateCustomContentName(@RequestBody CustomContentNameParam param) {
        CustomContent customContent = param.toEntity(getTenantId(), getOpenId(), getDataPackage(), getThirdPartyUserId());
        Boolean result = customContentService.updateCustomContentName(customContent);
        return Response.success("更新自建内容名称成功", result);
    }

    @PostMapping("/getEditingCustomContentByBizId")
    @Operation(summary = "通过自建内容业务id获取编写中的自建内容信息", description = "通过自建内容业务id获取编写中的自建内容信息")
    public Response<CustomContentDTO> getEditingCustomContentByBizId(@RequestParam String bizId) {
        CustomContent customContent = customContentService.getEditingCustomContentByBizId(bizId, getTenantId());
        CustomContentDTO dto = new CustomContentDTO(customContent);
        return Response.success("获取自建内容信息成功", dto);
    }

    @PostMapping("/getEditingCustomContentByBizIds")
    @Operation(summary = "通过自建内容业务id批量获取编写中的自建内容信息", description = "通过自建内容业务id批量获取编写中的自建内容信息")
    public Response<CustomContentListDTO> getEditingCustomContentByBizIds(@RequestBody CustomContentIdParam param) {
        List<CustomContent> customContentList = customContentService.getEditingCustomContentByBizIds(param.getBizIds(), getTenantId());
        return Response.success(new CustomContentListDTO(customContentList));
    }

    @PostMapping("/getPublishedCustomContentByBizId")
    @Operation(summary = "通过自建内容业务id获取已发布的自建内容信息", description = "通过自建内容业务id获取已发布的自建内容信息")
    public Response<CustomContentDTO> getPublishedCustomContentByBizId(@RequestParam String bizId) {
        CustomContent customContent = customContentService.getPublishedCustomContentByBizId(bizId, getTenantId());
        CustomContentDTO dto = new CustomContentDTO(customContent);
        return Response.success("获取自建内容信息成功", dto);
    }

    @PostMapping("/getPublishedCustomContentByBizIds")
    @Operation(summary = "通过自建内容业务id批量获取已发布的自建内容信息", description = "通过自建内容业务id批量获取已发布的自建内容信息")
    public Response<CustomContentListDTO> getPublishedCustomContentByBizIds(@RequestBody CustomContentIdParam param) {
        List<CustomContent> customContentList = customContentService.getPublishedCustomContentByBizIds(param.getBizIds(), getTenantId());
        return Response.success(new CustomContentListDTO(customContentList));
    }

    @PostMapping("/deleteCustomContentByBizIds")
    @Operation(summary = "通过自建内容业务id批量删除编写中的自建内容信息", description = "通过自建内容业务id批量删除编写中的自建内容信息")
    public Response<Boolean> deleteCustomContentByBizIds(@RequestBody CustomContentIdParam param) {
        Boolean result = customContentService.deleteCustomContentByBizIds(param.getBizIds(), getTenantId(), getThirdPartyUserId());
        return Response.success("批量删除自建内容信息成功", result);
    }

    @PostMapping("/publishCustomContent")
    @Operation(summary = "发布自建内容信息", description = "发布自建内容信息")
    public Response<Boolean> publishCustomContent(@RequestBody PublishCustomContentParam param) {
        Boolean result = customContentService.publishCustomContent(param.getPublishContentIds(), param.getDeleteContentIds(), getTenantId(), getThirdPartyUserId());
        return Response.success("发布自建内容信息成功", result);
    }

    @PostMapping("/copyPublishedCustomContentByBizIds")
    @Operation(summary = "复制已发布的自建内容", description = "复制已发布的自建内容为编写中状态")
    public Response<CopyCustomContentResultDTO> copyPublishedCustomContentByBizIds(@RequestBody CustomContentIdParam param) {
        Map<String, String> result = customContentService.copyPublishedCustomContentByBizIds(param.getBizIds(), getTenantId(), getThirdPartyUserId());
        return Response.success("复制已发布自建内容成功", new CopyCustomContentResultDTO(result));
    }

    /**
     * 通过getTenantId和getOpenId获取第三方用户id
     *
     * @return 第三方用户ID
     */
    public Long getThirdPartyUserId() {
        ThirdPartyUserInfo userInfo = thirdPartyUserService.getUserInfoByOpenId(getTenantId(), getOpenId());
        return Optional.ofNullable(userInfo).map(ThirdPartyUserInfo::getId).orElse(null);
    }
}
