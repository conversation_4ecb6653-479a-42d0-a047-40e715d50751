package com.unipus.digitalbook.controller.reader;

import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.process.ReadProgressListDTO;
import com.unipus.digitalbook.model.entity.action.ReadProgressList;
import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import com.unipus.digitalbook.model.params.UserAction.UserActionParam;
import com.unipus.digitalbook.service.ChapterService;
import com.unipus.digitalbook.service.UserActionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RequestMapping("reader/action")
@RestController
@Slf4j
@Tag(name = "读者行为接口相关功能", description = "行为接口相关接口")
public class UserActionController extends BaseController {

    @Resource
    private UserActionService userActionService;

    @Resource
    private ChapterService chapterService;

    @PostMapping("finishNode")
    public Response<Boolean> finishNode(@RequestBody UserActionParam param) {
        Long chapterVersionId = chapterService.getChapterVersionIdByChapterIdAndVersionNumber(param.getChapterId(), param.getChapterVersionNumber());
        if (chapterVersionId == null) {
            log.error("章节版本不存在 {}, {}", param.getChapterId(), param.getChapterVersionNumber());
            return Response.fail("章节版本不存在");
        }
        Map<String, ChapterNode> nodeMap = chapterService.getNodeMapByChapterVersionId(chapterVersionId);
        ChapterNode chapterNode = nodeMap.get(param.getNodeId());
        if (chapterNode == null) {
            log.error("节点可能已变更 {}", param.getNodeId());
            return Response.fail("节点可能已变更");
        }
        UserAction userAction = param.toEntity(getTenantId(), getOpenId(), chapterVersionId, chapterNode, getDataPackage(), getClientIp());
        return userActionService.finishNode(userAction);
    }

    @GetMapping("getChapterProgress")
    @Operation(summary = "获取章节全部进度", description = "获取章节全部进度")
    public Response<ReadProgressListDTO> getChapterProgress(String chapterId, String chapterVersionNumber) {
        if  (!StringUtils.hasText(chapterId)) {
            return Response.fail("chapterId不能为空");
        }
        if  (!StringUtils.hasText(chapterVersionNumber)) {
            return Response.fail("chapterVersionNumber不能为空");
        }
        Long chapterVersionId = chapterService.getChapterVersionIdByChapterIdAndVersionNumber(chapterId, chapterVersionNumber);
        if (chapterVersionId == null) {
            return Response.fail("章节版本不存在");
        }
        Response<ReadProgressList> chapterProgress = userActionService.getChapterProgress(getTenantId(), getOpenId(), chapterVersionId);
        if (!chapterProgress.isSuccess()) {
            return Response.fail(chapterProgress.getMessage());
        }
        return Response.success(new ReadProgressListDTO(chapterProgress.getData()));
    }

    @GetMapping("getNodeProgress")
    @Operation(summary = "获取节点进度", description = "获取节点进度")
    public Response<Boolean> getNodeProgress(String chapterId, String chapterVersionNumber, String nodeId) {
        if  (!StringUtils.hasText(chapterId)) {
            return Response.fail("chapterId不能为空");
        }
        if  (!StringUtils.hasText(chapterVersionNumber)) {
            return Response.fail("chapterVersionNumber不能为空");
        }
        if  (!StringUtils.hasText(nodeId)) {
            return Response.fail("nodeId不能为空");
        }
        Long chapterVersionId = chapterService.getChapterVersionIdByChapterIdAndVersionNumber(chapterId, chapterVersionNumber);
        if (chapterVersionId == null) {
            return Response.fail("章节版本不存在");
        }
        return userActionService.getNodeProgress(getTenantId(), getOpenId(), chapterVersionId, nodeId);
    }
}
