package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoExistPO;
import com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【course_knowledge_source_info】的数据库操作Mapper
 * @createDate 2025-06-04 17:19:35
 * @Entity com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfo
 */
public interface CourseKnowledgeSourceInfoMapper {

    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyList(List<Long> idList);

    int insert(CourseKnowledgeSourceInfoPO record);

    int insertSelective(CourseKnowledgeSourceInfoPO record);

    int batchInsertSelective(List<CourseKnowledgeSourceInfoPO> records);

    CourseKnowledgeSourceInfoPO selectByPrimaryKey(Long id);

    List<CourseKnowledgeSourceInfoPO> selectSelective(CourseKnowledgeSourceInfoPO record);

    List<CourseKnowledgeSourceInfoPO> selectExistSourceUrls(CourseKnowledgeSourceInfoExistPO existRecord);

    List<CourseKnowledgeSourceInfoPO> selectAllSourceInfoById(Long id);

    int deleteAllSourceInfoById(CourseKnowledgeSourceInfoPO record);

    int deleteAllSourceInfoByThirdIds(@Param("thirdIds") List<Long> thirdIds,
                                      @Param("userId") Long userId,
                                      @Param("knowledgeId") String knowledgeId);

    int updateEnableStatusAllSourceInfoById(CourseKnowledgeSourceInfoPO record);

    int updateDirAllSourceInfoById(CourseKnowledgeSourceInfoPO record);

    int updateByPrimaryKeySelective(CourseKnowledgeSourceInfoPO record);

    int updateByPrimaryKey(CourseKnowledgeSourceInfoPO record);

}
