package com.unipus.digitalbook.dao;

import com.unipus.digitalbook.model.po.UserCustomContentProgressPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2025/6/25 10:18
 */
public interface UserCustomContentProgressPOMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(UserCustomContentProgressPO record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    UserCustomContentProgressPO selectByPrimaryKey(Long id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UserCustomContentProgressPO record);

    int updateBatchSelective(@Param("list") List<UserCustomContentProgressPO> list);
}