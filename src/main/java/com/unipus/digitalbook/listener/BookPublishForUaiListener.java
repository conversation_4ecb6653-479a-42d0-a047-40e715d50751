package com.unipus.digitalbook.listener;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.events.UaiResourcePublishEvent;
import com.unipus.digitalbook.service.UserActionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * UAI教材发布后监听消息
 */
@Slf4j
@Component
@ConditionalOnProperty(value = "kafka.consumer.enable", havingValue = "true", matchIfMissing = true)
public class BookPublishForUaiListener {
    @Resource
    private UserActionService userActionService;

    @KafkaListener(topics = "${kafka.topic.uaiResourcePublish}")
    public void processMessage(String message) {
        log.info("received uai resource publish message: {}", message);
        UaiResourcePublishEvent uaiResourcePublishEvent = JsonUtil.parseObject(message, UaiResourcePublishEvent.class);
        userActionService.migratedProgress(1L, uaiResourcePublishEvent.getResourceId(), uaiResourcePublishEvent.getVersion());
    }
}
