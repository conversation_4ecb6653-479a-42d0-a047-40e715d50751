package com.unipus.digitalbook.listener;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.dao.UserChapterProgressPOMapper;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.events.UserActionEvent;
import com.unipus.digitalbook.service.BookVersionService;
import com.unipus.digitalbook.service.QuestionAnswerPushService;
import com.unipus.digitalbook.service.UserActionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
@ConditionalOnProperty(value = "kafka.consumer.enable",havingValue = "true", matchIfMissing = true)
public class UserActionListener {
    @Resource
    private UserChapterProgressPOMapper userChapterProgressPOMapper;
    @Resource
    private UserActionService userActionService;
    @Resource
    private QuestionAnswerPushService questionAnswerPushService;

    @Resource
    private BookVersionService bookVersionService;

    @KafkaListener(topics = "${kafka.topic.userAction}")
    public void processMessage(String message) {
        log.info("received user action message: {}", message);
        UserActionEvent userActionEvent = JSON.parseObject(message, UserActionEvent.class);
        Response<byte[]> progress = userActionService.getNodeProgressValueFromCache(
                userActionEvent.getTenantId(),
                userActionEvent.getOpenId(),
                userActionEvent.getChapterVersionId());
        // 持久化
        userChapterProgressPOMapper.saveProgressBit(
                userActionEvent.getTenantId(),
                userActionEvent.getOpenId(),
                userActionEvent.getChapterId(),
                userActionEvent.getChapterVersionId(),
                progress.getData());
        // 双写，如何学习章节版本Id和需要学习的章节版本id
        doubleWrite(userActionEvent);
        // 同步进度
        syncProgress(userActionEvent);
    }

    private void doubleWrite(UserActionEvent userActionEvent) {
        Long latestPublishChapterVersionId = userActionService.getLatestPublishChapterVersionId(userActionEvent.getTenantId(), userActionEvent.getChapterId()).getData();
        if (latestPublishChapterVersionId == null || Objects.equals(latestPublishChapterVersionId, userActionEvent.getChapterVersionId())) {
            return;
        }
        ChapterNode chapterNode = userActionEvent.getChapterNode();
        log.debug("diff chapter version openId:{} chapterId:{}, chapterVersionId: {}, {}", userActionEvent.getOpenId(), userActionEvent.getChapterId(), userActionEvent.getChapterVersionId(), latestPublishChapterVersionId);
        userActionService.directFinishNodeWithOutSendMessage(
                userActionEvent.getTenantId(),
                userActionEvent.getOpenId(),
                userActionEvent.getChapterId(),
                latestPublishChapterVersionId,
                chapterNode.getId());
    }

    private void syncProgress(UserActionEvent userActionEvent) {
        ChapterNode chapterNode = userActionEvent.getChapterNode();
        if (chapterNode.isRealQuestionNode()) {
            return;
        }
        Long chapterVersionId = userActionEvent.getChapterVersionId();
        BookVersion bookVersion = bookVersionService.getBookVersionByChapterVersionId(chapterVersionId);
        log.info("sync book version: {}, {}", bookVersion.getBookId(), bookVersion.getVersionNum());
        questionAnswerPushService.pushUserContentProgressToThird(
                userActionEvent.getOpenId(),
                chapterNode.getId(),
                chapterNode.getType(),
                bookVersion.getBookId(),
                bookVersion.getVersionNum(),
                userActionEvent.getDataPackage(),
                userActionEvent.getIp()
        );
    }
}
