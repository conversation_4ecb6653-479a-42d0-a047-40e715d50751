package com.unipus.digitalbook.listener.standalone;

import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.events.UserAnswerEvent;
import com.unipus.digitalbook.service.ChapterService;
import com.unipus.digitalbook.service.QuestionPushService;
import com.unipus.digitalbook.service.UserActionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class UserAnswerEventListener {
    @Resource
    private ChapterService chapterService;

    @Resource
    private UserActionService userActionService;

    @EventListener
    public void handleUserAnswerEvent(UserAnswerEvent event) {
        log.debug("收到用户作答事件: {}, {}, {}", event.getChapterVersionId(), event.getQuestion().getId(), event.getEventType());
        List<UserAnswer> userAnswers = event.getUserAnswers();
        if (CollectionUtils.isEmpty(userAnswers)) {
            return;
        }
        Long tenantId = userAnswers.getFirst().getTenantId();
        String openId = userAnswers.getFirst().getOpenId();
        String bizQuestionId = event.getQuestion().getBizGroupId();
        ChapterNode chapterNode = chapterService.getQuestionNodeByChapterVersionId(event.getChapterVersionId()).get(bizQuestionId);
        if (chapterNode == null) {
            log.error("节点可能已变更 {}", bizQuestionId);
            return;
        }
        UserAction userAction = new UserAction();
        userAction.setChapterNode(chapterNode);
        userAction.setTenantId(tenantId);
        userAction.setOpenId(openId);
        userAction.setChapterId(event.getChapterId());
        userAction.setChapterVersionId(event.getChapterVersionId());
        userAction.setQuestionCompleted(true);
        // 完成节点
        userActionService.finishNode(userAction);
    }
}
