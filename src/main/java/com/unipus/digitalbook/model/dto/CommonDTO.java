package com.unipus.digitalbook.model.dto;


import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "基本类型通用DTO")
public class CommonDTO<T> implements Serializable {
    private T value;

    public CommonDTO(T value) {
        this.value = value;
    }

    public T getValue() {
        return value;
    }

    public void setValue(T value) {
        this.value = value;
    }

}
