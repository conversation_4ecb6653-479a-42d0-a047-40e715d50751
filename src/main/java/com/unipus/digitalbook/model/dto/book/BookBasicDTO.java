package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.book.BookBasic;
import com.unipus.digitalbook.model.enums.BusinessTypeEnum;
import com.unipus.digitalbook.model.enums.CourseNatureEnum;
import com.unipus.digitalbook.model.enums.LanguageEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@Schema(description = "教材基本信息 DTO")
public class BookBasicDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(
            title = "ID",
            description = "基本信息的唯一标识符",
            example = "1001"
    )
    private Long id;

    @Schema(description = "关联教材ID", example = "B001")
    private String bookId;

    @Schema(
            title = "教材中文名称",
            description = "教材的完整中文名称",
            example = "高等数学（第四版）"
    )
    private String chineseName;

    @Schema(
            title = "教材英文名称",
            description = "教材的英文名称或翻译",
            example = "Advanced Mathematics (4th Edition)"
    )
    private String englishName;

    @Schema(
            title = "语种",
            description = "教材使用的语言",
            example = "cn"
    )
    private LanguageEnum language;

    @Schema(
            title = "教材业务类型",
            description = "教材的业务分类",
            example = "本科教材"
    )
    private BusinessTypeEnum businessType;

    @Schema(
            title = "教程系列ID",
            description = "关联的教材系列ID",
            example = "2001"
    )
    private Long seriesId;

    @Schema(
            title = "对应课程",
            description = "教材对应的课程名称",
            example = "高等数学"
    )
    private String course;

    @Schema(
            title = "课程性质",
            description = "课程的性质分类"
    )
    private CourseNatureEnum courseNature;

    @Schema(
            title = "适用专业",
            description = "教材适用的专业范围",
            example = "数学、物理、工程类"
    )
    private String applicableMajor;

    @Schema(
            title = "适用年级",
            description = "教材适用的年级范围",
            example = "大一、大二"
    )
    private String applicableGrade;

    @Schema(
            title = "联系电话",
            description = "联系人电话号码",
            example = "13800138000"
    )
    private String contactPhone;

    @Schema(
            title = "联系邮箱",
            description = "联系人电子邮箱地址",
            example = "<EMAIL>"
    )
    private String contactEmail;

    @Schema(
            title = "PC端封面图片地址",
            description = "教材PC端封面图片的URL地址",
            example = "https://example.com/images/cover/pc/math-4th.jpg"
    )
    private String pcCoverUrl;

    @Schema(
            title = "APP横版封面图片地址",
            description = "教材APP横版封面图片的URL地址",
            example = "https://example.com/images/cover/app/horizontal/math-4th.jpg"
    )
    private String appHorizontalCoverUrl;

    @Schema(
            title = "APP竖版封面图片地址",
            description = "教材APP竖版封面图片的URL地址",
            example = "https://example.com/images/cover/app/vertical/math-4th.jpg"
    )
    private String appVerticalCoverUrl;

    @Schema(
            title = "浅色",
            description = "浅色"
    )
    private String lightColor;

    @Schema(
            title = "深色",
            description = "深色"
    )
    private String darkColor;

    @Schema(
            title = "版本号",
            description = "教材的版本号",
            example = "4.2.1"
    )
    private String versionNumber;

    @Schema(
            title = "创建时间",
            description = "记录创建的时间"
    )
    private Long createTime;

    @Schema(
            title = "最后更新时间",
            description = "记录最后更新的时间"
    )
    private Long updateTime;

    @Schema(
            title = "创建者ID",
            description = "创建该记录的用户ID",
            example = "1001"
    )
    private Long createBy;

    @Schema(
            title = "最后更新者ID",
            description = "最后更新该记录的用户ID",
            example = "1002"
    )
    private Long updateBy;

    @Schema(
            title = "是否纯数字教材",
            description = "标识是否为纯数字教材: true-是, false-否",
            example = "false"
    )
    private Boolean digitalFlag;


    public BookBasicDTO(BookBasic book) {
        this.id = book.getId();
        this.bookId = book.getBookId();
        this.chineseName = book.getChineseName();
        this.englishName = book.getEnglishName();
        this.language = LanguageEnum.getByCode(book.getLanguage());
        this.businessType = BusinessTypeEnum.getByCode(book.getBusinessType());
        this.seriesId = book.getSeriesId();
        this.course = book.getCourse();
        this.courseNature = CourseNatureEnum.getByCode(book.getCourseNature());
        this.applicableMajor = book.getApplicableMajor();
        this.applicableGrade = book.getApplicableGrade();
        this.contactPhone = book.getContactPhone();
        this.contactEmail = book.getContactEmail();
        this.pcCoverUrl = book.getPcCoverUrl();
        this.appHorizontalCoverUrl = book.getAppHorizontalCoverUrl();
        this.appVerticalCoverUrl = book.getAppVerticalCoverUrl();
        this.lightColor = book.getLightColor();
        this.darkColor = book.getDarkColor();
        this.versionNumber = book.getVersionNumber();
        this.createTime = book.getCreateTime().getTime();
        this.updateTime = book.getUpdateTime().getTime();
        this.createBy = book.getCreateBy();
        this.updateBy = book.getUpdateBy();
        this.digitalFlag = book.getDigitalFlag();
    }

}