package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.book.BookFile;

import java.util.Date;

public class BookFileDTO {
    /**
     * 资源唯一ID (主键)
     */
    private Long id;

    /**
     * 关联书籍的ID
     */
    private String bookId;

    /**
     * 资源名称（例如：原始文件名或用户自定义名称）
     */
    private String resourceName;

    /**
     * 资源的存储路径或URL
     */
    private String storagePath;

    /**
     * 资源类型 (例如：IMAGE, AUDIO, VIDEO, DOCUMENT)
     */
    private String resourceType;

    /**
     * MIME类型 (例如：image/jpeg, audio/mpeg, application/pdf)
     */
    private String mimeType;

    /**
     * 文件大小（字节）
     */
    private Long fileSizeBytes;

    /**
     * 上传者用户ID (如果需要追踪上传者，可以关联用户表)
     */
    private Long uploaderId;

    /**
     * 资源状态 (例如：ACTIVE, DELETED, PENDING)
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 资源描述或备注 (可选)
     */
    private String description;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String  bookId) {
        this.bookId = bookId;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getStoragePath() {
        return storagePath;
    }

    public void setStoragePath(String storagePath) {
        this.storagePath = storagePath;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public Long getFileSizeBytes() {
        return fileSizeBytes;
    }

    public void setFileSizeBytes(Long fileSizeBytes) {
        this.fileSizeBytes = fileSizeBytes;
    }

    public Long getUploaderId() {
        return uploaderId;
    }

    public void setUploaderId(Long uploaderId) {
        this.uploaderId = uploaderId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BookFileDTO(){
        super();
    }


    public BookFileDTO(BookFile bookFile) {
        if (bookFile != null) {
            this.id = bookFile.getId();
            this.bookId = bookFile.getBookId();
            this.resourceName = bookFile.getResourceName();
            this.storagePath = bookFile.getStoragePath();
            this.resourceType = bookFile.getResourceType();
            this.mimeType = bookFile.getMimeType();
            this.fileSizeBytes = bookFile.getFileSizeBytes();
            this.uploaderId = bookFile.getUploaderId();
            this.status = bookFile.getStatus();
            this.createTime = bookFile.getCreateTime();
            this.description = bookFile.getDescription();
        }
    }

}

