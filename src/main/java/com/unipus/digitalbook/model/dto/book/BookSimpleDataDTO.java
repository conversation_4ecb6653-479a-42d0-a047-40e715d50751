package com.unipus.digitalbook.model.dto.book;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 书籍简要信息 DTO
 *
 * <AUTHOR>
 * @date 2025-06-24 15:31
 */
@Data
@Schema(description = "书籍简要信息")
public class BookSimpleDataDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "教材唯一标识符", example = "1")
    private String bookId;

    @Schema(description = "基本信息")
    private BookBasicDTO basic;

    @Schema(description = "简介")
    private BookIntroDTO intro;

    @Schema(description = "版权信息")
    private BookCopyrightDTO copyright;

}
