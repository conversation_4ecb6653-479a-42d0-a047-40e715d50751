package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.dto.chapter.ChapterMeteInfoDTO;
import com.unipus.digitalbook.model.dto.chapter.ChapterStructDTO;
import com.unipus.digitalbook.model.dto.complement.ComplementResourceListDTO;
import com.unipus.digitalbook.model.dto.paper.PaperSyncInfoDTO;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.entity.book.BookChangeItemEntity;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.paper.PaperSyncInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Schema(description = "教材传输数据")
public class BookStructDTO implements Serializable {

    @Schema(description = "教材唯一标识符",example = "1")
    private String bookId;

    @Schema(description = "版本号",example = "1.0")
    private String versionNumber;

    @Schema(description = "显示版本号")
    private String showVersionNumber;

    @Schema(description = "版本ID")
    private Long versionId;

    @Schema(description = "书名",example = "英语书")
    private String bookName;

    @Schema(description = "基本信息")
    private BookBasicInfoDTO bookBasicInfo;

    @Schema(description = "版权信息")
    private BookCopyrightDTO copyright;

    @Schema(description = "简介")
    private BookCoverIntroDTO intro;

    @Schema(description = "配套资源列表")
    private ComplementResourceListDTO complementResourceList;

    @Schema(description = "试卷同步信息列表")
    private List<PaperSyncInfoDTO> paperList;

    @Schema(description = "章节列表")
    private List<ChapterStructDTO> chapterList;

    @Schema(description = "全书预计章节列表")
    private List<ChapterMeteInfoDTO> expectedChapterList;

    @Schema(description = "当前版本变更内容列表")
    private List<BookVersionChangeDTO> changeContentList;

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }


    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }


    public BookCopyrightDTO getCopyright() {
        return copyright;
    }

    public void setCopyright(BookCopyrightDTO copyright) {
        this.copyright = copyright;
    }

    public BookCoverIntroDTO getIntro() {
        return intro;
    }

    public void setIntro(BookCoverIntroDTO intro) {
        this.intro = intro;
    }

    public ComplementResourceListDTO getComplementResourceList() {
        return complementResourceList;
    }

    public void setComplementResourceList(ComplementResourceListDTO complementResourceList) {
        this.complementResourceList = complementResourceList;
    }

    public List<ChapterStructDTO> getChapterList() {
        return chapterList;
    }

    public void setChapterList(List<ChapterStructDTO> chapterList) {
        this.chapterList = chapterList;
    }

    public BookStructDTO(Book book, Map<Long, UserInfo> userMap) {
        this.bookId = book.getId();
        this.versionNumber = book.getBookVersion().getVersionNum();
        this.showVersionNumber = book.getBookVersion().getShowVersionNumber();
        this.versionId = book.getBookVersion().getId();
        this.bookName = book.getChineseName();
        this.bookBasicInfo = new BookBasicInfoDTO(book, userMap);
        this.intro = new BookCoverIntroDTO(book);
        this.copyright = new BookCopyrightDTO(book.getBookCopyright(), book.getBookBasic());
        // 配套资源
        if (book.getComplementResourceList()!=null&&book.getComplementResourceList().getComplementResourceList()!=null) {
            this.complementResourceList = new ComplementResourceListDTO(book.getComplementResourceList().getComplementResourceList(), book.getComplementResourceList().getComplementResourceList().size());
        }
        // 处理教材下的卷子版本数据
        List<PaperSyncInfo> paperSyncInfos = book.getPaperSyncInfos();
        if(CollectionUtils.isNotEmpty(paperSyncInfos)) {
            this.paperList = paperSyncInfos.stream().map(PaperSyncInfoDTO::new).toList();
        }

        if (book.getChapterList() != null) {
            this.chapterList = new ArrayList<>(book.getChapterList().size());
            book.getChapterList().forEach(chapter -> {
                ChapterStructDTO chapterStructDTO = new ChapterStructDTO(chapter);
                this.chapterList.add(chapterStructDTO);
            });
        }
    }

    public Long getVersionId() {
        return versionId;
    }

    public void setVersionId(Long versionId) {
        this.versionId = versionId;
    }

    public List<ChapterMeteInfoDTO> getExpectedChapterList() {
        return expectedChapterList;
    }

    public void setExpectedChapterList(List<ChapterMeteInfoDTO> expectedChapterList) {
        this.expectedChapterList = expectedChapterList;
    }

    public void setExpectedChapterListByEntity(List<Chapter> expectedChapterList) {
        if (this.expectedChapterList == null) {
            this.expectedChapterList = new ArrayList<>(expectedChapterList.size());
        }
        expectedChapterList.forEach(chapter -> {
            ChapterMeteInfoDTO chapterMeteInfoDTO = new ChapterMeteInfoDTO(chapter);
            this.expectedChapterList.add(chapterMeteInfoDTO);
        });
    }

    public List<BookVersionChangeDTO> getChangeContentList() {
        return changeContentList;
    }

    public void setChangeContentList(List<BookVersionChangeDTO> changeContentList) {
        this.changeContentList = changeContentList;
    }


    public void setChangeContentListByEntity(List<BookChangeItemEntity> changeContentList) {
        if (changeContentList == null){
            this.changeContentList = new ArrayList<>();
        }else {
            this.changeContentList = changeContentList.stream().map(BookVersionChangeDTO::new).toList();
        }
    }

    public BookBasicInfoDTO getBookBasicInfo() {
        return bookBasicInfo;
    }

    public void setBookBasicInfo(BookBasicInfoDTO bookBasicInfo) {
        this.bookBasicInfo = bookBasicInfo;
    }

    public String getShowVersionNumber() {
        return showVersionNumber;
    }

    public void setShowVersionNumber(String showVersionNumber) {
        this.showVersionNumber = showVersionNumber;
    }

    public List<PaperSyncInfoDTO> getPaperList() {
        return paperList;
    }

    public void setPaperList(List<PaperSyncInfoDTO> paperList) {
        this.paperList = paperList;
    }
}


