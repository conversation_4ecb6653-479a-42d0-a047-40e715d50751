package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.book.BookTemporarySnapshot;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 教材临时快照DTO
 */
@Schema(description = "教材临时快照DTO")
public class BookTemporarySnapshotDTO implements Serializable {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "教材ID")
    private String bookId;

    @Schema(description = "章节ID")
    private String chapterId;

    @Schema(description = "快照内容")
    private String content;

    @Schema(description = "创建时间")
    private Long createTime;

    @Schema(description = "最后更新时间")
    private Long updateTime;

    public BookTemporarySnapshotDTO() {
    }

    public BookTemporarySnapshotDTO(BookTemporarySnapshot bookTemporarySnapshot) {
        if (bookTemporarySnapshot != null) {
            this.id = bookTemporarySnapshot.getId();
            this.bookId = bookTemporarySnapshot.getBookId();
            this.chapterId = bookTemporarySnapshot.getChapterId();
            this.content = bookTemporarySnapshot.getContent();
            this.createTime = bookTemporarySnapshot.getCreateTime() != null ? bookTemporarySnapshot.getCreateTime().getTime() : null;
            this.updateTime = bookTemporarySnapshot.getUpdateTime() != null ? bookTemporarySnapshot.getUpdateTime().getTime() : null;
        }
    }

    // Getters and Setters


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
