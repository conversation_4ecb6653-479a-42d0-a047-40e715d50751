package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.permission.ResourceUser;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 批量查询章节协作者结果DTO
 */
@Schema(description = "批量查询章节协作者结果DTO")
public class ChapterCollaboratorDTO implements Serializable {

    @Schema(description = "章节协作者列表")
    private Map<String, List<BookUserDTO>> chapterMap;

    public ChapterCollaboratorDTO(Map<String, List<ResourceUser>> chapterMap) {
        if (CollectionUtils.isEmpty(chapterMap)) {
            return;
        }
        this.chapterMap = chapterMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> entry.getValue() == null ? Collections.emptyList() :
                                entry.getValue().stream().map(BookUserDTO::new).toList()));
    }

    public Map<String, List<BookUserDTO>> getChapterMap() {
        return chapterMap;
    }

    public void setChapterMap(Map<String, List<BookUserDTO>> chapterMap) {
        this.chapterMap = chapterMap;
    }
}