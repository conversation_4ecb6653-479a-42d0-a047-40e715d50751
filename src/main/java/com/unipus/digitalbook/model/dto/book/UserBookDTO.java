package com.unipus.digitalbook.model.dto.book;

import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.entity.book.UserBook;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

@Schema(description = "用户教材")
public class UserBookDTO implements Serializable {
    @Schema(description = "编辑时间")
    private Long editTime;
    @Schema(description = "加入教材编辑时间")
    private Long joinEditTime;

    @Schema(description = "加入教材预览时间")
    private Long joinPreviewTime;

    @Schema(description = "教材信息")
    private BookDetailDTO book;

    @Schema(description = "是否上架")
    private Boolean publishFlag;

    @Schema(description = "上架时间")
    private Long publishTime;

    public UserBookDTO(){}
    public UserBookDTO(UserBook userBook, Map<Long, UserInfo> userMap) {
        this.editTime = Optional.ofNullable(userBook.getEditTime()).map(Date::getTime).orElse(null);
        this.joinEditTime = Optional.ofNullable(userBook.getJoinEditTime()).map(Date::getTime).orElse(null);
        this.joinPreviewTime = Optional.ofNullable(userBook.getJoinPreviewTime()).map(Date::getTime).orElse(null);
        Book bookEntity = userBook.getBook();
        if (bookEntity != null) {
            this.book = new BookDetailDTO(bookEntity, userMap);
        }
    }

    public Long getEditTime() {
        return editTime;
    }

    public void setEditTime(Long editTime) {
        this.editTime = editTime;
    }

    public Long getJoinEditTime() {
        return joinEditTime;
    }

    public void setJoinEditTime(Long joinEditTime) {
        this.joinEditTime = joinEditTime;
    }

    public BookDetailDTO getBook() {
        return book;
    }

    public void setBook(BookDetailDTO book) {
        this.book = book;
    }

    public Long getJoinPreviewTime() {
        return joinPreviewTime;
    }

    public void setJoinPreviewTime(Long joinPreviewTime) {
        this.joinPreviewTime = joinPreviewTime;
    }

    public Boolean getPublishFlag() {
        return publishFlag;
    }

    public void setPublishFlag(Boolean publishFlag) {
        this.publishFlag = publishFlag;
    }

    public Long getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Long publishTime) {
        this.publishTime = publishTime;
    }
}
