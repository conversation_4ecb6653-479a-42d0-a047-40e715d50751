package com.unipus.digitalbook.model.dto.chapter;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

public class ChapterCatalogDTO implements Serializable {
    @Schema(description = "教材章节ID")
    private String chapterId;

    @Schema(description = "教材章节名称")
    private String chapterName;

    @Schema(description = "教材章节资源")
    private String catalog;


    public ChapterCatalogDTO() {
    }
    public ChapterCatalogDTO(ChapterVersion chapter) {
        this.chapterId = chapter.getChapterId();
        this.chapterName=chapter.getName();
        this.catalog = JsonUtil.toJsonString(chapter.getHeaderNodeList());
    }
    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getCatalog() {
        return catalog;
    }

    public void setCatalog(String catalog) {
        this.catalog = catalog;
    }

    public String getChapterName() {
        return chapterName;
    }

    public void setChapterName(String chapterName) {
        this.chapterName = chapterName;
    }
}
