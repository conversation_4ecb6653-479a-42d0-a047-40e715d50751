package com.unipus.digitalbook.model.dto.chapter;

import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

public class ChapterCatalogListDTO implements Serializable {
    @Schema(description = "教材ID")
    private String bookId;

    @Schema(description = "章节资源")
    private List<ChapterCatalogDTO> catalogList;

    public ChapterCatalogListDTO(){}
    public ChapterCatalogListDTO(String bookId, List<ChapterVersion> latestCatalogList) {
        this.bookId = bookId;
        this.catalogList = latestCatalogList.stream().map(ChapterCatalogDTO::new).toList();
    }


    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public List<ChapterCatalogDTO> getCatalogList() {
        return catalogList;
    }

    public void setCatalogList(List<ChapterCatalogDTO> catalogList) {
        this.catalogList = catalogList;
    }
}
