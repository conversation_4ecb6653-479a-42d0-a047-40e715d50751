package com.unipus.digitalbook.model.dto.chapter;

import com.unipus.digitalbook.model.dto.book.BookUserDTO;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.permission.ResourceUser;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

@Schema(description = "教材章节")
public class ChapterDTO implements Serializable {
    @Schema(description = "章节ID")
    private String id;

    @Schema(description = "关联教材ID")
    private String bookId;

    @Schema(description = "章节编号")
    private Integer chapterNumber;

    @Schema(description = "章节名称")
    private String name;

    @Schema(description = "创建时间")
    private Long createTime;

    @Schema(description = "更新时间")
    private Long updateTime;

    @Schema(description = "协作者列表")
    private List<BookUserDTO> collaborators;

    @Schema(description = "是否可以编辑")
    private Boolean isEditable = false;

    @Schema(description = "是否是编者")
    private Boolean isEditor = false;


    public ChapterDTO(Chapter chapter){
        this.id = chapter.getId();
        this.bookId = chapter.getBookId();
        this.chapterNumber = chapter.getChapterNumber();
        this.name = chapter.getName();
    }



    public ChapterDTO(Long editorId, Chapter chapter, List<ResourceUser> resourceUsers, Long currentUserId) {
        this.bookId = chapter.getBookId();
        this.id = chapter.getId();
        this.chapterNumber = chapter.getChapterNumber();
        this.name = chapter.getName();
        this.createTime = chapter.getCreateTime().getTime();
        this.updateTime = chapter.getUpdateTime().getTime();
        if (resourceUsers != null) {
            this.collaborators = resourceUsers.stream().map(BookUserDTO::new).toList();
            this.isEditable = resourceUsers.stream().anyMatch(resourceUser -> resourceUser.getUserId().equals(currentUserId));
        } else {
            this.collaborators = Collections.emptyList();
        }
        this.isEditor = currentUserId.equals(editorId);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public Integer getChapterNumber() {
        return chapterNumber;
    }

    public void setChapterNumber(Integer chapterNumber) {
        this.chapterNumber = chapterNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getEditable() {
        return isEditable;
    }

    public void setEditable(Boolean editable) {
        isEditable = editable;
    }

    public Boolean getEditor() {
        return isEditor;
    }

    public void setEditor(Boolean editor) {
        isEditor = editor;
    }

    public List<BookUserDTO> getCollaborators() {
        return collaborators;
    }

    public void setCollaborators(List<BookUserDTO> collaborators) {
        this.collaborators = collaborators;
    }
}
