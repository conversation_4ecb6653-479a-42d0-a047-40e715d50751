package com.unipus.digitalbook.model.dto.chapter;

import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.permission.ResourceUser;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Schema(description = "教材章节列表")
public class ChapterListDTO implements Serializable {

    @Schema(description = "章节列表")
    private List<ChapterDTO> chapterList;

    public ChapterListDTO(){
        this.chapterList = Collections.emptyList();
    }

    public ChapterListDTO(List<Chapter> chapters){
        if (CollectionUtils.isEmpty(chapters)) {
            this.chapterList = Collections.emptyList();
        } else {
            this.chapterList = chapters.stream().map(ChapterDTO::new).collect(Collectors.toList());
        }
    }

    public ChapterListDTO(Long editorId, List<Chapter> chapterList, Map<String, List<ResourceUser>> chapterEditorMap, Long currentUserId) {
        if (CollectionUtils.isEmpty(chapterList)) {
            this.chapterList = Collections.emptyList();
        } else {
            this.chapterList = chapterList.stream()
                    .map(chapter -> new ChapterDTO(editorId, chapter, chapterEditorMap.get(chapter.getId()), currentUserId )).collect(Collectors.toList());
        }
    }
    public List<ChapterDTO> getChapterList() {
        return chapterList;
    }

    public void setChapterList(List<ChapterDTO> chapterList) {
        this.chapterList = chapterList;
    }
}
