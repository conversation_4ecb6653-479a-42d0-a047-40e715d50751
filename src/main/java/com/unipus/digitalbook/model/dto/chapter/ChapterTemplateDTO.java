package com.unipus.digitalbook.model.dto.chapter;

import com.unipus.digitalbook.model.entity.chapter.ChapterTemplate;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;

@Schema(description = "章节模板的传输对象")
public class ChapterTemplateDTO implements Serializable {

    @Schema(description = "模板ID", example = "1")
    private Integer id;

    @Schema(description = "模板名称", example = "示例模板")
    private String name;

    @Schema(description = "模板来源章节ID", example = "chapter_123")
    private String fromChapterId;

    @Schema(description = "模板JSON存储地址", example = "https://example.com/template.json")
    private String url;

    @Schema(description = "模板预览图片地址", example = "https://example.com/preview.jpg")
    private String previewImageUrl;

    @Schema(description = "创建时间", example = "2023-10-01T12:00:00Z")
    private Date createTime;

    @Schema(description = "最后更新时间", example = "2023-10-02T12:00:00Z")
    private Date updateTime;

    @Schema(description = "创建者ID", example = "1001")
    private Long createBy;

    @Schema(description = "最后更新者ID", example = "1002")
    private Long updateBy;

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFromChapterId() {
        return fromChapterId;
    }

    public void setFromChapterId(String fromChapterId) {
        this.fromChapterId = fromChapterId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getPreviewImageUrl() {
        return previewImageUrl;
    }

    public void setPreviewImageUrl(String previewImageUrl) {
        this.previewImageUrl = previewImageUrl;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public void fromEntity(ChapterTemplate chapterTemplate) {
        setId(chapterTemplate.getId());
        setName(chapterTemplate.getName());
        setFromChapterId(chapterTemplate.getFromChapterId());
        setUrl(chapterTemplate.getUrl());
        setPreviewImageUrl(chapterTemplate.getPreviewImageUrl());
        setCreateTime(chapterTemplate.getCreateTime());
        setUpdateTime(chapterTemplate.getUpdateTime());
        setCreateBy(chapterTemplate.getCreateBy());
        setUpdateBy(chapterTemplate.getUpdateBy());
    }

}