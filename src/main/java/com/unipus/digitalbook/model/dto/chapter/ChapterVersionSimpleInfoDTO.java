package com.unipus.digitalbook.model.dto.chapter;

import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "教材章节版本信息")
public class ChapterVersionSimpleInfoDTO implements Serializable {
    @Schema(description = "版本ID")
    private Long id;
    @Schema(description = "所属章节ID")
    private String chapterId;
    @Schema(description = "章节名称")
    private String name;
    @Schema(description = "版本号")
    private String versionNumber;
    @Schema(description = "创建人")
    private String  creatorName;
    @Schema(description = "创建时间戳")
    private Long createTimeStamp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }


    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Long getCreateTimeStamp() {
        return createTimeStamp;
    }

    public void setCreateTimeStamp(Long createTimeStamp) {
        this.createTimeStamp = createTimeStamp;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void fromEntity(ChapterVersion entity) {
        if (entity == null) {
            return ;
        }
        this.setId(entity.getId());
        this.setChapterId(entity.getChapterId());
        this.setVersionNumber(entity.getVersionNumber());
        this.setCreateTimeStamp(entity.getCreateTime().getTime());
        this.setCreatorName(entity.getCreatorName());
        this.setName(entity.getName());
    }
}
