package com.unipus.digitalbook.model.dto.content;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.dto.question.BigQuestionGroupDTO;
import com.unipus.digitalbook.model.entity.content.CustomContent;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Schema(description = "自建内容信息")
public class CustomContentDTO implements Serializable {

    @Schema(description = "内容业务ID")
    private String bizId;

    @Schema(description = "内容类型  1：自定义章节/2：自定义段落")
    private Integer type;

    @Schema(description = "内容名称")
    private String name;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "学生内容")
    private String studentContent;

    @Schema(description = "头图地址")
    private String headerImg;

    @Schema(description = "目录结构")
    private String catalog;

    @Schema(description = "资源信息")
    private String resource;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "是否有效 0-无效 1-有效")
    private Boolean enable;

    @Schema(description = "整体结构节点列表")
    private List<CustomContentNodeDTO> totalStructNodeList;

    @Schema(description = "内容的题")
    private List<BigQuestionGroupDTO> questionList;

    /**
     * 默认构造方法
     */
    public CustomContentDTO() {
    }

    /**
     * 从CustomContent实体构造DTO
     */
    public CustomContentDTO(CustomContent customContent) {
        this.bizId = customContent.getBizId();
        this.type = customContent.getType();
        this.name = customContent.getName();
        this.content = customContent.getContent();
        this.studentContent = customContent.getStudentContent();
        this.headerImg = customContent.getHeaderImg();
        this.resource = customContent.getResource();
        this.tenantId = customContent.getTenantId();
        this.createTime = customContent.getCreateTime();
        this.updateTime = customContent.getUpdateTime();
        this.enable = customContent.getEnable();
        // 转换目录节点列表
        if (customContent.getCatalogNodeList() != null && !customContent.getCatalogNodeList().isEmpty()) {
            this.catalog = JsonUtil.toJsonString(customContent.getCatalogNodeList());
        }
        // 转换整体结构节点列表
        if (customContent.getTotalStructNodeList() != null && !customContent.getTotalStructNodeList().isEmpty()) {
            this.totalStructNodeList = customContent.getTotalStructNodeList().stream()
                    .map(CustomContentNodeDTO::new)
                    .toList();
        }
        // 转换题目列表
        if (customContent.getQuestionList() != null && !customContent.getQuestionList().isEmpty()) {
            this.questionList = customContent.getQuestionList().stream()
                    .map(q -> new BigQuestionGroupDTO(q, false))
                    .toList();
        }
    }

    // Getter and Setter methods
    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getStudentContent() {
        return studentContent;
    }

    public void setStudentContent(String studentContent) {
        this.studentContent = studentContent;
    }

    public String getHeaderImg() {
        return headerImg;
    }

    public void setHeaderImg(String headerImg) {
        this.headerImg = headerImg;
    }

    public String getCatalog() {
        return catalog;
    }

    public void setCatalog(String catalog) {
        this.catalog = catalog;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public List<CustomContentNodeDTO> getTotalStructNodeList() {
        return totalStructNodeList;
    }

    public void setTotalStructNodeList(List<CustomContentNodeDTO> totalStructNodeList) {
        this.totalStructNodeList = totalStructNodeList;
    }

    public List<BigQuestionGroupDTO> getQuestionList() {
        return questionList;
    }

    public void setQuestionList(List<BigQuestionGroupDTO> questionList) {
        this.questionList = questionList;
    }
}
