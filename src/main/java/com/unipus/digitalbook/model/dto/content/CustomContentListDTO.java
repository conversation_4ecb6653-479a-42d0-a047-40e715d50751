package com.unipus.digitalbook.model.dto.content;

import com.unipus.digitalbook.model.entity.content.CustomContent;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "自建内容信息列表")
public class CustomContentListDTO implements Serializable {

    @Schema(description = "自建内容信息列表")
    private List<CustomContentDTO> customContentList;

    public CustomContentListDTO() {
    }

    public CustomContentListDTO(List<CustomContent> customContentList) {
        if (customContentList == null) {
            return;
        }
        this.customContentList = customContentList.stream().map(CustomContentDTO::new).toList();
    }

    public List<CustomContentDTO> getCustomContentList() {
        return customContentList;
    }

    public void setCustomContentList(List<CustomContentDTO> customContentList) {
        this.customContentList = customContentList;
    }
}
