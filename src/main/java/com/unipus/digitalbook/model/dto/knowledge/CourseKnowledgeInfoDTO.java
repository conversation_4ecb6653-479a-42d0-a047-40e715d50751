package com.unipus.digitalbook.model.dto.knowledge;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName course_knowledge_info
 */
@Data
public class CourseKnowledgeInfoDTO implements Serializable {
    /**
     * 
     */
    private Long id;

    /**
     * 课程Id 例如：course-v2:Unipus+1234Bb+20220729
     */
    private String courseIdStr;

    /**
     * 课程Id 
     */
    private Long courseId;

    /**
     * 图谱名称
     */
    private String name;

    /**
     * 图谱描述
     */
    private String desc;

    /**
     * 图谱Id 云知声返回的图谱Id
     */
    private String knowledgeId;

    /**
     * 知识标签集 【知识标签1】
     */
    private String knoweldgeTag;

    /**
     * 图谱封面图
     */
    private String backgroundUrl;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer enable;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 状态（0-未发布 1-已发布 2 发布失败）
     */
    private Integer status;

    private static final long serialVersionUID = 1L;
}