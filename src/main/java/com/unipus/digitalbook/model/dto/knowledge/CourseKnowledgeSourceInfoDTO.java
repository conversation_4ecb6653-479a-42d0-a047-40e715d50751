package com.unipus.digitalbook.model.dto.knowledge;

import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeResourceDetailResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @TableName course_knowledge_source_info
 */
@Data
public class CourseKnowledgeSourceInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    //课程知识图谱ID
    private Long courseKnowledgeId;
    //课程知识图谱资源ID
    private Long courseResourceId;
    private String thirdResourceId;
    private String name;
    private String type;
    private String url;
    private String dir;
    private String description;
    private String mainSourceUrl;
    private List<SubSourceInfo> subSourceList;
    private Integer location;
    private String multimediaKey;
    private String multimediaIndex;
    private String multimediaName;

    //用户信息
    private String userName;
    private String avatarUrl;
    private Long createTime;
    private String ssoId;
    private Integer enableStatus;
    private Integer startTime;
    private String startPictureUrl;

    //知识标签
    private List<KnowledgeResourceDetailResponse.Node> nodes;
    //资源标签
    private List<KnowledgeResourceDetailResponse.Label> labels;

    @Data
    public static class SubSourceInfo {
        private Long id;
        private String sourceUrl;
        private Integer enableStatus;

    }

}