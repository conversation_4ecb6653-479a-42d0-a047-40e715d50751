package com.unipus.digitalbook.model.dto.knowledge;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @TableName course_knowledge_source_info
 */
@Data
public class CourseKnowledgeSourceInfoGroupDTO implements Serializable {

    private String type;
    private Integer location;
    private String dir;
    private String multimediaKey;

    private String userName;
    private String avatarUrl;
    private Long createTime;

    private List<CourseKnowledgeSourceInfoDTO> courseKnowledgeSourceInfoDTOs;

}