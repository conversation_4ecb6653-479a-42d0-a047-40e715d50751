package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.common.utils.DateUtil;
import com.unipus.digitalbook.model.entity.paper.Paper;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 试卷DTO
 */
@Schema(description = "试卷DTO")
public class PaperDTO implements Serializable {

    @Schema(description = "试卷ID")
    private String paperId;
    @Schema(description = "试卷名称")
    private String paperName;
    @Schema(description = "试卷类型")
    private Integer paperType;
    @Schema(description = "试卷题目总数")
    private Integer questionCount;
    @Schema(description = "试卷描述")
    private String description;
    @Schema(description = "试卷内容")
    private String content;
    @Schema(description = "创建时间")
    private String createTime;
    @Schema(description = "创建人")
    private String createBy;
    @Schema(description = "引用标识")
    private Boolean referenceFlag;
    @Schema(description = "上架标识")
    private Boolean publishFlag;
    @Schema(description = "试卷题目列表")
    private PaperQuestionListDTO questionListDTO;

    public PaperDTO(Paper paper, PaperQuestionListDTO questionListDTO) {
        this.paperId = paper.getPaperId();
        this.paperName = paper.getPaperName();
        this.paperType = paper.getPaperType().getCode();
        this.questionCount = paper.getQuestionCount();
        this.description = paper.getDescription();
        this.content = paper.getContent();
        this.createTime = DateUtil.formatDateTimeMinutesWithYearOrNot(paper.getCreateTime());
        this.createBy = paper.getCreatorName();
        this.referenceFlag = paper.getReferenceFlag();
        this.publishFlag = paper.getPublishFlag();
        this.questionListDTO = questionListDTO;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public Integer getPaperType() {
        return paperType;
    }

    public void setPaperType(Integer paperType) {
        this.paperType = paperType;
    }

    public Integer getQuestionCount() {
        return questionCount;
    }

    public void setQuestionCount(Integer questionCount) {
        this.questionCount = questionCount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Boolean getReferenceFlag() {
        return referenceFlag;
    }

    public void setReferenceFlag(Boolean referenceFlag) {
        this.referenceFlag = referenceFlag;
    }

    public Boolean getPublishFlag() {
        return publishFlag;
    }

    public void setPublishFlag(Boolean publishFlag) {
        this.publishFlag = publishFlag;
    }

    public PaperQuestionListDTO getQuestionListDTO() {
        return questionListDTO;
    }

    public void setQuestionListDTO(PaperQuestionListDTO questionListDTO) {
        this.questionListDTO = questionListDTO;
    }
}
