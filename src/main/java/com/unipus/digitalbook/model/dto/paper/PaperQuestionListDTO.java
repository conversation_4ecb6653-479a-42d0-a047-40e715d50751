package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@Schema(description = "题目列表DTO")
public class PaperQuestionListDTO implements Serializable {

    @Schema(description = "大题组")
    private List<PaperBigQuestionGroupDTO> bigQuestions;

    public PaperQuestionListDTO(List<BigQuestionGroup> questionList, boolean isReturnAnswer) {
        if(CollectionUtils.isEmpty(questionList)){
            this.bigQuestions = List.of();
        }else {
            this.bigQuestions = questionList.stream().map(q -> new PaperBigQuestionGroupDTO(q, isReturnAnswer)).toList();
        }
    }

    public List<PaperBigQuestionGroupDTO> getBigQuestions() {
        return bigQuestions;
    }

    public void setBigQuestions(List<PaperBigQuestionGroupDTO> bigQuestions) {
        this.bigQuestions = bigQuestions;
    }
}
