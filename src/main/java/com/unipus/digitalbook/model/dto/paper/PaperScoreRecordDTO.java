package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.common.utils.DateUtil;
import com.unipus.digitalbook.model.entity.paper.PaperScoreHistory;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;

@Schema(description = "用户试卷作答记录DTO")
public class PaperScoreRecordDTO implements Serializable {

    @Schema(description = "成绩批次ID")
    private String scoreBatchId;
    @Schema(description = "试卷实例ID")
    private String instanceId;
    @Schema(description = "用户得分")
    private BigDecimal userScore;
    @Schema(description = "标准得分")
    private BigDecimal standardScore;
    @Schema(description = "提交时间")
    private String submitTime;

    public PaperScoreRecordDTO(PaperScoreHistory paperScoreHistory) {
        this.scoreBatchId = paperScoreHistory.getScoreBatchId();
        this.instanceId = paperScoreHistory.getInstanceId();
        this.userScore = paperScoreHistory.getUserScore();
        this.standardScore = paperScoreHistory.getStandardScore();
        this.submitTime = DateUtil.formatDateTimeMinutesWithYearOrNot(paperScoreHistory.getSubmitTime());
    }

    public String getScoreBatchId() {
        return scoreBatchId;
    }

    public void setScoreBatchId(String scoreBatchId) {
        this.scoreBatchId = scoreBatchId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public BigDecimal getUserScore() {
        return userScore;
    }

    public void setUserScore(BigDecimal userScore) {
        this.userScore = userScore;
    }

    public BigDecimal getStandardScore() {
        return standardScore;
    }

    public void setStandardScore(BigDecimal standardScore) {
        this.standardScore = standardScore;
    }

    public String getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(String submitTime) {
        this.submitTime = submitTime;
    }
}
