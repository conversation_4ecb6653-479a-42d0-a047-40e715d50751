package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.common.utils.ScoreUtil;
import com.unipus.digitalbook.model.entity.paper.PaperScoreHistory;
import com.unipus.digitalbook.model.enums.ScoreTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;

@Schema(description = "试卷成绩记录列表DTO")
public class PaperScoreRecordListDTO implements Serializable {

    @Schema(description = "提交次数")
    private Integer submitCount;

    @Schema(description = "最高分")
    private BigDecimal bestScore;

    @Schema(description = "试卷成绩记录列表")
    private List<PaperScoreRecordDTO> paperScoreRecordDTOs;

    public PaperScoreRecordListDTO(List<PaperScoreHistory> paperScoreHistories, ScoreTypeEnum scoreType) {
        this.paperScoreRecordDTOs = paperScoreHistories.stream().map(PaperScoreRecordDTO::new).toList();
        this.paperScoreRecordDTOs.forEach(dto -> {
            if(scoreType.match(ScoreTypeEnum.PERCENTAGE)){
                dto.setUserScore(ScoreUtil.keepOneDecimalForPercent(dto.getStandardScore(), dto.getUserScore()));
                dto.setStandardScore(new BigDecimal("100.0"));
            }else{
                dto.setUserScore(ScoreUtil.keepTwoDecimal(dto.getUserScore()));
                dto.setStandardScore(ScoreUtil.keepTwoDecimal(dto.getStandardScore()));
            }
        });


        this.submitCount = paperScoreHistories.size();
        // 查找成绩最高的记录
        PaperScoreHistory bestPaperScoreHistory = paperScoreHistories.stream()
                .filter(dto -> dto.getUserScore() != null)
                .max(Comparator.comparing(PaperScoreHistory::getUserScore)).orElse(null);
        if(bestPaperScoreHistory != null) {
            this.bestScore = bestPaperScoreHistory.getUserScore();
            if (scoreType.match(ScoreTypeEnum.PERCENTAGE)) {
                this.bestScore = calculateScoringRate(this.bestScore, bestPaperScoreHistory.getStandardScore());
            }else{
                this.bestScore = ScoreUtil.keepTwoDecimal(this.bestScore);
            }
        }
    }

    // 计算得分率(百分制变换)
    protected BigDecimal calculateScoringRate(BigDecimal userScore, BigDecimal standardScore){
        if(standardScore==null || standardScore.compareTo(BigDecimal.ZERO) == 0 ||
                userScore==null || userScore.compareTo(BigDecimal.ZERO) == 0 ){
            return BigDecimal.ZERO;
        } else {
            return userScore.multiply(new BigDecimal("100")).divide(standardScore, 1, RoundingMode.HALF_UP);
        }
    }

    public static PaperScoreRecordListDTO build(List<PaperScoreHistory> userPaperSubmitHistory, ScoreTypeEnum scoreType) {
        if(CollectionUtils.isEmpty(userPaperSubmitHistory)) {
            return null;
        }else{
            return new PaperScoreRecordListDTO(userPaperSubmitHistory, scoreType);
        }
    }

    public Integer getSubmitCount() {
        return submitCount;
    }

    public void setSubmitCount(Integer submitCount) {
        this.submitCount = submitCount;
    }

    public BigDecimal getBestScore() {
        return bestScore;
    }

    public void setBestScore(BigDecimal bestScore) {
        this.bestScore = bestScore;
    }

    public List<PaperScoreRecordDTO> getPaperScoreRecordDTOs() {
        return paperScoreRecordDTOs;
    }

    public void setPaperScoreRecordDTOs(List<PaperScoreRecordDTO> paperScoreRecordDTOs) {
        this.paperScoreRecordDTOs = paperScoreRecordDTOs;
    }
}
