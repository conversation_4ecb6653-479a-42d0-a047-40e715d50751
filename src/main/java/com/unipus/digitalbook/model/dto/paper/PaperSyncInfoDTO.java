package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.model.dto.question.BigQuestionGroupDTO;
import com.unipus.digitalbook.model.entity.paper.PaperSyncInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 试卷同步信息DTO
 */
@Schema(description = "试卷同步信息DTO")
public class PaperSyncInfoDTO implements Serializable {
    @Schema(description = "试卷ID")
    private String paperId;
    @Schema(description = "试卷版本号")
    private String versionNumber;
    @Schema(description = "试卷类型,1:常规卷/2:挑战卷/3:诊断卷")
    private Integer paperType;
    @Schema(description = "试卷名称")
    private String paperName;
    @Schema(description = "题目数量")
    private Integer questionCount;
    @Schema(description = "试卷总分")
    private BigDecimal totalScore;
    @Schema(description = "题目列表")
    private List<BigQuestionGroupDTO> questionGroups;

    public PaperSyncInfoDTO(PaperSyncInfo paperSyncInfo) {
        this.paperId = paperSyncInfo.getPaperId();
        this.versionNumber = paperSyncInfo.getVersionNumber();
        this.paperType = paperSyncInfo.getPaperType();
        this.paperName = paperSyncInfo.getPaperName();
        this.questionCount = paperSyncInfo.getQuestionCount();
        this.totalScore = paperSyncInfo.getTotalScore();

        if(CollectionUtils.isNotEmpty(paperSyncInfo.getQuestionGroups())) {
            this.questionGroups = paperSyncInfo.getQuestionGroups().stream()
                    .map(group->new BigQuestionGroupDTO(group, true))
                    .toList();
        }
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Integer getPaperType() {
        return paperType;
    }

    public void setPaperType(Integer paperType) {
        this.paperType = paperType;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public Integer getQuestionCount() {
        return questionCount;
    }

    public void setQuestionCount(Integer questionCount) {
        this.questionCount = questionCount;
    }

    public BigDecimal getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(BigDecimal totalScore) {
        this.totalScore = totalScore;
    }

    public List<BigQuestionGroupDTO> getQuestionGroups() {
        return questionGroups;
    }

    public void setQuestionGroups(List<BigQuestionGroupDTO> questionGroups) {
        this.questionGroups = questionGroups;
    }
}
