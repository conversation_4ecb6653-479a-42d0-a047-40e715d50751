package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.model.entity.paper.PaperVersion;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 试卷版本DTO
 */
@Schema(description = "试卷版本DTO")
public class PaperVersionDTO implements Serializable {

    @Schema(description = "教材ID")
    private String bookId;
    @Schema(description = "教材版本号")
    private String bookVersionNumber;
    @Schema(description = "试卷ID")
    private String paperId;
    @Schema(description = "试卷分类 1:常规卷/2:挑战卷/3:诊断卷")
    private Integer paperType;
    @Schema(description = "试卷版本号")
    private String versionNumber;
    @Schema(description = "试卷名称")
    private String paperName;

    public PaperVersionDTO(String bookId, String bookVersionNumber, PaperVersion paperVersion) {
        this.bookId = bookId;
        this.bookVersionNumber = bookVersionNumber;
        this.paperId = paperVersion.getPaperId();
        this.versionNumber = paperVersion.getVersionNumber();
        this.paperName = paperVersion.getPaperName();
        this.paperType = paperVersion.getPaperType();
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookVersionNumber() {
        return bookVersionNumber;
    }

    public void setBookVersionNumber(String bookVersionNumber) {
        this.bookVersionNumber = bookVersionNumber;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public Integer getPaperType() {
        return paperType;
    }

    public void setPaperType(Integer paperType) {
        this.paperType = paperType;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }
}
