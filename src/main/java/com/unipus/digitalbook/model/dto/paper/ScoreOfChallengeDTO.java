package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.model.dto.template.PaperScoreTemplateDetailDTO;
import com.unipus.digitalbook.model.entity.paper.UserPaperScore;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

@Schema(description = "挑战卷成绩")
public class ScoreOfChallengeDTO extends ScoreBaseDTO{
    @Schema(description = "用户本轮成绩")
    private BigDecimal userScore;
    @Schema(description = "本轮试卷总分")
    private BigDecimal standardScore;
    @Schema(description = "完成轮次数")
    private Integer completeRoundCount;
    @Schema(description = "累计作答题目数")
    private Integer totalQuestionCount;
    @Schema(description = "最高成绩")
    private BigDecimal bestScore;
    @Schema(description = "试卷评价模板详情信息")
    private PaperScoreTemplateDetailDTO paperScoreTemplateDetailDTO;

    // 挑战卷：本轮成绩/完成轮次数/累计作答题目数/最高成绩
    public ScoreOfChallengeDTO(UserPaperScore userPaperScore) {
        this.standardScore = userPaperScore.getStandardScore();
        this.userScore = userPaperScore.getUserScore();
        this.completeRoundCount = userPaperScore.getRoundCountInCurrentBatch();
        this.totalQuestionCount = userPaperScore.getTotalQuestionCountInCurrentBatch();
        this.bestScore = userPaperScore.getBestScore();
        // 获取试卷评价模板详情信息
        this.paperScoreTemplateDetailDTO = PaperScoreTemplateDetailDTO
                .assemblyPaperScoreTemplateDetailDTO(userPaperScore.getPaperScoreTemplateDetail());
    }

    public BigDecimal getUserScore() {
        return userScore;
    }

    public void setUserScore(BigDecimal userScore) {
        this.userScore = userScore;
    }

    public BigDecimal getStandardScore() {
        return standardScore;
    }

    public void setStandardScore(BigDecimal standardScore) {
        this.standardScore = standardScore;
    }

    public Integer getCompleteRoundCount() {
        return completeRoundCount;
    }

    public void setCompleteRoundCount(Integer completeRoundCount) {
        this.completeRoundCount = completeRoundCount;
    }

    public Integer getTotalQuestionCount() {
        return totalQuestionCount;
    }

    public void setTotalQuestionCount(Integer totalQuestionCount) {
        this.totalQuestionCount = totalQuestionCount;
    }

    public BigDecimal getBestScore() {
        return bestScore;
    }

    public void setBestScore(BigDecimal bestScore) {
        this.bestScore = bestScore;
    }

    public PaperScoreTemplateDetailDTO getPaperScoreTemplateDetailDTO() {
        return paperScoreTemplateDetailDTO;
    }

    public void setPaperScoreTemplateDetailDTO(PaperScoreTemplateDetailDTO paperScoreTemplateDetailDTO) {
        this.paperScoreTemplateDetailDTO = paperScoreTemplateDetailDTO;
    }
}
