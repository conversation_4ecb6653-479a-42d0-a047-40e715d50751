package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.model.dto.template.PaperScoreTemplateDetailDTO;
import com.unipus.digitalbook.model.entity.paper.UserPaperScore;
import com.unipus.digitalbook.model.entity.paper.UserQuestionScore;
import com.unipus.digitalbook.model.enums.PaperSubmitStatusEnum;
import com.unipus.digitalbook.model.enums.TagLevelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.math.BigDecimal;
import java.util.*;

@Schema(description = "诊断卷成绩")
public class ScoreOfDiagnosticDTO extends ScoreBaseDTO{
    @Schema(description = "用户最终得分")
    private BigDecimal userScore;
    @Schema(description = "试卷总分")
    private BigDecimal standardScore;
    @Schema(description = "正确率(客观题)")
    private BigDecimal correctRate;
    @Schema(description = "得分率(客观题)")
    private BigDecimal scoringRate;
    @Schema(description = "用于试卷得分与班级平均水平相比")
    private String scoreLevel = "等于";
    @Schema(description = "客观题正确题目数")
    private Integer correctCount;
    @Schema(description = "错误题目数(客观题)")
    private Integer incorrectCount;
    @Schema(description = "客观题题目总数")
    private Integer totalCount;
    @Schema(description = "一级技能点成绩信息列表")
    private List<UserPaperKnowledgeScoreDTO> firstLevelKnowledgeScores;
    @Schema(description = "二级技能点成绩信息列表")
    private List<UserPaperKnowledgeScoreDTO> secondLevelKnowledgeScores;
    @Schema(description = "正确题技能点列表")
    private List<String> correctKnowledgePoints;
    @Schema(description = "推荐题技能点列表(作答错误的题目关联的技能点)")
    private List<String> recommendedKnowledgePoints;
    @Schema(description = "推荐卷实例ID")
    private String recommendedPaperInstanceId;
    @Schema(description = "推荐题作答错误的题目关联的技能点列表")
    private List<String> incorrectRecommendedKnowledgePoints;
    @Schema(description = "推荐卷提交状态")
    private Integer recommendSubmitStatus;
    @Schema(description = "试卷评价模板详情信息")
    private PaperScoreTemplateDetailDTO paperScoreTemplateDetailDTO;

    public ScoreOfDiagnosticDTO(UserPaperScore userPaperScore) {
        Integer objectiveCorrectCount = userPaperScore.fetchCorrectObjectiveSmallQuestionCount();
        Integer objectiveTotalCount = userPaperScore.fetchTotalObjectiveSmallQuestionCount();

        this.userScore = userPaperScore.getUserScore();
        this.standardScore = userPaperScore.getStandardScore();
        this.correctCount = objectiveCorrectCount;
        this.incorrectCount = objectiveTotalCount - objectiveCorrectCount;
        this.totalCount = userPaperScore.getUserQuestionScores().size();
        // 计算正确率
        this.correctRate = super.calculateCorrectRate(objectiveCorrectCount, objectiveTotalCount);
        // 计算试卷用户得分率
        BigDecimal paperQuestionUserScore = userPaperScore.fetchUserSmallQuestionScore();
        BigDecimal paperQuestionTotalScore = userPaperScore.fetchTotalSmallQuestionScore();
        this.scoringRate = super.calculateScoringRate(paperQuestionUserScore, paperQuestionTotalScore);
        // 获取一级技能点成绩信息列表：按一级技能点统计分数/用户最终得分/正确率
        this.firstLevelKnowledgeScores = queryKnowledgePointScores(userPaperScore, TagLevelEnum.FIRST);
        // 获取二级技能点成绩信息列表：按二级技能点统计正确/错误映射列表
        this.secondLevelKnowledgeScores = queryKnowledgePointScores(userPaperScore, TagLevelEnum.SECOND);
        // 推荐卷实例ID
        this.recommendedPaperInstanceId = userPaperScore.getRecommendInstanceId();
        // 推荐技能点
        this.incorrectRecommendedKnowledgePoints = userPaperScore.getIncorrectRecommendTagNames();
        // 推荐卷提交状态
        this.recommendSubmitStatus = Optional.ofNullable(userPaperScore.getRecommendSubmitStatus()).orElse(PaperSubmitStatusEnum.UNSUBMITTED).getCode();
        // 获取试卷评价模板详情信息
        this.paperScoreTemplateDetailDTO = PaperScoreTemplateDetailDTO
                .assemblyPaperScoreTemplateDetailDTO(userPaperScore.getPaperScoreTemplateDetail());
        // 合并处理二级技能点
        mergeSecondLevelKnowledgePointScores();
    }

    // 合并处理二级技能点
    private void mergeSecondLevelKnowledgePointScores() {
        if(CollectionUtils.isEmpty(this.secondLevelKnowledgeScores)){
            return;
        }

        this.correctKnowledgePoints = new ArrayList<>();
        this.recommendedKnowledgePoints = new ArrayList<>();
        // 对技能点进行分组处理
        this.secondLevelKnowledgeScores.forEach(knowledgeScore -> {
            String point = knowledgeScore.getKnowledgePoint();
            if(!knowledgeScore.getCorrect() && !this.recommendedKnowledgePoints.contains(point)){
                // 追加错误的不重复的技能点
                this.recommendedKnowledgePoints.add(knowledgeScore.getKnowledgePoint());
            }
            if(knowledgeScore.getCorrect() && !this.recommendedKnowledgePoints.contains(point) &&
                    !this.correctKnowledgePoints.contains(point)){
                // 追加正确的且不在错误列表中的不重复的技能点
                this.correctKnowledgePoints.add(knowledgeScore.getKnowledgePoint());
            }
        });

        if(CollectionUtils.isEmpty(this.recommendedKnowledgePoints)){
            // 没有推荐题关联的技能点
            return;
        }
        if(CollectionUtils.isEmpty(this.incorrectRecommendedKnowledgePoints)){
            // 没有推荐题关联的错误技能点
            return;
        }
        // 更新推荐题关联技能点列表(删除仍然错误的推荐题技能点，作为推荐提正确技能点列表)
        this.recommendedKnowledgePoints.removeIf(point->this.incorrectRecommendedKnowledgePoints.contains(point));
    }

    /**
     * 获取一级/二级技能点成绩信息列表
     * @param userPaperScore 用户试卷得分信息
     * @param level 技能点等级（1:一级/2:二级）
     * @return 一级技能点成绩信息列表
     */
    private List<UserPaperKnowledgeScoreDTO> queryKnowledgePointScores(UserPaperScore userPaperScore, TagLevelEnum level) {
        if(CollectionUtils.isEmpty(userPaperScore.getTags())){
            return List.of();
        }
        // 取得关联的知识点（标签）列表
        LinkedHashMap<String, List<String>> knowledgePointMaps = userPaperScore.getTagNameMapByLevel(level);
        // 获取用户小题得分列表
        List<UserQuestionScore> userQuestionScores = userPaperScore.getUserQuestionScores();
        if(MapUtils.isEmpty(knowledgePointMaps) || CollectionUtils.isEmpty(userQuestionScores)){
            return List.of();
        }

        // 遍历技能点与小题ID列表映射(key:技能点，value:小题ID列表)
        List<UserPaperKnowledgeScoreDTO> knowledgeScores = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : knowledgePointMaps.entrySet()) {
            String point = entry.getKey();
            List<String> smallIds = entry.getValue();
            List<UserQuestionScore> smallScores = userQuestionScores.stream()
                    .filter(score -> smallIds.contains(score.getSmallQuestionBizId())).toList();
            if(CollectionUtils.isEmpty(smallScores)){
                continue;
            }

            // 用户小题得分
            BigDecimal userTotalScore = BigDecimal.ZERO;
            // 试卷标准分
            BigDecimal standardTotalScore = BigDecimal.ZERO;
            // 正确的客观题数量（并且判题的小题数量）
            int correctObjectiveCount = 0;
            // 客观题总数（并且判题的小题数量）
            int totalObjectiveCount = 0;

            // 计算该知识点下所有小题得分
            for (UserQuestionScore score : smallScores) {
                // 用户得分
                userTotalScore = userTotalScore.add(score.getUserScore());
                // 标准分
                standardTotalScore = standardTotalScore.add(score.getStandardScore());
                // 客观题并且判题
                if(score.getObjective() && score.getJudged()) {
                    // 正确的客观题数量
                    correctObjectiveCount += (score.getIsCorrect() ? 1 : 0);
                    // 客观题总数
                    totalObjectiveCount += 1;
                 }
            }

            // 判断有效题目数量(如果存在有效题目则输出该技能点信息)
            if(totalObjectiveCount > 0){
                // 计算该知识点的正确率(客观题)
                BigDecimal localCorrectRate = super.calculateCorrectRate(correctObjectiveCount, totalObjectiveCount);
                BigDecimal localScoringRate = super.calculateScoringRate(userTotalScore, standardTotalScore);
                // 创建技能点成绩信息
                knowledgeScores.add(new UserPaperKnowledgeScoreDTO(point, userTotalScore, standardTotalScore, localCorrectRate, localScoringRate));
            }
        }
        return knowledgeScores;
    }

    public BigDecimal getUserScore() {
        return userScore;
    }

    public void setUserScore(BigDecimal userScore) {
        this.userScore = userScore;
    }

    public BigDecimal getStandardScore() {
        return standardScore;
    }

    public void setStandardScore(BigDecimal standardScore) {
        this.standardScore = standardScore;
    }

    public BigDecimal getCorrectRate() {
        return correctRate;
    }

    public void setCorrectRate(BigDecimal correctRate) {
        this.correctRate = correctRate;
    }

    public BigDecimal getScoringRate() {
        return scoringRate;
    }

    public void setScoringRate(BigDecimal scoringRate) {
        this.scoringRate = scoringRate;
    }

    public String getScoreLevel() {
        return scoreLevel;
    }

    public void setScoreLevel(String scoreLevel) {
        this.scoreLevel = scoreLevel;
    }

    public Integer getCorrectCount() {
        return correctCount;
    }

    public void setCorrectCount(Integer correctCount) {
        this.correctCount = correctCount;
    }

    public Integer getIncorrectCount() {
        return incorrectCount;
    }

    public void setIncorrectCount(Integer incorrectCount) {
        this.incorrectCount = incorrectCount;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public List<UserPaperKnowledgeScoreDTO> getFirstLevelKnowledgeScores() {
        return firstLevelKnowledgeScores;
    }

    public void setFirstLevelKnowledgeScores(List<UserPaperKnowledgeScoreDTO> firstLevelKnowledgeScores) {
        this.firstLevelKnowledgeScores = firstLevelKnowledgeScores;
    }

    public List<UserPaperKnowledgeScoreDTO> getSecondLevelKnowledgeScores() {
        return secondLevelKnowledgeScores;
    }

    public void setSecondLevelKnowledgeScores(List<UserPaperKnowledgeScoreDTO> secondLevelKnowledgeScores) {
        this.secondLevelKnowledgeScores = secondLevelKnowledgeScores;
    }

    public List<String> getCorrectKnowledgePoints() {
        return correctKnowledgePoints;
    }

    public void setCorrectKnowledgePoints(List<String> correctKnowledgePoints) {
        this.correctKnowledgePoints = correctKnowledgePoints;
    }

    public List<String> getRecommendedKnowledgePoints() {
        return recommendedKnowledgePoints;
    }

    public void setRecommendedKnowledgePoints(List<String> recommendedKnowledgePoints) {
        this.recommendedKnowledgePoints = recommendedKnowledgePoints;
    }

    public String getRecommendedPaperInstanceId() {
        return recommendedPaperInstanceId;
    }

    public void setRecommendedPaperInstanceId(String recommendedPaperInstanceId) {
        this.recommendedPaperInstanceId = recommendedPaperInstanceId;
    }

    public List<String> getIncorrectRecommendedKnowledgePoints() {
        return incorrectRecommendedKnowledgePoints;
    }

    public void setIncorrectRecommendedKnowledgePoints(List<String> incorrectRecommendedKnowledgePoints) {
        this.incorrectRecommendedKnowledgePoints = incorrectRecommendedKnowledgePoints;
    }

    public Integer getRecommendSubmitStatus() {
        return recommendSubmitStatus;
    }

    public void setRecommendSubmitStatus(Integer recommendSubmitStatus) {
        this.recommendSubmitStatus = recommendSubmitStatus;
    }

    public PaperScoreTemplateDetailDTO getPaperScoreTemplateDetailDTO() {
        return paperScoreTemplateDetailDTO;
    }

    public void setPaperScoreTemplateDetailDTO(PaperScoreTemplateDetailDTO paperScoreTemplateDetailDTO) {
        this.paperScoreTemplateDetailDTO = paperScoreTemplateDetailDTO;
    }
}
