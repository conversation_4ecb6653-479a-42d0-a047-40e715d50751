package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.model.entity.paper.UserPaperScore;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

@Schema(description = "常规卷成绩")
public class ScoreOfRegularDTO extends ScoreBaseDTO{
    @Schema(description = "用户分数")
    private BigDecimal userScore;
    @Schema(description = "试卷总分")
    private BigDecimal standardScore;
    @Schema(description = "正确题目数(客观题)")
    private Integer correctCount;
    @Schema(description = "错误题目数(客观题)")
    private Integer incorrectCount;
    @Schema(description = "所有题目总数(包含主观客观题)")
    private Integer totalCount;
    @Schema(description = "正确率(客观题)")
    private BigDecimal correctRate;
    @Schema(description = "得分率(客观题)")
    private BigDecimal scoringRate;

    // 常规卷：用户分数/试卷总分/正确题目数/题目总数/正确率
    public ScoreOfRegularDTO(UserPaperScore userPaperScore) {
        Integer objectiveCorrectCount = userPaperScore.fetchCorrectObjectiveSmallQuestionCount();
        Integer objectiveTotalCount = userPaperScore.fetchTotalObjectiveSmallQuestionCount();

        this.userScore = userPaperScore.getUserScore();
        this.standardScore = userPaperScore.getStandardScore();
        this.correctCount = objectiveCorrectCount;
        this.incorrectCount = objectiveTotalCount - objectiveCorrectCount;
        this.totalCount = userPaperScore.getUserQuestionScores().size();
        this.correctRate = super.calculateCorrectRate(objectiveCorrectCount, objectiveTotalCount);

        BigDecimal paperQuestionUserScore = userPaperScore.fetchUserSmallQuestionScore();
        BigDecimal paperQuestionTotalScore = userPaperScore.fetchTotalSmallQuestionScore();
        this.scoringRate = super.calculateScoringRate(paperQuestionUserScore, paperQuestionTotalScore);
    }

    public BigDecimal getUserScore() {
        return userScore;
    }

    public void setUserScore(BigDecimal userScore) {
        this.userScore = userScore;
    }

    public BigDecimal getStandardScore() {
        return standardScore;
    }

    public void setStandardScore(BigDecimal standardScore) {
        this.standardScore = standardScore;
    }

    public Integer getCorrectCount() {
        return correctCount;
    }

    public void setCorrectCount(Integer correctCount) {
        this.correctCount = correctCount;
    }

    public Integer getIncorrectCount() {
        return incorrectCount;
    }

    public void setIncorrectCount(Integer incorrectCount) {
        this.incorrectCount = incorrectCount;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getCorrectRate() {
        return correctRate;
    }

    public void setCorrectRate(BigDecimal correctRate) {
        this.correctRate = correctRate;
    }

    public BigDecimal getScoringRate() {
        return scoringRate;
    }

    public void setScoringRate(BigDecimal scoringRate) {
        this.scoringRate = scoringRate;
    }
}
