package com.unipus.digitalbook.model.dto.paper;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

@Schema(description = "知识点成绩DTO")
public class UserPaperKnowledgeScoreDTO {
    @Schema(description = "知识点")
    private String knowledgePoint;
    @Schema(description = "用户得分")
    private BigDecimal userScore;
    @Schema(description = "用户失分")
    private BigDecimal loseScore;
    @Schema(description = "标准分")
    private BigDecimal standardScore;
    @Schema(description = "正确率(客观题)")
    private BigDecimal correctRate;
    @Schema(description = "得分率(客观题)")
    private BigDecimal scoringRate;
    @Schema(description = "知识点是否完全正确")
    private Boolean isCorrect;

    public UserPaperKnowledgeScoreDTO(String knowledgePoint, BigDecimal userScore, BigDecimal standardScore, BigDecimal correctRate, BigDecimal scoringRate){
        this.knowledgePoint = knowledgePoint;
        this.userScore = userScore;
        this.standardScore = standardScore;
        this.correctRate = correctRate;
        this.scoringRate = scoringRate;
        this.loseScore = standardScore!=null && userScore!=null ? standardScore.subtract(userScore) : BigDecimal.ZERO;
        this.isCorrect = correctRate != null && correctRate.equals(BigDecimal.valueOf(100.0));
    }

    public String getKnowledgePoint() {
        return knowledgePoint;
    }

    public void setKnowledgePoint(String knowledgePoint) {
        this.knowledgePoint = knowledgePoint;
    }

    public BigDecimal getUserScore() {
        return userScore;
    }

    public void setUserScore(BigDecimal userScore) {
        this.userScore = userScore;
    }

    public BigDecimal getLoseScore() {
        return loseScore;
    }

    public void setLoseScore(BigDecimal loseScore) {
        this.loseScore = loseScore;
    }

    public BigDecimal getStandardScore() {
        return standardScore;
    }

    public void setStandardScore(BigDecimal standardScore) {
        this.standardScore = standardScore;
    }

    public BigDecimal getCorrectRate() {
        return correctRate;
    }

    public void setCorrectRate(BigDecimal correctRate) {
        this.correctRate = correctRate;
    }

    public BigDecimal getScoringRate() {
        return scoringRate;
    }

    public void setScoringRate(BigDecimal scoringRate) {
        this.scoringRate = scoringRate;
    }

    public Boolean getCorrect() {
        return isCorrect;
    }

    public void setCorrect(Boolean correct) {
        isCorrect = correct;
    }
}
