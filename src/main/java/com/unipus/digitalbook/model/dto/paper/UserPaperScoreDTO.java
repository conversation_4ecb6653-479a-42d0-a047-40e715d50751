package com.unipus.digitalbook.model.dto.paper;

import com.unipus.digitalbook.common.utils.ScoreUtil;
import com.unipus.digitalbook.model.entity.paper.UserPaperInfo;
import com.unipus.digitalbook.model.entity.paper.UserPaperScore;
import com.unipus.digitalbook.model.entity.paper.UserQuestionScore;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.enums.ScoreTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Schema(description = "用户试卷作答记录DTO")
public class UserPaperScoreDTO implements Serializable {

    @Schema(description = "试卷ID")
    private String paperId;
    @Schema(description = "试卷版本")
    private String versionNumber;
    @Schema(description = "试卷类型")
    private Integer paperType;
    @Schema(description = "成绩提交批次ID")
    private String scoreBatchId;
    @Schema(description = "试卷实例ID/轮次ID")
    private String instanceId;
    @Schema(description = "试卷成绩提交状态")
    private Integer status;
    @Schema(description = "用户得分")
    private BigDecimal userScore;
    @Schema(description = "试卷标准分")
    private BigDecimal standardScore;
    @Schema(description = "试卷成绩对象")
    private ScoreBaseDTO paperScore;

    public UserPaperScoreDTO(UserPaperInfo userPaperInfo){
        this.paperId = userPaperInfo.getPaperId();
        this.versionNumber = userPaperInfo.getVersionNumber();
        this.paperType = userPaperInfo.getPaperType()==null ? null : userPaperInfo.getPaperType().getCode();
        this.scoreBatchId = userPaperInfo.getScoreBatchId();
        this.instanceId = userPaperInfo.getInstanceId();
        this.status = userPaperInfo.getStatus();
        this.userScore = userPaperInfo.getUserScore();
        this.standardScore = userPaperInfo.getStandardScore();
    }

    public UserPaperScoreDTO(UserPaperScore userPaperScore){
        // 基础信息设置
        this.paperType = userPaperScore.getPaperType().getCode();
        this.scoreBatchId = userPaperScore.getScoreBatchId();
        this.instanceId = userPaperScore.getInstanceId();
        this.status = userPaperScore.getSubmitStatus()==null ? null : userPaperScore.getSubmitStatus().getCode();
        // 根据试卷类型构建对应的成绩对象
        this.paperScore = switch (PaperTypeEnum.getByCode(this.paperType)) {
            case PaperTypeEnum.REGULAR -> new ScoreOfRegularDTO(userPaperScore);
            case PaperTypeEnum.CHALLENGE -> new ScoreOfChallengeDTO(userPaperScore);
            case PaperTypeEnum.DIAGNOSTIC -> new ScoreOfDiagnosticDTO(userPaperScore);
        };
    }

    public static UserPaperScoreDTO build(UserPaperScore userPaperScore, ScoreTypeEnum scoreType) {
        if(userPaperScore==null) {
            return null;
        }
        if (ScoreTypeEnum.PERCENTAGE.match(scoreType)) {
            // 变换试卷及题目得分类型
            convertUserPaperScoreToPercentageScore(userPaperScore);
        }else{
            // 保留2位有效位数
            setDigitOfScore(userPaperScore);
        }

        return new UserPaperScoreDTO(userPaperScore);
    }

    // 设置试卷及题目得分保留2位有效位数
    private static void setDigitOfScore(UserPaperScore userPaperScore) {
        // 试卷得分有效位数
        userPaperScore.setUserScore(ScoreUtil.keepTwoDecimal(userPaperScore.getUserScore()));
        // 标准分有效位数
        userPaperScore.setStandardScore(ScoreUtil.keepTwoDecimal(userPaperScore.getStandardScore()));
        // 最高得分有效位数
        userPaperScore.setBestScore(ScoreUtil.keepTwoDecimal(userPaperScore.getBestScore()));
        // 用户得分题目
        List<UserQuestionScore> userQuestionScores = userPaperScore.getUserQuestionScores();
        if(CollectionUtils.isNotEmpty(userQuestionScores)) {
            userQuestionScores.forEach(uqs -> {
                uqs.setUserScore(ScoreUtil.keepTwoDecimal(uqs.getUserScore()));
                uqs.setStandardScore(ScoreUtil.keepTwoDecimal(uqs.getStandardScore()));
            });
        }
    }

    // 变换试卷及题目得分类型
    private static void convertUserPaperScoreToPercentageScore(UserPaperScore userPaperScore){
        // 百分制基准分
        BigDecimal total = userPaperScore.getStandardScore();
        // 标准分
        userPaperScore.setStandardScore(BigDecimal.valueOf(100));
        // 用户得分
        userPaperScore.setUserScore(ScoreUtil.keepOneDecimalForPercent(total, userPaperScore.getUserScore()));
        // 最高得分
        userPaperScore.setBestScore(ScoreUtil.keepOneDecimalForPercent(total, userPaperScore.getBestScore()));
        // 用户得分题目
        List<UserQuestionScore> userQuestionScores = userPaperScore.getUserQuestionScores();
        if(CollectionUtils.isNotEmpty(userQuestionScores)) {
            userQuestionScores.forEach(uqs -> {
                uqs.setUserScore(ScoreUtil.keepOneDecimalForPercent(total, uqs.getUserScore()));
                uqs.setStandardScore(ScoreUtil.keepOneDecimalForPercent(total, uqs.getStandardScore()));
            });
        }
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Integer getPaperType() {
        return paperType;
    }

    public void setPaperType(Integer paperType) {
        this.paperType = paperType;
    }

    public String getScoreBatchId() {
        return scoreBatchId;
    }

    public void setScoreBatchId(String scoreBatchId) {
        this.scoreBatchId = scoreBatchId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public ScoreBaseDTO getPaperScore() {
        return paperScore;
    }

    public void setPaperScore(ScoreBaseDTO paperScore) {
        this.paperScore = paperScore;
    }

    public BigDecimal getUserScore() {
        return userScore;
    }

    public void setUserScore(BigDecimal userScore) {
        this.userScore = userScore;
    }

    public BigDecimal getStandardScore() {
        return standardScore;
    }

    public void setStandardScore(BigDecimal standardScore) {
        this.standardScore = standardScore;
    }
}
