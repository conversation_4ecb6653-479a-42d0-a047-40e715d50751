package com.unipus.digitalbook.model.dto.publish;

import com.unipus.digitalbook.model.entity.book.BookBasic;
import com.unipus.digitalbook.model.entity.book.BookCopyright;
import com.unipus.digitalbook.model.entity.book.BookIntro;
import com.unipus.digitalbook.model.entity.book.BookNode;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.complement.ComplementResource;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.enums.PublishContentTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Map;

@Schema(description = "教材上架版本详情资源资源实体类")
public class BookPublishedDetailResourceDTO implements Serializable {

    @Schema(description = "教材ID")
    private String bookId;

    @Schema(description = "资源类型名称: 1：教材基本信息/2：教材简介/3：版权信息/4：配套资源/5：教材章节/6：试卷")
    private String typeName;

    @Schema(description = "资源类型编码: 1：教材基本信息/2：教材简介/3：版权信息/4：配套资源/5：教材章节/6：试卷")
    private Integer typeCode;

    @Schema(description = "当前版本教材节点的版本id")
    private Long currentVersionId;

    @Schema(description = "前一版本教材节点的版本id")
    private Long previousVersionId;

    @Schema(description = "当前版本的上架版本")
    private String currentVersionNumber;

    @Schema(description = "前一版本的上架版本")
    private String previousVersionNumber;

    @Schema(description = "变更标识 0:未变更/1:新增/2:更新/3:删除")
    private Integer changeFlag;

    @Schema(description = "教材节点的资源id")
    private String resourceId;

    @Schema(description = "当前版本教材节点的资源名称")
    private String currentResourceName;

    @Schema(description = "前一版本教材节点的资源名称")
    private String previousResourceName;

    @Schema(description = "教材节点的资源属性")
    private Map<String, Object> property;

    public BookPublishedDetailResourceDTO(String bookId, PublishContentTypeEnum contentType, BookNode currentBookNode, BookNode previousBookNode) {
        this.bookId = bookId;
        this.typeName = contentType.getDesc();
        this.typeCode = contentType.getCode();
        if (currentBookNode == null && previousBookNode == null) {
            return;
        }
        // 处理不同类型的节点
        setPropertiesByContentType(contentType, currentBookNode, previousBookNode);
    }

    /**
     * 根据内容类型设置特定属性
     */
    private void setPropertiesByContentType(PublishContentTypeEnum contentType, BookNode currentBookNode, BookNode previousBookNode) {
        if (PublishContentTypeEnum.BASIC_INFO.match(contentType.getCode())) {
            BookBasic current = currentBookNode instanceof BookBasic ? (BookBasic) currentBookNode : null;
            BookBasic previous = previousBookNode instanceof BookBasic ? (BookBasic) previousBookNode : null;
            if (current != null) {
                this.currentVersionId = current.getId();
                this.currentVersionNumber = current.getVersionNumber();
            }
            if (previous != null) {
                this.previousVersionId = previous.getId();
                this.previousVersionNumber = previous.getVersionNumber();
            }
        } else if (PublishContentTypeEnum.BOOK_INTRO.match(contentType.getCode())) {
            BookIntro current = currentBookNode instanceof BookIntro ? (BookIntro) currentBookNode : null;
            BookIntro previous = previousBookNode instanceof BookIntro ? (BookIntro) previousBookNode : null;
            if (current != null) {
                this.currentVersionId = current.getId();
                this.currentVersionNumber = current.getVersionNumber();
            }
            if (previous != null) {
                this.previousVersionId = previous.getId();
                this.previousVersionNumber = previous.getVersionNumber();
            }
        } else if (PublishContentTypeEnum.COPYRIGHT_INFO.match(contentType.getCode())) {
            BookCopyright current = currentBookNode instanceof BookCopyright ? (BookCopyright) currentBookNode : null;
            BookCopyright previous = previousBookNode instanceof BookCopyright ? (BookCopyright) previousBookNode : null;
            if (current != null) {
                this.currentVersionId = current.getId();
                this.currentVersionNumber = current.getVersionNumber();
            }
            if (previous != null) {
                this.previousVersionId = previous.getId();
                this.previousVersionNumber = previous.getVersionNumber();
            }
        } else if (PublishContentTypeEnum.COMPLEMENT_RESOURCE.match(contentType.getCode())) {
            ComplementResource current = currentBookNode instanceof ComplementResource ? (ComplementResource) currentBookNode : null;
            ComplementResource previous = previousBookNode instanceof ComplementResource ? (ComplementResource) previousBookNode : null;
            if (current != null) {
                this.currentVersionId = current.getId();
                this.currentVersionNumber = current.getVersionNumber();
                this.resourceId = current.getResourceId();
                this.currentResourceName = current.getName();
            }
            if (previous != null) {
                this.previousVersionId = previous.getId();
                this.previousVersionNumber = previous.getVersionNumber();
                if (this.resourceId == null) this.resourceId = previous.getResourceId();
                this.previousResourceName = previous.getName();
            }
        } else if (PublishContentTypeEnum.CHAPTER_CONTENT.match(contentType.getCode())) {
            Chapter current = currentBookNode instanceof Chapter ? (Chapter) currentBookNode : null;
            Chapter previous = previousBookNode instanceof Chapter ? (Chapter) previousBookNode : null;
            if (current != null) {
                this.currentVersionId = current.getChapterVersion() != null ? current.getChapterVersion().getId() : null;
                this.currentVersionNumber = current.getChapterVersion() != null ? current.getChapterVersion().getVersionNumber() : null;
                this.resourceId = current.getId();
                this.currentResourceName = current.getName();
            }
            if (previous != null) {
                this.previousVersionId = previous.getChapterVersion() != null ? previous.getChapterVersion().getId() : null;
                this.previousVersionNumber = previous.getChapterVersion() != null ? previous.getChapterVersion().getVersionNumber() : null;
                if (this.resourceId == null) this.resourceId = previous.getId();
                this.previousResourceName = previous.getName();
            }
        } else if (PublishContentTypeEnum.PAPER.match(contentType.getCode())) {
            Paper current = currentBookNode instanceof Paper ? (Paper) currentBookNode : null;
            Paper previous = previousBookNode instanceof Paper ? (Paper) previousBookNode : null;
            if (current != null) {
                this.currentVersionId = current.getId();
                this.currentVersionNumber = current.getVersionNumber();
                this.resourceId = current.getPaperId();
                this.currentResourceName = current.getPaperName();
            }
            if (previous != null) {
                this.previousVersionId = previous.getId();
                this.previousVersionNumber = previous.getVersionNumber();
                if (this.resourceId == null) this.resourceId = previous.getPaperId();
                this.previousResourceName = previous.getPaperName();
            }
        }
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(Integer typeCode) {
        this.typeCode = typeCode;
    }

    public Long getCurrentVersionId() {
        return currentVersionId;
    }

    public void setCurrentVersionId(Long currentVersionId) {
        this.currentVersionId = currentVersionId;
    }

    public Long getPreviousVersionId() {
        return previousVersionId;
    }

    public void setPreviousVersionId(Long previousVersionId) {
        this.previousVersionId = previousVersionId;
    }

    public String getCurrentVersionNumber() {
        return currentVersionNumber;
    }

    public void setCurrentVersionNumber(String currentVersionNumber) {
        this.currentVersionNumber = currentVersionNumber;
    }

    public String getPreviousVersionNumber() {
        return previousVersionNumber;
    }

    public void setPreviousVersionNumber(String previousVersionNumber) {
        this.previousVersionNumber = previousVersionNumber;
    }

    public Integer getChangeFlag() {
        return changeFlag;
    }

    public void setChangeFlag(Integer changeFlag) {
        this.changeFlag = changeFlag;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getCurrentResourceName() {
        return currentResourceName;
    }

    public void setCurrentResourceName(String currentResourceName) {
        this.currentResourceName = currentResourceName;
    }

    public String getPreviousResourceName() {
        return previousResourceName;
    }

    public void setPreviousResourceName(String previousResourceName) {
        this.previousResourceName = previousResourceName;
    }

    public Map<String, Object> getProperty() {
        return property;
    }

    public void setProperty(Map<String, Object> property) {
        this.property = property;
    }
}
