package com.unipus.digitalbook.model.dto.publish;

import com.unipus.digitalbook.model.entity.publish.BookPublishedVersionList;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;

@Schema(description = "教材已发布版本列表数据返回对象")
public class BookPublishedVersionListDTO implements Serializable {

    @Schema(description = "教材已发布版本列表")
    private List<BookPublishedVersionDTO> versionList;

    @Schema(description = "总数量")
    private Integer totalCount;

    public BookPublishedVersionListDTO(BookPublishedVersionList bookPublishedVersionList) {
        if (bookPublishedVersionList == null) {
            return;
        }
        Optional.ofNullable(bookPublishedVersionList.getVersionList())
                .ifPresent(list -> this.versionList = list.stream()
                        .map(BookPublishedVersionDTO::new)
                        .toList());
        this.totalCount = bookPublishedVersionList.getTotalCount();
    }

    public List<BookPublishedVersionDTO> getVersionList() {
        return versionList;
    }

    public void setVersionList(List<BookPublishedVersionDTO> versionList) {
        this.versionList = versionList;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }
}
