package com.unipus.digitalbook.model.dto.publish;

import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.enums.PublishContentTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Schema(description = "教材章节版本发布检测实体类")
public class ChapterPublishCheckDTO implements Serializable {

    @Schema(description = "版本ID")
    private Long versionId;

    @Schema(description = "所属章节ID")
    private String resourceId;

    @Schema(description = "版本号")
    private String versionNumber;

    @Schema(description = "保存时间")
    private Long saveTime;

    @Schema(description = "是否上架")
    private Boolean publishFlag;

    @Schema(description = "上架时间")
    private Long publishTime;

    @Schema(description = "资源发布列表")
    private List<PublishCheckResourceDTO> publishResources;

    public ChapterPublishCheckDTO(ChapterVersion chapterVersion, Date publishTime, List<Paper> currentChapterPaperList, List<Paper> publishedChapterPaperList) {
        this.versionId = chapterVersion.getId();
        this.resourceId = chapterVersion.getChapterId();
        this.versionNumber = chapterVersion.getVersionNumber();
        this.saveTime = chapterVersion.getCreateTime() != null ? chapterVersion.getCreateTime().getTime() : null;
        this.publishFlag = publishTime != null;
        this.publishTime = publishTime != null ? publishTime.getTime() : null;
        this.publishResources = processChapterPapers(chapterVersion.getChapterId(), currentChapterPaperList, publishedChapterPaperList);
    }

    /**
     * 处理章节中的试卷信息
     *
     * @param bookId                    教材ID
     * @param currentChapterPaperList   当前版本章节试卷列表
     * @param publishedChapterPaperList 已发布章节试卷列表
     * @return
     */
    private List<PublishCheckResourceDTO> processChapterPapers(String bookId,
                                                               List<Paper> currentChapterPaperList,
                                                               List<Paper> publishedChapterPaperList) {
        // 构建已发布试卷映射
        Map<String, Paper> publishedPaperMap = new HashMap<>();
        if (publishedChapterPaperList != null) {
            publishedPaperMap = publishedChapterPaperList.stream()
                    .filter(paper -> paper != null && paper.getPaperId() != null)
                    .collect(Collectors.toMap(Paper::getPaperId, paper -> paper));
        }
        List<PublishCheckResourceDTO> paperResourceList = new ArrayList<>();
        // 添加试卷资源
        if (currentChapterPaperList != null) {
            for (Paper paper : currentChapterPaperList) {
                Paper publishedPaper = publishedPaperMap.get(paper.getPaperId());
                String paperVersion = publishedPaper != null ? publishedPaper.getVersionNumber() : null;

                PublishCheckResourceDTO resource = new PublishCheckResourceDTO(bookId,
                        PublishContentTypeEnum.PAPER,
                        paper,
                        paperVersion);
                paperResourceList.add(resource);
            }
        }
        return paperResourceList;
    }

    public Long getVersionId() {
        return versionId;
    }

    public void setVersionId(Long versionId) {
        this.versionId = versionId;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Long getSaveTime() {
        return saveTime;
    }

    public void setSaveTime(Long saveTime) {
        this.saveTime = saveTime;
    }

    public Boolean getPublishFlag() {
        return publishFlag;
    }

    public void setPublishFlag(Boolean publishFlag) {
        this.publishFlag = publishFlag;
    }

    public Long getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Long publishTime) {
        this.publishTime = publishTime;
    }

    public List<PublishCheckResourceDTO> getPublishResources() {
        return publishResources;
    }

    public void setPublishResources(List<PublishCheckResourceDTO> publishResources) {
        this.publishResources = publishResources;
    }
}
