package com.unipus.digitalbook.model.dto.publish;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "教材章节版本发布检测列表数据返回对象")
public class ChapterPublishCheckListDTO implements Serializable {

    @Schema(description = "教材章节版本发布检测列表")
    private List<ChapterPublishCheckDTO> versionList;

    @Schema(description = "总数量")
    private Integer totalCount;

    public ChapterPublishCheckListDTO(List<ChapterPublishCheckDTO> versionList, Integer totalCount) {
        this.versionList = versionList;
        this.totalCount = totalCount;
    }

    public List<ChapterPublishCheckDTO> getVersionList() {
        return versionList;
    }

    public void setVersionList(List<ChapterPublishCheckDTO> versionList) {
        this.versionList = versionList;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }
}
