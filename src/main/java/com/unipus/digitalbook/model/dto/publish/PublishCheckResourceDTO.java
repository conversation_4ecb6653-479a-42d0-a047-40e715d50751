package com.unipus.digitalbook.model.dto.publish;

import com.unipus.digitalbook.model.entity.book.BookBasic;
import com.unipus.digitalbook.model.entity.book.BookCopyright;
import com.unipus.digitalbook.model.entity.book.BookIntro;
import com.unipus.digitalbook.model.entity.book.BookNode;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.complement.ComplementResource;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.enums.PublishContentTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Map;

@Schema(description = "教材发布检测资源数据返回对象")
public class PublishCheckResourceDTO implements Serializable {

    @Schema(description = "教材ID")
    private String bookId;

    @Schema(description = "资源类型名称: 1：教材基本信息/2：教材简介/3：版权信息/4：配套资源/5：教材章节/6：试卷")
    private String typeName;

    @Schema(description = "资源类型名称: 1：教材基本信息/2：教材简介/3：版权信息/4：配套资源/5：教材章节/6：试卷")
    private Integer typeCode;

    @Schema(description = "教材节点的版本id")
    private Long versionId;

    @Schema(description = "教材节点已上架的版本号")
    private String publishVersionNumber;

    @Schema(description = "教材节点的资源id")
    private String resourceId;

    @Schema(description = "教材节点的资源名称")
    private String resourceName;

    @Schema(description = "教材节点的资源属性")
    private Map<String, Object> property;

    public PublishCheckResourceDTO(String bookId, PublishContentTypeEnum contentType, BookNode bookNode, String publishVersionNumber) {
        // 初始化基本属性
        this.bookId = bookId;
        this.typeName = contentType.getDesc();
        this.typeCode = contentType.getCode();
        this.publishVersionNumber = publishVersionNumber;
        // 如果节点为空，直接返回
        if (bookNode == null) {
            return;
        }
        // 根据内容类型设置特定属性
        setPropertiesByContentType(contentType, bookNode);
    }

    /**
     * 根据内容类型设置特定属性
     */
    private void setPropertiesByContentType(PublishContentTypeEnum contentType, BookNode bookNode) {
        if (PublishContentTypeEnum.BASIC_INFO.match(contentType.getCode())
                && bookNode instanceof BookBasic bookBasic) {
            this.versionId = bookBasic.getId();
        } else if (PublishContentTypeEnum.BOOK_INTRO.match(contentType.getCode())
                && bookNode instanceof BookIntro bookIntro) {
            this.versionId = bookIntro.getId();
        } else if (PublishContentTypeEnum.COPYRIGHT_INFO.match(contentType.getCode())
                && bookNode instanceof BookCopyright bookCopyright) {
            this.versionId = bookCopyright.getId();
        } else if (PublishContentTypeEnum.COMPLEMENT_RESOURCE.match(contentType.getCode())
                && bookNode instanceof ComplementResource complementResource) {
            this.versionId = complementResource.getId();
            this.resourceId = complementResource.getResourceId();
            this.resourceName = complementResource.getName();
        } else if (PublishContentTypeEnum.CHAPTER_CONTENT.match(contentType.getCode())
                && bookNode instanceof Chapter chapter) {
            this.versionId = chapter.getChapterVersion() != null ? chapter.getChapterVersion().getId() : null;
            this.resourceId = chapter.getId();
            this.resourceName = chapter.getName();
        } else if (PublishContentTypeEnum.PAPER.match(contentType.getCode())
                && bookNode instanceof Paper paper) {
            this.versionId = paper.getId();
            this.resourceId = paper.getPaperId();
            this.resourceName = paper.getPaperName();
        }
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(Integer typeCode) {
        this.typeCode = typeCode;
    }

    public Long getVersionId() {
        return versionId;
    }

    public void setVersionId(Long versionId) {
        this.versionId = versionId;
    }

    public String getPublishVersionNumber() {
        return publishVersionNumber;
    }

    public void setPublishVersionNumber(String publishVersionNumber) {
        this.publishVersionNumber = publishVersionNumber;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public Map<String, Object> getProperty() {
        return property;
    }

    public void setProperty(Map<String, Object> property) {
        this.property = property;
    }
}
