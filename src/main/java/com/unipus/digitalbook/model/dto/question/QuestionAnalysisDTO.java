package com.unipus.digitalbook.model.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class QuestionAnalysisDTO implements Serializable {

    @Schema(description = "题目ID")
    private String questionId;

    @Schema(description = "题目分析")
    private String analysis;

    public QuestionAnalysisDTO(){}

    public QuestionAnalysisDTO(String questionId, String analysis) {
        this.questionId = questionId;
        this.analysis = analysis;
    }
    public static List<QuestionAnalysisDTO> toDTOList(Map<String, String> questionAnalysisMap) {
        List<QuestionAnalysisDTO> questionAnalysis = new ArrayList<>();
        questionAnalysisMap.forEach((questionId, analysis) -> questionAnalysis.add(new QuestionAnalysisDTO(questionId, analysis)));
        return questionAnalysis;
    }

    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    public String getAnalysis() {
        return analysis;
    }

    public void setAnalysis(String analysis) {
        this.analysis = analysis;
    }
}
