package com.unipus.digitalbook.model.dto.question;

import com.unipus.digitalbook.model.entity.question.QuestionSetting;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "题目设置DTO")
public class QuestionSettingDTO implements Serializable {
    @Schema(description = "答题方式，如拖拽、点选")
    private String answerType;
    @Schema(description = "pc端布局类型 瀑布|分页|左右")
    private String pcLayoutType;
    @Schema(description = "app端布局类型 瀑布|分页")
    private String appLayoutType;
    @Schema(description = "答题角色，如学生、老师")
    private String answerRole;
    @Schema(description = "听原音 可选 'before' | 'after'| null")
    private String answerTiming;
    @Schema(description = "作答量级")
    private String answerLevel;
    @Schema(description = "音频设置信息")
    private AudioSetting audioSetting;
    @Schema(description = "视频设置信息")
    private VideoSetting videoSetting;

    public QuestionSettingDTO(QuestionSetting questionSetting) {
        this.answerType = questionSetting.getAnswerType();
        this.pcLayoutType = questionSetting.getPcLayoutType();
        this.appLayoutType = questionSetting.getAppLayoutType();
        this.answerRole = questionSetting.getAnswerRole();
        this.answerTiming = questionSetting.getAnswerTiming();
        this.answerLevel = questionSetting.getAnswerLevel();
        if (questionSetting.getAudioSetting() != null) {
            this.audioSetting = new AudioSetting(questionSetting.getAudioSetting());
        }
        if (questionSetting.getVideoSetting() != null) {
            this.videoSetting = new VideoSetting(questionSetting.getVideoSetting());
        }
    }

    public String getAnswerType() {
        return answerType;
    }

    public void setAnswerType(String answerType) {
        this.answerType = answerType;
    }

    public String getPcLayoutType() {
        return pcLayoutType;
    }

    public void setPcLayoutType(String pcLayoutType) {
        this.pcLayoutType = pcLayoutType;
    }

    public String getAppLayoutType() {
        return appLayoutType;
    }

    public void setAppLayoutType(String appLayoutType) {
        this.appLayoutType = appLayoutType;
    }

    public AudioSetting getAudioSetting() {
        return audioSetting;
    }

    public void setAudioSetting(AudioSetting audioSetting) {
        this.audioSetting = audioSetting;
    }

    public VideoSetting getVideoSetting() {
        return videoSetting;
    }

    public void setVideoSetting(VideoSetting videoSetting) {
        this.videoSetting = videoSetting;
    }

    public String getAnswerRole() {
        return answerRole;
    }

    public void setAnswerRole(String answerRole) {
        this.answerRole = answerRole;
    }

    public String getAnswerTiming() {
        return answerTiming;
    }

    public void setAnswerTiming(String answerTiming) {
        this.answerTiming = answerTiming;
    }

    public String getAnswerLevel() {
        return answerLevel;
    }

    public void setAnswerLevel(String answerLevel) {
        this.answerLevel = answerLevel;
    }

    public static class AudioSetting implements Serializable{
        @Schema(description = "播放方式 点击播放 自动播放")
        private String playType;
        @Schema(description = "作答前显示，作答后显示")
        private String subtitle;
        @Schema(description = "是否设置播放次数")
        private boolean setPlayNum;
        @Schema(description = "设置播放次数是true的时候，填写播放多少次")
        private Integer playNum;

        public AudioSetting(QuestionSetting.AudioSetting audioSetting) {
            this.playType = audioSetting.getPlayType();
            this.subtitle = audioSetting.getSubtitle();
            this.setPlayNum = audioSetting.isSetPlayNum();
            this.playNum = audioSetting.getPlayNum();
        }

        public String getPlayType() {
            return playType;
        }

        public void setPlayType(String playType) {
            this.playType = playType;
        }

        public String getSubtitle() {
            return subtitle;
        }

        public void setSubtitle(String subtitle) {
            this.subtitle = subtitle;
        }

        public boolean isSetPlayNum() {
            return setPlayNum;
        }

        public void setSetPlayNum(boolean setPlayNum) {
            this.setPlayNum = setPlayNum;
        }

        public Integer getPlayNum() {
            return playNum;
        }

        public void setPlayNum(Integer playNum) {
            this.playNum = playNum;
        }
    }

    public static class VideoSetting implements Serializable {
        @Schema(description = "播放方式 点击播放 自动播放")
        private String playType;
        @Schema(description = "作答前显示，作答后显示")
        private String subtitle;
        @Schema(description = "是否设置视频播放次数")
        private boolean setPlayNum;
        @Schema(description = "视频设置播放次数的值")
        private Integer playNum;

        public VideoSetting(QuestionSetting.VideoSetting videoSetting) {
            this.playType = videoSetting.getPlayType();
            this.subtitle = videoSetting.getSubtitle();
            this.setPlayNum = videoSetting.isSetPlayNum();
            this.playNum = videoSetting.getPlayNum();
        }

        public String getPlayType() {
            return playType;
        }

        public void setPlayType(String playType) {
            this.playType = playType;
        }

        public String getSubtitle() {
            return subtitle;
        }

        public void setSubtitle(String subtitle) {
            this.subtitle = subtitle;
        }

        public boolean isSetPlayNum() {
            return setPlayNum;
        }

        public void setSetPlayNum(boolean setPlayNum) {
            this.setPlayNum = setPlayNum;
        }

        public Integer getPlayNum() {
            return playNum;
        }

        public void setPlayNum(Integer playNum) {
            this.playNum = playNum;
        }
    }
}
