package com.unipus.digitalbook.model.dto.question;

import com.unipus.digitalbook.model.entity.question.QuestionTag;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 题目标签
 */
@Schema(description = "题目标签")
public class QuestionTagDTO {
    @Schema(description = "标签id")
    private Long id;
    @Schema(description = "标签名称")
    private String name;
    @Schema(description = "标签父id")
    private Long parentId;
    @Schema(description = "标签类型")
    private Integer tagType;

    public QuestionTagDTO() {
    }
    public QuestionTagDTO(QuestionTag tag) {
        this.id = tag.getId();
        this.name = tag.getName();
        this.parentId = tag.getParentId();
        this.tagType = tag.getTagType();
    }
    public static List<List<QuestionTagDTO>> toDTOPathsList(List<List<QuestionTag>> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return Collections.emptyList();
        }
        return tags.stream().map(tagList -> tagList.stream().map(QuestionTagDTO::new).toList()).toList();
    }

    public static List<List<QuestionTagDTO>> toDTOList(List<List<QuestionTag>> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return Collections.emptyList();
        }
        return tags.stream().map(tagList -> tagList.stream().map(QuestionTagDTO::new).toList()).toList();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getTagType() {
        return tagType;
    }

    public void setTagType(Integer tagType) {
        this.tagType = tagType;
    }
}
