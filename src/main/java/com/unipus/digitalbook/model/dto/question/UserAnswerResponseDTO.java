package com.unipus.digitalbook.model.dto.question;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Schema(description = "用户答题返回数据")
public class UserAnswerResponseDTO implements Serializable {

    @Schema(description = "用户答题总分")
    private BigDecimal score;
    @Schema(description = "用户答题结果")
    private List<UserAnswerResultDTO> userAnswersResult;

    @Schema(description = "正确答案")
    private List<AnswerDTO> correctAnswers;

    @Schema(description = "题目解析")
    private List<QuestionAnalysisDTO> questionAnalysis;

    public UserAnswerResponseDTO(){}
    public UserAnswerResponseDTO(List<UserAnswerResultDTO> userAnswersResult) {
        this.userAnswersResult = userAnswersResult;
    }
    public List<UserAnswerResultDTO> getUserAnswersResult() {
        return userAnswersResult;
    }

    public void setUserAnswersResult(List<UserAnswerResultDTO> userAnswersResult) {
        this.userAnswersResult = userAnswersResult;
    }

    public List<AnswerDTO> getCorrectAnswers() {
        return correctAnswers;
    }

    public void setCorrectAnswers(List<AnswerDTO> correctAnswers) {
        this.correctAnswers = correctAnswers;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public List<QuestionAnalysisDTO> getQuestionAnalysis() {
        return questionAnalysis;
    }

    public void setQuestionAnalysis(List<QuestionAnalysisDTO> questionAnalysis) {
        this.questionAnalysis = questionAnalysis;
    }
}
