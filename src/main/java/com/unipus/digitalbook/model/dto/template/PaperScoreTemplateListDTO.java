package com.unipus.digitalbook.model.dto.template;

import com.unipus.digitalbook.model.entity.template.PaperScoreTemplateList;
import com.unipus.digitalbook.service.UserService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "模板列表信息")
public class PaperScoreTemplateListDTO implements Serializable {

    @Schema(description = "模板列表")
    private List<PaperScoreTemplateDTO> templateDetailList;

    @Schema(description = "总数量")
    private Integer total;

    public static PaperScoreTemplateListDTO assemblyPaperScoreTemplateDTO(PaperScoreTemplateList paperScoreTemplateList, UserService userService) {
        if (ObjectUtils.isEmpty(paperScoreTemplateList)) {
            return null;
        }
        PaperScoreTemplateListDTO paperScoreTemplateListDTO = new PaperScoreTemplateListDTO();
        paperScoreTemplateListDTO.setTotal(paperScoreTemplateList.getTotal());
        if (CollectionUtils.isNotEmpty(paperScoreTemplateList.getTemplateList())) {
            paperScoreTemplateListDTO.setTemplateDetailList(paperScoreTemplateList.getTemplateList().stream().map(p -> PaperScoreTemplateDTO.assemblyPaperScoreTemplateDTO(p, userService)).toList());
        }
        return paperScoreTemplateListDTO;
    }
}
