package com.unipus.digitalbook.model.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "读者初始化信息")
public class ReaderInitInfoDTO implements Serializable {
    @Schema(description = "用户id")
    private String openId;

    @Schema(description = "读者的身份")
    private Integer readerType;

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Integer getReaderType() {
        return readerType;
    }

    public void setReaderType(Integer readerType) {
        this.readerType = readerType;
    }
}
