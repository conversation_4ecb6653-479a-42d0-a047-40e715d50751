package com.unipus.digitalbook.model.dto.user;

import com.unipus.digitalbook.model.entity.ThirdPartyUserInfo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;

/**
 * 第三方用户信息数据传输对象
 *
 * <AUTHOR>
 * @date 2025年06月23日 18:55:16
 */
@Schema(description = "第三方用户信息数据传输对象")
public class ThirdPartyUserInfoDTO implements Serializable {

    @Schema(description = "主键ID", example = "123456")
    private Long id;

    @Schema(description = "租户ID", example = "123456")
    private Long tenantId;

    @Schema(description = "用户openId", example = "openid123456")
    private String openId;

    @Schema(description = "用户昵称", example = "张三")
    private String nickName;

    @Schema(description = "用户全称", example = "张三丰")
    private String fullName;

    @Schema(description = "用户名", example = "zhangsan")
    private String userName;

    @Schema(description = "手机号", example = "13800138000")
    private String mobile;

    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;

    @Schema(description = "是否有效", example = "true")
    private Boolean enable;

    @Schema(description = "创建时间", example = "2023-10-01T12:00:00.000Z")
    private Date createTime;

    @Schema(description = "最后更新时间", example = "2023-10-01T12:00:00.000Z")
    private Date updateTime;

    public ThirdPartyUserInfoDTO() {
    }

    /**
     * 根据ThirdPartyUserInfo实体构造DTO
     *
     * @param thirdPartyUserInfo SSO用户信息实体
     */
    public ThirdPartyUserInfoDTO(ThirdPartyUserInfo thirdPartyUserInfo) {
        if (thirdPartyUserInfo == null) {
            return;
        }
        this.id = thirdPartyUserInfo.getId();
        this.tenantId = thirdPartyUserInfo.getTenantId();
        this.openId = thirdPartyUserInfo.getOpenId();
        this.nickName = thirdPartyUserInfo.getNickName();
        this.fullName = thirdPartyUserInfo.getFullName();
        this.userName = thirdPartyUserInfo.getUserName();
        this.mobile = thirdPartyUserInfo.getMobile();
        this.email = thirdPartyUserInfo.getEmail();
        this.enable = thirdPartyUserInfo.getEnable();
        this.createTime = thirdPartyUserInfo.getCreateTime();
        this.updateTime = thirdPartyUserInfo.getUpdateTime();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
