package com.unipus.digitalbook.model.entity;

import com.unipus.digitalbook.model.enums.ReaderTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 内部访问信息缓存实体类
 */
@Schema(description = "权限系统-缓存内部访问信息")
public class BackendUserInfo implements Serializable {

    @Schema(description = "openID")
    private String openId;

    @Schema(description = "读者类型（学生:1/老师:2）")
    private ReaderTypeEnum readerType;

    @Schema(description = "用户数据包")
    private String dataPackage;

    @Schema(description = "客户端ID")
    private Long appId;

    /**
     * 环境分区
     */
    private String envPartition;
    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public ReaderTypeEnum getReaderType() {
        return readerType;
    }

    public void setReaderType(ReaderTypeEnum readerType) {
        this.readerType = readerType;
    }

    public String getDataPackage() {
        return dataPackage;
    }

    public void setDataPackage(String dataPackage) {
        this.dataPackage = dataPackage;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getEnvPartition() {
        return envPartition;
    }

    public void setEnvPartition(String envPartition) {
        this.envPartition = envPartition;
    }
}