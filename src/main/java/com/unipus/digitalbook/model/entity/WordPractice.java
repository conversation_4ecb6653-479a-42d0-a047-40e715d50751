package com.unipus.digitalbook.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 词汇学练
 * <AUTHOR>
 * @date 2025年06月27日 10:28:16
 */
@Data
public class WordPractice implements Serializable {

    /**
     * id
     */
    private Long id;
    /**
     * 章节ID
     */
    private String chapterId;
    /**
     * 业务ID
     */
    private String bizId;
    /**
     * 名称
     */
    private String name;
    /**
     * 状态 0未发布 1已发布
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建者ID
     */
    private Long createBy;
    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

}
