package com.unipus.digitalbook.model.entity.action;

import com.unipus.digitalbook.model.entity.chapter.ChapterNode;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class ReadProgressList implements Serializable {
    /**
     * 已完成节点列表
     */
    private Set<String> completedList;

    private Set<String> uncompletedList;

    /**
     * 计算进度
     * @param bytes
     * @param nodeIndexMap
     * @return
     */
    public ReadProgressList computation(byte[] bytes, Map<String, ChapterNode> nodeIndexMap) {
        if(bytes == null) {
            return this;
        }
        Set<String> completedList = new HashSet<>();
        Set<String> uncompletedList = new HashSet<>();
        nodeIndexMap.forEach((id, node) -> {
            Integer offset = node.getOffset();
            int byteIndex = offset / 8;
            int bitInByte = 7 - (offset % 8);
            if (byteIndex >= bytes.length || (bytes[byteIndex] & (1 << bitInByte)) == 0) {
                uncompletedList.add(id);
            } else {
                completedList.add(id);
            }
        });
        this.setCompletedList(completedList);
        this.setUncompletedList(uncompletedList);
        return this;
    }
    public Set<String> getCompletedList() {
        return completedList;
    }

    public void setCompletedList(Set<String> completedList) {
        this.completedList = completedList;
    }

    public Set<String> getUncompletedList() {
        return uncompletedList;
    }

    public void setUncompletedList(Set<String> uncompletedList) {
        this.uncompletedList = uncompletedList;
    }
}
