package com.unipus.digitalbook.model.entity.action;

import com.unipus.digitalbook.model.entity.chapter.ChapterNode;

public class UserAction {
    /**
     * 章节ID
     */
    private String chapterId;
    /**
     * 章节版本id
     */
    private Long chapterVersionId;

    /**
     * 节点数据
     */
    private ChapterNode chapterNode;

    /**
     * 用户ID
     */
    private String openId;
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 用户IP
     */
    private String ip;

    /**
     * 数据包
     */
    private String dataPackage;

    /**
     * 环境分区
     */
    private String envPartition;
    /**
     * 行为开始时间
     */
    private Long a;

    /**
     * 行为结束时间
     */
    private Long b;

    /**
     * 题目是否作答完成
     */
    private boolean questionCompleted;

    /**
     * 行为时长
     */
    public Long getDuration() {
        if (b == null || a == null) {
            return null;
        }
        return b - a;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }


    public Long getChapterVersionId() {
        return chapterVersionId;
    }

    public void setChapterVersionId(Long chapterVersionId) {
        this.chapterVersionId = chapterVersionId;
    }

    public ChapterNode getChapterNode() {
        return chapterNode;
    }

    public void setChapterNode(ChapterNode chapterNode) {
        this.chapterNode = chapterNode;
    }

    public Long getA() {
        return a;
    }

    public void setA(Long a) {
        this.a = a;
    }

    public Long getB() {
        return b;
    }

    public void setB(Long b) {
        this.b = b;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getDataPackage() {
        return dataPackage;
    }

    public void setDataPackage(String dataPackage) {
        this.dataPackage = dataPackage;
    }

    public String getEnvPartition() {
        return envPartition;
    }

    public void setEnvPartition(String envPartition) {
        this.envPartition = envPartition;
    }

    public boolean isQuestionCompleted() {
        return questionCompleted;
    }

    public void setQuestionCompleted(boolean questionCompleted) {
        this.questionCompleted = questionCompleted;
    }
}
