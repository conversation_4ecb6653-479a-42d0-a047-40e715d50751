package com.unipus.digitalbook.model.entity.book;

import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.model.entity.Series;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.complement.ComplementResource;
import com.unipus.digitalbook.model.entity.complement.ComplementResourceList;
import com.unipus.digitalbook.model.entity.paper.PaperSyncInfo;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 教材实体
 */
public class Book {
    /**
     * 教材ID
     */
    private String id;

    /**
     * 编者
     */
    private Long editorId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 true 有效
     */
    private Boolean enable;
    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 教材版权信息对象
     */
    private BookCopyright bookCopyright;

    /**
     * 教材简介信息对象
     */
    private BookIntro bookIntro;

    /**
     * 教材基本信息对象
     */
    private BookBasic bookBasic;

    /**
     * 章节列表
     */
    private List<Chapter> chapterList;

    /**
     * 配套资源列表
     */
    private ComplementResourceList complementResourceList;

    /**
     * 教材试卷版本
     */
    private List<PaperSyncInfo> paperSyncInfos;

    /**
     * 教材版本
     */
    private BookVersion bookVersion;

    /**
     * 教材状态
     */
    private Integer status;


// --- Getters and Setters for Direct Fields ---

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getEditorId() {
        return editorId;
    }

    public void setEditorId(Long editorId) {
        this.editorId = editorId;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public List<Chapter> getChapterList() {
        return chapterList;
    }

    public void setChapterList(List<Chapter> chapterList) {
        this.chapterList = chapterList;
    }


    // --- Getters and Setters Delegating to BookBasic (with instantiation) ---

    /**
     * Ensures bookBasic is instantiated before setting the value.
     */
    private void ensureBookBasicExists() {
        if (this.bookBasic == null) {
            this.bookBasic = new BookBasic();
            this.bookBasic.setBookId(this.id);
        }
    }

    public String getChineseName() {
        return (this.bookBasic != null) ? this.bookBasic.getChineseName() : null;
    }

    public void setChineseName(String chineseName) {
        ensureBookBasicExists();
        this.bookBasic.setChineseName(chineseName);
    }

    public String getEnglishName() {
        return (this.bookBasic != null) ? this.bookBasic.getEnglishName() : null;
    }

    public void setEnglishName(String englishName) {
        ensureBookBasicExists();
        this.bookBasic.setEnglishName(englishName);
    }

    public String getLanguage() {
        return (this.bookBasic != null) ? this.bookBasic.getLanguage() : null;
    }

    public void setLanguage(String language) {
        ensureBookBasicExists();
        this.bookBasic.setLanguage(language);
    }

    public String getBusinessType() {
        return (this.bookBasic != null) ? this.bookBasic.getBusinessType() : null;
    }

    public void setBusinessType(String businessType) {
        ensureBookBasicExists();
        this.bookBasic.setBusinessType(businessType);
    }

    public Long getSeriesId() {
        return (this.bookBasic != null) ? this.bookBasic.getSeriesId() : null;
    }

    public void setSeriesId(Long seriesId) {
        ensureBookBasicExists();
        this.bookBasic.setSeriesId(seriesId);
    }

    public String getCourse() {
        return (this.bookBasic != null) ? this.bookBasic.getCourse() : null;
    }

    public void setCourse(String course) {
        ensureBookBasicExists();
        this.bookBasic.setCourse(course);
    }

    public String getCourseNature() {
        return (this.bookBasic != null) ? this.bookBasic.getCourseNature() : null;
    }

    public void setCourseNature(String courseNature) {
        ensureBookBasicExists();
        this.bookBasic.setCourseNature(courseNature);
    }

    public String getApplicableMajor() {
        return (this.bookBasic != null) ? this.bookBasic.getApplicableMajor() : null;
    }

    public void setApplicableMajor(String applicableMajor) {
        ensureBookBasicExists();
        this.bookBasic.setApplicableMajor(applicableMajor);
    }

    public String getApplicableGrade() {
        return (this.bookBasic != null) ? this.bookBasic.getApplicableGrade() : null;
    }

    public void setApplicableGrade(String applicableGrade) {
        ensureBookBasicExists();
        this.bookBasic.setApplicableGrade(applicableGrade);
    }

    public String getContactPhone() {
        return (this.bookBasic != null) ? this.bookBasic.getContactPhone() : null;
    }

    public void setContactPhone(String contactPhone) {
        ensureBookBasicExists();
        this.bookBasic.setContactPhone(contactPhone);
    }

    public String getContactEmail() {
        return (this.bookBasic != null) ? this.bookBasic.getContactEmail() : null;
    }

    public void setContactEmail(String contactEmail) {
        ensureBookBasicExists();
        this.bookBasic.setContactEmail(contactEmail);
    }

    public String getPcCoverUrl() {
        return (this.bookBasic != null) ? this.bookBasic.getPcCoverUrl() : null;
    }

    public void setPcCoverUrl(String pcCoverUrl) {
        ensureBookBasicExists();
        this.bookBasic.setPcCoverUrl(pcCoverUrl);
    }

    public String getAppHorizontalCoverUrl() {
        return (this.bookBasic != null) ? this.bookBasic.getAppHorizontalCoverUrl() : null;
    }

    public void setAppHorizontalCoverUrl(String appHorizontalCoverUrl) {
        ensureBookBasicExists();
        this.bookBasic.setAppHorizontalCoverUrl(appHorizontalCoverUrl);
    }

    public String getAppVerticalCoverUrl() {
        return (this.bookBasic != null) ? this.bookBasic.getAppVerticalCoverUrl() : null;
    }

    public void setAppVerticalCoverUrl(String appVerticalCoverUrl) {
        ensureBookBasicExists();
        this.bookBasic.setAppVerticalCoverUrl(appVerticalCoverUrl);
    }

    public String getLightColor() {
        return (this.bookBasic != null) ? this.bookBasic.getLightColor() : null;
    }

    public void setLightColor(String lightColor) {
        ensureBookBasicExists();
        this.bookBasic.setLightColor(lightColor);
    }

    public String getDarkColor() {
        return (this.bookBasic != null) ? this.bookBasic.getDarkColor() : null;
    }

    public void setDarkColor(String darkColor) {
        ensureBookBasicExists();
        this.bookBasic.setDarkColor(darkColor);
    }

    // --- Getter and Setter Delegating to BookIntro (with instantiation) ---

    /**
     * Ensures bookIntro is instantiated before setting the value.
     */
    private void ensureBookIntroExists() {
        if (this.bookIntro == null) {
            this.bookIntro = new BookIntro();
            this.bookIntro.setBookId(this.id); // If BookIntro needs the Book's ID
        }
    }

    public String getDescription() {
        return (this.bookIntro != null) ? this.bookIntro.getDescription() : null;
    }

    public void setDescription(String description) {
        ensureBookIntroExists();
        this.bookIntro.setDescription(description);
    }

    public Series getSeries() {
        return (this.bookBasic != null) ? this.bookBasic.getSeries() : null;
    }

    public void setSeries(Series series) {
        ensureBookBasicExists();
        this.bookBasic.setSeries(series);
    }

    // --- Getters for Contained Objects (Unchanged) ---

    public BookBasic getBookBasic() {
        return this.bookBasic;
    }

    public BookIntro getBookIntro() {
        return this.bookIntro;
    }

    public BookCopyright getBookCopyright() {
        return this.bookCopyright;
    }

    // Note: If fillBookIntro is called after a setter has already created a BookIntro,
    // the existing one will be replaced by the one from the PO.
    public void fillBookIntro(BookIntro bookIntro) {
        // Allow setting back to null if needed
        this.bookIntro = bookIntro;
    }

    // Note: If fillBookCopyright is called, it replaces any existing BookCopyright.
    public void fillBookCopyright(BookCopyright copyright) {
        // Allow setting back to null if needed
        this.bookCopyright = copyright;
    }

    // Note: If fillBasicInfo is called after a setter has already created a BookBasic,
    // the existing one will be replaced by the one from the PO.
    public void fillBasicInfo(BookBasic bookBasic) {
        // Allow setting back to null if needed
        this.bookBasic = bookBasic;
    }


    public void fillComplementResourceList(List<ComplementResource> complementResourceList) {
        if (complementResourceList != null){
            this.complementResourceList = new ComplementResourceList();
            this.complementResourceList.setComplementResourceList(complementResourceList);
        }
    }


    /**
     * 生成唯一标识符ID的方法
     * 如果当前对象的id为空，则通过IdentifierUtils工具类生成一个短UUID作为id
     */
    public void generateId() {
        // 检查当前对象的id是否为空或空白字符串
        if (StringUtils.isBlank(this.id)) {
            // 如果id为空，则使用IdentifierUtils工具类生成一个短UUID，并赋值给当前对象的id
            this.id = IdentifierUtil.getShortUUID();
        }
    }

    public BookVersion getBookVersion() {
        return bookVersion;
    }

    public void setBookVersion(BookVersion bookVersion) {
        this.bookVersion = bookVersion;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getDigitalFlag() {
        return (this.bookBasic != null) ? this.bookBasic.getDigitalFlag() : null;
    }

    public void setDigitalFlag(Boolean digitalFlag) {
        ensureBookBasicExists();
        this.bookBasic.setDigitalFlag(digitalFlag);
    }

    public ComplementResourceList getComplementResourceList() {
        return complementResourceList;
    }

    public List<PaperSyncInfo> getPaperSyncInfos() {
        return paperSyncInfos;
    }

    public void setPaperSyncInfos(List<PaperSyncInfo> paperSyncInfos) {
        this.paperSyncInfos = paperSyncInfos;
    }
}
