package com.unipus.digitalbook.model.entity.chapter;


import com.unipus.digitalbook.common.utils.HexUtil;
import com.unipus.digitalbook.model.entity.book.BookNode;

import java.util.Date;
import java.util.List;

public class Chapter extends BookNode {
    /**
     * 章节ID
     */
    private String id;


    /**
     * 章节编号
     */
    private Integer chapterNumber;

    /**
     * 章节名称
     */
    private String name;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 章节版本
     */
    private ChapterVersion chapterVersion;

    /**
     * 章节版本hashCode
     */
    private String versionHashCode;

    /**
     * 历史版本
     */
    private List<ChapterVersion> historyVersions;

    public Chapter generateVersionHashCode() {
        StringBuilder builder = new StringBuilder(name);
        builder.append(chapterNumber);
        if (chapterVersion != null) {
            builder.append(chapterVersion.getContent());
        }
        this.versionHashCode = HexUtil.generateObjectHash(builder.toString());
        return this;
    }
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getChapterNumber() {
        return chapterNumber;
    }

    public void setChapterNumber(Integer chapterNumber) {
        this.chapterNumber = chapterNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public ChapterVersion getChapterVersion() {
        return chapterVersion;
    }

    public String getVersionNumber() {
        return this.chapterVersion ==null ? null : this.chapterVersion.getVersionNumber();
    }

    public void setChapterVersion(ChapterVersion chapterVersion) {
        this.chapterVersion = chapterVersion;
    }

    public List<ChapterVersion> getHistoryVersions() {
        return historyVersions;
    }

    public void setHistoryVersions(List<ChapterVersion> historyVersions) {
        this.historyVersions = historyVersions;
    }

    public String getVersionHashCode() {
        return versionHashCode;
    }

    public void setVersionHashCode(String versionHashCode) {
        this.versionHashCode = versionHashCode;
    }
}
