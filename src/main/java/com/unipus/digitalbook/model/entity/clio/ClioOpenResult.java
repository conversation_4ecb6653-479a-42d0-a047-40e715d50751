package com.unipus.digitalbook.model.entity.clio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * clio个人陈述评测结果
 */
@Data
public class ClioOpenResult {

    @Schema(description = "最终评测结果")
    private FinalResult finalResult;

    @Data
    public static class FinalResult {
        @Schema(description = "音频及数据对应的唯一标识")
        private String uuid;
        @Schema(description = "用户ID")
        private String userId;
        @Schema(description = "评测结果")
        private Result result;
        @Schema(description = "音频地址")
        private String url;
        @Schema(description = "参数信息")
        private Params params;
    }

    @Data
    public static class Result {
        @Schema(description = "句子总分")
        private Integer total;
        @Schema(description = "音频时长")
        private Double audioTime;
        @Schema(description = "发音转录文本")
        private String asrDetail;
        @Schema(description = "额外信息")
        private ExtraDetails extraDetails;
        @Schema(description = "音频地址")
        private String audioUrl;
    }

    @Data
    public static class ExtraDetails {
        @Schema(description = "多维度分数")
        private MultiDimScore multiDimScore;
        @Schema(description = "多维度反馈")
        private MultiDimFeedback multiDimFeedback;
    }

    @Data
    public static class MultiDimScore {
        @Schema(description = "发音准确度")
        private Integer accuracy;
        @Schema(description = "流利度")
        private Integer fluency;
        @Schema(description = "切题度")
        private Integer relevance;
    }

    @Data
    public static class MultiDimFeedback {
        @Schema(description = "结论")
        private String conclusion;
    }

    @Data
    public static class Params {
        @Schema(description = "请求参数")
        private Request request;
        @Schema(description = "音频参数")
        private Audio audio;
    }

    @Data
    public static class Request {
        @Schema(description = "结果配置")
        private RequestResult result;
        @Schema(description = "转录文本")
        private String transcript;
        @Schema(description = "API名称")
        private String apiName;
        @Schema(description = "排名")
        private Integer rank;
        @Schema(description = "用户ID")
        private String userId;
        @Schema(description = "签名")
        private String sig;
    }

    @Data
    public static class RequestResult {
        @Schema(description = "详情配置")
        private Details details;
    }

    @Data
    public static class Details {
        @Schema(description = "调整参数")
        private Integer adjust;
    }

    @Data
    public static class Audio {
        @Schema(description = "音频类型")
        private String audioType;
        @Schema(description = "声道数")
        private Integer channel;
        @Schema(description = "采样率")
        private Integer sampleRate;
        @Schema(description = "采样字节数")
        private Integer sampleBytes;
    }
}