package com.unipus.digitalbook.model.entity.content;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 自建内容实体类
 */
@Slf4j
public class CustomContent {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 内容业务ID
     */
    private String bizId;

    /**
     * 内容类型  1：自定义章节/2：自定义段落
     */
    private Integer type;

    /**
     * 内容状态  0：编写中/1：待发布/2：已发布
     */
    private Integer status;

    /**
     * 内容名称
     */
    private String name;

    /**
     * 内容
     */
    private String content;

    /**
     * 学生内容
     */
    private String studentContent;

    /**
     * 头图地址
     */
    private String headerImg;

    /**
     * 资源信息
     */
    private String resource;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    /**
     * 自建内容目录节点列表
     */
    private List<CustomContentHeaderNode> catalogNodeList;

    /**
     * 自建内容结构节点列表
     */
    private List<CustomContentNode> totalStructNodeList;

    /**
     * 章节的题
     */
    private List<BigQuestionGroup> questionList;

    /**
     * 用户openId
     */
    private String openId;

    /**
     * 数据包ID
     */
    private String dataPackage;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getStudentContent() {
        return studentContent;
    }

    public void setStudentContent(String studentContent) {
        this.studentContent = studentContent;
    }

    public String getHeaderImg() {
        return headerImg;
    }

    public void setHeaderImg(String headerImg) {
        this.headerImg = headerImg;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public List<CustomContentHeaderNode> getCatalogNodeList() {
        return catalogNodeList;
    }

    public void setCatalogNodeList(List<CustomContentHeaderNode> catalogNodeList) {
        this.catalogNodeList = catalogNodeList;
    }

    public List<CustomContentNode> getTotalStructNodeList() {
        return totalStructNodeList;
    }

    public void setTotalStructNodeList(List<CustomContentNode> totalStructNodeList) {
        this.totalStructNodeList = totalStructNodeList;
    }

    public List<BigQuestionGroup> getQuestionList() {
        return questionList;
    }

    public void setQuestionList(List<BigQuestionGroup> questionList) {
        this.questionList = questionList;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getDataPackage() {
        return dataPackage;
    }

    public void setDataPackage(String dataPackage) {
        this.dataPackage = dataPackage;
    }

    public void fillCatalogNodeList(String catalog) {
        this.catalogNodeList = parseCatalog(catalog);
    }

    private List<CustomContentHeaderNode> parseCatalog(String catalog) {
        List<CustomContentHeaderNode> headerNodeResList = new ArrayList<>();
        if (StringUtils.isBlank(catalog)) {
            log.debug("目录为空，返回空列表");
            return headerNodeResList;
        }

        try {
            return parseAndValidateCatalog(catalog);
        } catch (JSONException e) {
            log.error("解析目录JSON格式错误: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("解析目录时发生未知异常: {}", e.getMessage(), e);
        }

        return headerNodeResList;
    }

    private List<CustomContentHeaderNode> parseAndValidateCatalog(String catalog) {
        // 解析JSON字符串到CustomContentHeaderNode列表
        List<CustomContentHeaderNode> parsedNodes = JSON.parseArray(catalog, CustomContentHeaderNode.class);
        if (parsedNodes == null) {
            return new ArrayList<>();
        }

        // 过滤掉无效的CustomContentHeaderNode
        List<CustomContentHeaderNode> validNodes = new ArrayList<>();
        for (CustomContentHeaderNode node : parsedNodes) {
            if (isValidHeaderNode(node)) {
                validNodes.add(node);
            } else {
                log.warn("忽略无效的目录节点: {}\n 原始数据：{}", node, catalog);
            }
        }

        return validNodes;
    }

    /**
     * 验证CustomContentHeaderNode对象是否有效
     *
     * @param node 待验证的CustomContentHeaderNode对象
     * @return 是否有效
     */
    private boolean isValidHeaderNode(CustomContentHeaderNode node) {
        if (node == null) {
            return false;
        }
        // 验证必要字段是否为空
        if (StringUtils.isBlank(node.getId()) ||
                StringUtils.isBlank(node.getType())) {
            return false;
        }
        // 验证标题类型是否符合h1-h6格式
        String type = node.getType().toLowerCase();
        if (!type.matches("h[1-6]")) {
            return false;
        }
        // key值验证 - 确保为正整数
        if (node.getKey() <= 0) {
            return false;
        }

        // 其他可能的业务逻辑验证...
        if (StringUtils.isBlank(node.getText())) {
            return false;
        }
        log.debug("验证通过: {}", node);
        return true;
    }
}
