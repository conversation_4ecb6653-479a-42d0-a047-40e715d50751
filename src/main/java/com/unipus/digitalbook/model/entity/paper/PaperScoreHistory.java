package com.unipus.digitalbook.model.entity.paper;

import com.unipus.digitalbook.model.po.paper.PaperScoreBatchPO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;

@Schema(description = "试卷成绩记录")
public class PaperScoreHistory {

    @Schema(description = "成绩批次ID")
    private String scoreBatchId;
    @Schema(description = "试卷实例ID")
    private String instanceId;
    @Schema(description = "用户得分")
    private BigDecimal userScore;
    @Schema(description = "标准得分")
    private BigDecimal standardScore;
    @Schema(description = "提交时间")
    private Date submitTime;

    public PaperScoreHistory(PaperScoreBatchPO paperScoreBatchPO) {
        this.scoreBatchId = paperScoreBatchPO.getId();
        this.instanceId = paperScoreBatchPO.getPaperId();
        this.userScore = paperScoreBatchPO.getUserScore();
        this.standardScore = paperScoreBatchPO.getStandardScore();
        this.submitTime = paperScoreBatchPO.getScoreSubmitDate();
    }

    public PaperScoreHistory() {}

    public String getScoreBatchId() {
        return scoreBatchId;
    }

    public void setScoreBatchId(String scoreBatchId) {
        this.scoreBatchId = scoreBatchId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public BigDecimal getUserScore() {
        return userScore;
    }

    public void setUserScore(BigDecimal userScore) {
        this.userScore = userScore;
    }

    public BigDecimal getStandardScore() {
        return standardScore;
    }

    public void setStandardScore(BigDecimal standardScore) {
        this.standardScore = standardScore;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }
}
