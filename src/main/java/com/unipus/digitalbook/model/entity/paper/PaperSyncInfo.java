package com.unipus.digitalbook.model.entity.paper;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.po.paper.PaperPO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "试卷同步信息")
public class PaperSyncInfo {
    @Schema(description = "试卷ID")
    private String paperId;
    @Schema(description = "试卷版本号")
    private String versionNumber;
    @Schema(description = "试卷类型,1:常规卷/2:挑战卷/3:诊断卷")
    private Integer paperType;
    @Schema(description = "试卷名称")
    private String paperName;
    @Schema(description = "题目数量")
    private Integer questionCount;
    @Schema(description = "试卷总分")
    private BigDecimal totalScore;
    @Schema(description = "题目列表")
    private List<BigQuestionGroup> questionGroups;

    public PaperSyncInfo(PaperPO po,  PaperTypeEnum paperType, Integer questionCount, BigDecimal totalScore, List<BigQuestionGroup> questionGroups) {
        this.paperId = po.getPaperId();
        this.versionNumber = po.getVersionNumber();
        this.paperType = paperType.getCode();
        this.paperName = po.getPaperName();
        this.questionCount = questionCount;
        this.totalScore = totalScore;
        this.questionGroups = questionGroups;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Integer getPaperType() {
        return paperType;
    }

    public void setPaperType(Integer paperType) {
        this.paperType = paperType;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public Integer getQuestionCount() {
        return questionCount;
    }

    public void setQuestionCount(Integer questionCount) {
        this.questionCount = questionCount;
    }

    public BigDecimal getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(BigDecimal totalScore) {
        this.totalScore = totalScore;
    }

    public List<BigQuestionGroup> getQuestionGroups() {
        return questionGroups;
    }

    public void setQuestionGroups(List<BigQuestionGroup> questionGroups) {
        this.questionGroups = questionGroups;
    }
}
