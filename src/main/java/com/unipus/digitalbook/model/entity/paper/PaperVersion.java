package com.unipus.digitalbook.model.entity.paper;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "试卷版本信息")
public class PaperVersion {
    @Schema(description = "试卷ID")
    private String paperId;
    @Schema(description = "试卷版本号")
    private String versionNumber;
    @Schema(description = "试卷类型")
    private Integer paperType;

    @Schema(description = "试卷主键ID")
    private Long id;
    @Schema(description = "试卷名称")
    private String paperName;
    @Schema(description = "试卷说明")
    private String description;
    @Schema(description = "题目数量")
    private Integer questionCount;
    @Schema(description = "试卷总分")
    private Integer totalScore;
    @Schema(description = "试卷内容")
    private String content;
    @Schema(description = "是否启用")
    private Boolean enable;

    public PaperVersion(Long versionId, String paperId, String versionNumber) {
        this.id = versionId;
        this.paperId = paperId;
        this.versionNumber = versionNumber;
    }

    public PaperVersion(BigQuestionGroup bigQuestionGroup) {
        this.id = bigQuestionGroup.getId();
        this.paperId = bigQuestionGroup.getBizGroupId();
        // 试卷名称
        this.paperName = bigQuestionGroup.getQuestionText();
        // 作答提示
        this.description = bigQuestionGroup.getDirection();
        // 试卷内容
        this.content = bigQuestionGroup.getContent();
        // 试卷版本
        this.versionNumber = bigQuestionGroup.getVersionNumber();
        this.enable = bigQuestionGroup.getEnable();
        this.questionCount = null;
        this.totalScore = null;
    }

    public PaperVersion() {
    }

    public Long getVersionId() {
        return this.id;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Integer getPaperType() {
        return paperType;
    }

    public void setPaperType(Integer paperType) {
        this.paperType = paperType;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getQuestionCount() {
        return questionCount;
    }

    public void setQuestionCount(Integer questionCount) {
        this.questionCount = questionCount;
    }

    public Integer getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(Integer totalScore) {
        this.totalScore = totalScore;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}
