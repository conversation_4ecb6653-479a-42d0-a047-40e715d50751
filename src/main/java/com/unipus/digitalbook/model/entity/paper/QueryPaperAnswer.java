package com.unipus.digitalbook.model.entity.paper;

import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "查询试卷作答记录")
public class QueryPaperAnswer {

    @Schema(description = "试卷实例ID")
    private String instanceId;
    @Schema(description = "试卷类型：1:常规卷/2:挑战卷/3:诊断卷")
    private PaperTypeEnum paperType;
    @Schema(description = "用户ID")
    private String openId;
    @Schema(description = "租户ID")
    private Long tenantId;

    public QueryPaperAnswer() {
    }

    public QueryPaperAnswer(String instanceId, PaperTypeEnum paperType, UserAccessInfo userAccessInfo) {
        this.instanceId = instanceId;
        this.paperType = paperType;
        this.openId = userAccessInfo.getOpenId();
        this.tenantId = userAccessInfo.getTenantId();
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public PaperTypeEnum getPaperType() {
        return paperType;
    }

    public void setPaperType(PaperTypeEnum paperType) {
        this.paperType = paperType;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }
}
