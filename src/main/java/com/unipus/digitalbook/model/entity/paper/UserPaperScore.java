package com.unipus.digitalbook.model.entity.paper;

import com.unipus.digitalbook.model.entity.tag.Tag;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplate;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplateDetail;
import com.unipus.digitalbook.model.enums.PaperSubmitStatusEnum;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.enums.TagLevelEnum;
import com.unipus.digitalbook.model.po.paper.PaperRoundPO;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户试卷成绩实体
 */
public class UserPaperScore {
    @Schema(description = "试卷实例ID")
    private String instanceId;
    @Schema(description = "试卷成绩批次ID")
    private String scoreBatchId;
    @Schema(description = "试卷类型：常规卷，诊断卷，挑战卷")
    private PaperTypeEnum paperType;
    @Schema(description = "提交时间")
    private Date submitTime;
    @Schema(description = "标准得分")
    private BigDecimal standardScore;
    @Schema(description = "用户试卷得分")
    private BigDecimal userScore;
    @Schema(description = "试卷状态：0-未提交，1-已提交")
    private PaperSubmitStatusEnum submitStatus;
    @Schema(description = "试卷小题用户得分列表")
    private List<UserQuestionScore> userQuestionScores;

    @Schema(description = "小题ID关联的技能点（标签）列表")
    private List<Tag> tags;
    @Schema(description = "推荐卷实例ID(仅诊断卷并且推荐题完成时存在)")
    private String recommendInstanceId;
    @Schema(description = "推荐卷未通过的技能点标签列表(仅诊断卷并且推荐题完成时存在)")
    private List<Tag> incorrectRecommendTags;
    @Schema(description = "推荐卷提交状态")
    private PaperSubmitStatusEnum recommendSubmitStatus;

    @Schema(description = "用户试卷最佳得分（提交批次范围内）")
    private BigDecimal bestScore;
    @Schema(description = "当前成绩批次用户挑战卷完成轮次")
    private Integer roundCountInCurrentBatch;
    @Schema(description = "当前成绩批次用户挑战卷累计作答题目数")
    private Integer totalQuestionCountInCurrentBatch;

    @Schema(description = "试卷成绩评价模板")
    private PaperScoreTemplateDetail paperScoreTemplateDetail;

    public UserPaperScore(String scoreBatchId, String instanceId, PaperTypeEnum paperType,
                          List<UserQuestionScore> userQuestionScores, List<PaperRoundPO> rounds,
                          PaperSubmitStatusEnum submitStatus) {

        this.instanceId = instanceId;
        this.scoreBatchId = scoreBatchId;
        this.paperType = paperType;
        this.submitTime = new Date();

        this.userQuestionScores = userQuestionScores;
        this.standardScore = fetchPaperStandardScore();
        this.userScore = fetchUserTotalScore();
        this.submitStatus = submitStatus;

        // 基于成绩批次统计用户挑战卷完成轮次数/累计作答题目数/最高成绩
        this.roundCountInCurrentBatch = rounds==null ? 0: rounds.size();
        this.totalQuestionCountInCurrentBatch = fetchTotalQuestionCountInCurrentBatch(rounds);
        this.bestScore = fetchBestScoreInCurrentBatch(rounds);
    }

    // 获取当前成绩批次用户挑战卷累计作答题目数
    private Integer fetchTotalQuestionCountInCurrentBatch(List<PaperRoundPO> rounds) {
        if (CollectionUtils.isEmpty(rounds)) {
            return 0;
        }
        return rounds.stream().map(PaperRoundPO::getQuestionCount)
                .filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
    }

    // 获取当前成绩批次用户挑战卷最高成绩
    private BigDecimal fetchBestScoreInCurrentBatch(List<PaperRoundPO> rounds) {
        if (CollectionUtils.isEmpty(rounds)) {
            return BigDecimal.ZERO;
        }
        return rounds.stream().map(PaperRoundPO::getUserScore)
                .filter(Objects::nonNull).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);

    }

    /**
     * 取得用户正确小题总数(判题的客观题数量)
     */
    public Integer fetchCorrectObjectiveSmallQuestionCount() {
        if (CollectionUtils.isEmpty(this.userQuestionScores)) {
            return 0;
        }
        return (int) this.userQuestionScores.stream()
                .filter(score -> score != null
                        && Boolean.TRUE.equals(score.getObjective())
                        && Boolean.TRUE.equals(score.getJudged())
                        && Boolean.TRUE.equals(score.getIsCorrect())
                        && score.getUserScore() != null
                        && score.getStandardScore() != null
                        && score.getUserScore().compareTo(score.getStandardScore()) == 0)
                .count();
    }

    /**
     * 取得全部小题总数(判题的客观题数量)
     */
    public Integer fetchTotalObjectiveSmallQuestionCount() {
        if (CollectionUtils.isEmpty(this.userQuestionScores)) {
            return 0;
        }
        return (int) this.userQuestionScores.stream()
                .filter(score -> Boolean.TRUE.equals(score.getObjective()))
                .filter(score -> Boolean.TRUE.equals(score.getJudged()))
                .count();
    }

    /**
     * 获取用户正确的计分的客观题分数
     * @return 分数合计
     */
    public BigDecimal fetchUserSmallQuestionScore() {
        if (CollectionUtils.isEmpty(this.userQuestionScores)) {
            return BigDecimal.ZERO;
        }
        return this.userQuestionScores.stream()
                .filter(score -> Boolean.TRUE.equals(score.getScored()))
                .filter(score -> score.getUserScore() != null && score.getStandardScore() != null)
                .filter(score -> score.getUserScore().compareTo(score.getStandardScore()) == 0)
                .map(UserQuestionScore::getUserScore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取计分的客观题总分
     * @return 分数合计
     */
    public BigDecimal fetchTotalSmallQuestionScore() {
        if (CollectionUtils.isEmpty(this.userQuestionScores)) {
            return BigDecimal.ZERO;
        }
        return this.userQuestionScores.stream()
                .filter(Objects::nonNull)
                .filter(score ->Boolean.TRUE.equals(score.getScored()))
                .map(UserQuestionScore::getStandardScore)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    // 取得试卷标准得分
    private BigDecimal fetchPaperStandardScore() {
        if (CollectionUtils.isEmpty(this.userQuestionScores)) {
            return BigDecimal.ZERO;
        }
        return this.userQuestionScores.stream()
                .map(UserQuestionScore::getStandardScore)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    // 计算用户总得分
    private BigDecimal fetchUserTotalScore() {
        if (CollectionUtils.isEmpty(this.userQuestionScores)) {
            return BigDecimal.ZERO;
        }
        return this.userQuestionScores.stream().map(UserQuestionScore::getUserScore).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 取得大题得分(以大题维度用户得分)
     * @return Map<String, BigDecimal> 大题ID:用户得分
     */
    public Map<String, BigDecimal> getUserBigQuestionScoresMap() {
        return this.userQuestionScores.stream().collect(Collectors.groupingBy(
                UserQuestionScore::getBigQuestionBizId,
                Collectors.mapping(UserQuestionScore::getUserScore, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))
        ));
    }

    /**
     * 取得小题得分(小题维度)
     * @return Map<String, BigDecimal> 小题ID:用户得分
     */
    public Map<String, BigDecimal> getUserSmallQuestionScoresMap() {
        return this.userQuestionScores.stream().collect(Collectors.toMap(
                UserQuestionScore::getSmallQuestionBizId,
                UserQuestionScore::getUserScore));
    }

    /**
     * 取得小题是否计分映射(小题维度)
     * @return Map<String, Boolean> 小题ID:是否计分（只要有一个计分则认为当前小题计分）
     */
    public Map<String, Boolean> getUserSmallQuestionIsScoredMap() {
        if (CollectionUtils.isEmpty(this.userQuestionScores)) {
            return Map.of();
        }
        return this.userQuestionScores.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        UserQuestionScore::getSmallQuestionBizId,
                        UserQuestionScore::getScored,
                        (existing, replacement) -> existing || replacement
                ));
    }

    /**
     * 取得小题是否判题映射(小题维度)
     * @return Map<String, Boolean> 小题ID:是否判题（只要有一个判题则认为当前小题判题）
     */
    public Map<String, Boolean> getUserSmallQuestionIsJudgedMap() {
        if (CollectionUtils.isEmpty(this.userQuestionScores)) {
            return Map.of();
        }
        return this.userQuestionScores.stream()
                .collect(Collectors.groupingBy(
                        UserQuestionScore::getSmallQuestionBizId,
                        Collectors.mapping(
                                UserQuestionScore::getJudged,
                                Collectors.reducing(false, (a, b) -> a || b)
                        )
                ));
    }

    /**
     * 取得小题是否正确的映射(小题维度)
     * @return Map<String, Boolean> 小题ID，true:正确/错误
     */
    public Map<String, Boolean> getUserSmallQuestionCorrectMap() {
        if (CollectionUtils.isEmpty(this.userQuestionScores)) {
            return Map.of();
        }
        return this.userQuestionScores.stream()
                .collect(Collectors.groupingBy(
                        UserQuestionScore::getSmallQuestionBizId,
                        Collectors.mapping(
                                UserQuestionScore::getIsCorrect,
                                Collectors.reducing(true, (a, b) -> a && b)
                        )
                ));
    }

    /**
     * 获取指定层级的标签
     * @param level 标签层级
     * @return Map<String, List<String>> key：知识点名称，value：资源列表
     */
    public LinkedHashMap<String, List<String>> getTagNameMapByLevel(TagLevelEnum level){
        if(CollectionUtils.isEmpty(this.tags) || level==null){
            return new LinkedHashMap<>();
        }
        // 过滤出目标层级的标签
        List<Tag> targetLevelTags = this.tags.stream()
                .filter(tag -> tag != null && level.getCode().equals(tag.getLevel()))
                .collect(Collectors.toList());

        // 一级技能点需要按照TagId排序，二级保持原始顺序
        if(TagLevelEnum.FIRST.match(level)) {
            targetLevelTags.sort(Comparator.comparing(Tag::getTagId));
        }

        // 按照tagName分组并收集resourceId到列表中
        return targetLevelTags.stream()
                .collect(Collectors.groupingBy(
                        Tag::getTagName,
                        LinkedHashMap::new,
                        Collectors.mapping(Tag::getResourceId, Collectors.toList())
                ));
    }

    /**
     * 取得小题ID列表
     * @return List<String> 小题ID列表
     */
    public List<String> getSmallQuestionIds() {
        if (CollectionUtils.isEmpty(this.userQuestionScores)) {
            return List.of();
        }
        return this.userQuestionScores.stream()
                .map(UserQuestionScore::getSmallQuestionBizId)
                .toList();
    }

    /**
     * 设置匹配的试卷成绩模板
     * @param paperScoreTemplate 试卷成绩模板
     */
    public void setMatchedPaperScoreTemplateDetail(PaperScoreTemplate paperScoreTemplate) {
        if (paperScoreTemplate==null) {
            return;
        }

        int score;
        if (this.userScore == null || this.standardScore == null || this.standardScore.compareTo(BigDecimal.ZERO) == 0) {
            score = 0;
        }else {
            // 计算得分百分比
            score = 100 * this.userScore.intValue() / this.standardScore.intValue();
        }

        this.paperScoreTemplateDetail = paperScoreTemplate.getMatchedDetail(score);
    }

    /**
     * 获取错误推荐题标签名称列表
     * @return 错误推荐题标签名称列表
     */
    public List<String> getIncorrectRecommendTagNames() {
        if(CollectionUtils.isEmpty(this.incorrectRecommendTags)){
            return List.of();
        }
        return this.incorrectRecommendTags.stream().map(Tag::getTagName).distinct().toList();
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getScoreBatchId() {
        return scoreBatchId;
    }

    public void setScoreBatchId(String scoreBatchId) {
        this.scoreBatchId = scoreBatchId;
    }

    public PaperTypeEnum getPaperType() {
        return paperType;
    }

    public void setPaperType(PaperTypeEnum paperType) {
        this.paperType = paperType;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public BigDecimal getStandardScore() {
        return standardScore;
    }

    public void setStandardScore(BigDecimal standardScore) {
        this.standardScore = standardScore;
    }

    public BigDecimal getUserScore() {
        return userScore;
    }

    public void setUserScore(BigDecimal userScore) {
        this.userScore = userScore;
    }

    public PaperSubmitStatusEnum getSubmitStatus() {
        return submitStatus;
    }

    public void setSubmitStatus(PaperSubmitStatusEnum submitStatus) {
        this.submitStatus = submitStatus;
    }

    public List<UserQuestionScore> getUserQuestionScores() {
        return userQuestionScores;
    }

    public void setUserQuestionScores(List<UserQuestionScore> userQuestionScores) {
        this.userQuestionScores = userQuestionScores;
    }

    public List<Tag> getTags() {
        return tags;
    }

    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }

    public String getRecommendInstanceId() {
        return recommendInstanceId;
    }

    public void setRecommendInstanceId(String recommendInstanceId) {
        this.recommendInstanceId = recommendInstanceId;
    }

    public List<Tag> getIncorrectRecommendTags() {
        return incorrectRecommendTags;
    }

    public void setIncorrectRecommendTags(List<Tag> incorrectRecommendTags) {
        this.incorrectRecommendTags = incorrectRecommendTags;
    }

    public PaperSubmitStatusEnum getRecommendSubmitStatus() {
        return recommendSubmitStatus;
    }

    public void setRecommendSubmitStatus(PaperSubmitStatusEnum recommendSubmitStatus) {
        this.recommendSubmitStatus = recommendSubmitStatus;
    }

    public BigDecimal getBestScore() {
        return bestScore;
    }

    public void setBestScore(BigDecimal bestScore) {
        this.bestScore = bestScore;
    }

    public Integer getRoundCountInCurrentBatch() {
        return roundCountInCurrentBatch;
    }

    public void setRoundCountInCurrentBatch(Integer roundCountInCurrentBatch) {
        this.roundCountInCurrentBatch = roundCountInCurrentBatch;
    }

    public Integer getTotalQuestionCountInCurrentBatch() {
        return totalQuestionCountInCurrentBatch;
    }

    public void setTotalQuestionCountInCurrentBatch(Integer totalQuestionCountInCurrentBatch) {
        this.totalQuestionCountInCurrentBatch = totalQuestionCountInCurrentBatch;
    }

    public PaperScoreTemplateDetail getPaperScoreTemplateDetail() {
        return paperScoreTemplateDetail;
    }

    public void setPaperScoreTemplateDetail(PaperScoreTemplateDetail paperScoreTemplateDetail) {
        this.paperScoreTemplateDetail = paperScoreTemplateDetail;
    }
}
