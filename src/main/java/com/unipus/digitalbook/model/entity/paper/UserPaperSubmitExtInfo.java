package com.unipus.digitalbook.model.entity.paper;

import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Schema(description = "用户试卷提交扩展信息")
@Data
public class UserPaperSubmitExtInfo {

    @Schema(description = "用户访问信息")
    private UserAccessInfo userAccessInfo;

    @Schema(description = "书籍ID")
    private String bookId;
    @Schema(description = "书籍版本号")
    private String bookVersionNumber;

    @Schema(description = "试卷ID")
    private String paperId;
    @Schema(description = "试卷版本号")
    private String paperVersionNumber;
    @Schema(description = "实例ID")
    private String instanceId;
    @Schema(description = "试卷类型")
    private Integer paperType;
    @Schema(description = "试卷名称")
    private String paperName;

    @Schema(description = "题目数量")
    private Integer questionCount;
    @Schema(description = "大题组列表")
    private List<BigQuestionGroup> bigQuestionGroups;
    @Schema(description = "用户答案映射")
    private Map<String, List<UserAnswer>> userAnswerMap;

    public UserPaperSubmitExtInfo(String bookId, String bookVersionNumber, PaperInstance paperInstance,
        Map<String, List<UserAnswer>> userAnswerMap, UserAccessInfo userAccessInfo){

        this.userAccessInfo = userAccessInfo;

        this.bookId = bookId;
        this.bookVersionNumber = bookVersionNumber;

        this.paperId = paperInstance.getPaperId();
        this.paperVersionNumber = paperInstance.getVersionNumber();
        this.instanceId = paperInstance.getInstanceId();
        this.paperType = paperInstance.getPaperType().getCode();
        this.paperName = paperInstance.getPaperName();
        this.bigQuestionGroups = paperInstance.getBigQuestionGroupList();
        this.userAnswerMap = userAnswerMap;

        this.questionCount = paperInstance.getQuestionCount();
    }

    // 获取用户得分
    public BigDecimal getUserScoreFromJudgedUserAnswer() {
        if (MapUtils.isEmpty(this.userAnswerMap)) {
            return BigDecimal.ZERO;
        }
        return this.userAnswerMap.values().stream().flatMap(List::stream)
                .map(UserAnswer::getScore)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    // 获取用户答题总分
    public BigDecimal getTotalScoreFromQuestions() {
        if(CollectionUtils.isEmpty(this.bigQuestionGroups)){
            return BigDecimal.ZERO;
        }
        return this.bigQuestionGroups.stream()
                .map(BigQuestionGroup::getScore)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    // 获取过关率检查表示
    public Boolean isCheckPassRate() {
        // 如果能够取得教材发布版本信息，则进行过关率检查。
        // 预览模式，编辑中的教材（默认版本号），无发布信息，则不进行过关率检查
        return this.bookId != null && this.bookVersionNumber != null;
    }
}
