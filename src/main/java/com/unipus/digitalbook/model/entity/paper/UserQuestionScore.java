package com.unipus.digitalbook.model.entity.paper;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.UserAnswerList;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import com.unipus.digitalbook.model.po.paper.PaperQuestionInstancePO;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 试卷小题分数
 */
public class UserQuestionScore {
    // 大题业务ID
    private String bigQuestionBizId;
    // 小题业务ID
    private String smallQuestionBizId;
    // 小题用户得分
    private BigDecimal userScore;
    // 小题标准分
    private BigDecimal standardScore;
    // 是否客观题
    private Boolean isObjective;
    // 是否判分
    private Boolean isScored;
    // 是否判分
    private Boolean isJudged;
    // 是否正确
    private Boolean isCorrect;

    public UserQuestionScore(String bigQuestionBizId, String smallQuestionBizId, BigDecimal userScore,
                             BigDecimal standardScore, Boolean isObjective, Boolean isScored, Boolean isJudged, Boolean isCorrect) {
        this.bigQuestionBizId = bigQuestionBizId;
        this.smallQuestionBizId = smallQuestionBizId;
        this.userScore = userScore;
        this.standardScore = standardScore;
        this.isObjective = isObjective;
        this.isScored = isScored;
        this.isJudged = isJudged;
        this.isCorrect = isCorrect;
    }

    public UserQuestionScore(PaperQuestionInstancePO instancePO) {
        this.bigQuestionBizId = instancePO.getQuestionBizGroupId();
        this.smallQuestionBizId = instancePO.getQuestionBizId();
        this.userScore = instancePO.getUserScore();
        this.standardScore = instancePO.getStandardScore();
        this.isObjective = QuestionGroupTypeEnum.isObjective(instancePO.getQuestionGroupType());
        this.isScored = instancePO.getScored();
        this.isJudged = instancePO.getJudged();
        this.isCorrect = instancePO.getCorrect();
    }

    public Boolean isCorrect(){
        if(this.userScore == null || this.standardScore == null){
            return false;
        }
        return this.userScore.compareTo(this.standardScore) == 0;
    }

    /**
     * 构建小题用户得分
     * 1.从题组中提取小题及小题下面所有作答单元ID列表
     * 2.遍历小题下面所有作答单元ID列表，获取每个作答单元的得分
     * 3.汇聚作答单元得分，得到到小题的总分
     * 4.返回所有小题用户的数
     * @param group 大题对象
     * @param answerList 用户答案列表
     * @return 小题分数对象列表
     */
    public static List<UserQuestionScore> buildUserSmallQuestionScores(BigQuestionGroup group, UserAnswerList answerList){

        if(CollectionUtils.isEmpty(group.getQuestions()) || CollectionUtils.isEmpty(answerList.getUserAnswers())){
            return List.of();
        }

        // 小题ID列表
        List<String> smallQuestionIds = group.getQuestions().stream().map(Question::getBizQuestionId).toList();
        // 小题下所有子题ID列表映射(子题：最小作答单位)
        Map<String, List<String>> smallChildMap = group.fetchChildQuestionIds(smallQuestionIds);
        if(CollectionUtils.isEmpty(smallChildMap)){
            return List.of();
        }
        // 子题单位用户成绩映射
        Map<String, BigDecimal> userChildScoreMap = answerList.getUserAnswers().stream()
                .filter(ua->ua.getScore()!=null)
                .collect(Collectors.toMap(UserAnswer::getBizQuestionId, UserAnswer::getScore));

        // 以小题单位统计用户成绩
        List<UserQuestionScore> result = new ArrayList<>();
        for (Question small : group.getQuestions()) {
            BigDecimal standardSmallScore = small.getScore();
            String smallQuestionId = small.getBizQuestionId();
            // 判断该小题是否计分
            boolean smallQuestionIsScored = smallQuestionIsScored(small);
            // 判断该小题是否判题
            boolean smallQuestionIsJudged = smallQuestionIsJudged(small);
            // 判断该小题是否正确
            boolean smallQuestionIsCorrect = smallQuestionCorrect(small, answerList.getUserAnswers());

            BigDecimal userSmallScore = BigDecimal.ZERO;
            if(smallQuestionIsScored) {
                // 如果小题计分，则取得小题下面子题ID列表
                List<String> childQuestionIds = smallChildMap.getOrDefault(smallQuestionId, List.of());
                // 计算小题总得分
                for (String childId : childQuestionIds) {
                    userSmallScore = userSmallScore.add(userChildScoreMap.getOrDefault(childId, BigDecimal.ZERO));
                }
            }

            // 追加小题用户得分
            result.add(new UserQuestionScore(group.getBizGroupId(), smallQuestionId, userSmallScore, standardSmallScore,
                    QuestionGroupTypeEnum.isObjective(group.getType()), smallQuestionIsScored, smallQuestionIsJudged,
                    smallQuestionIsCorrect));
        }
        return result;
    }

    // 递归判断小题是否计分（任意最小作答单元计分，则认为该小题计分）
    private static Boolean smallQuestionIsScored(Question question) {
        if (question == null) {
            return false;
        }
        List<Question> subQuestions = question.getQuestions();
        if (CollectionUtils.isEmpty(subQuestions)) {
            return Boolean.TRUE.equals(question.getIsScoring());
        }
        return subQuestions.stream().anyMatch(UserQuestionScore::smallQuestionIsScored);
    }

    // 递归判断小题是否判题（任意最小作答单元判题，则认为该小题判题）
    private static Boolean smallQuestionIsJudged(Question question) {
        if (question == null) {
            return false;
        }
        List<Question> subQuestions = question.getQuestions();
        if (CollectionUtils.isEmpty(subQuestions)) {
            return Boolean.TRUE.equals(question.getIsJudgment());
        }
        return subQuestions.stream().anyMatch(UserQuestionScore::smallQuestionIsJudged);
    }

    // 判断小题是否正确（所有最小作答单元都为正确则小题为正确，否则错误）（true:正确，false:错误）
    private static Boolean smallQuestionCorrect(Question small, List<UserAnswer> userAnswers) {
        if (small==null || CollectionUtils.isEmpty(userAnswers)) {
            return false;
        }
        List<String> ids = fetchChildQuestionIds(small);

        // 递归检查所有子题目是否正确
        return ids.stream().allMatch(id -> userAnswers.stream().anyMatch(ua -> ua.getBizQuestionId().equals(id) && ua.isCorrect()));
    }

    // 递归取得所有最小作答单元Id
    private static List<String> fetchChildQuestionIds(Question question) {
        if (question == null) {
            return List.of();
        }
        if (CollectionUtils.isEmpty(question.getQuestions())) {
            return List.of(question.getBizQuestionId());
        }else{
            List<String> ids = new ArrayList<>();
            question.getQuestions().forEach(q -> ids.addAll(fetchChildQuestionIds(q)));
            return ids;
        }
    }

    public String getBigQuestionBizId() {
        return bigQuestionBizId;
    }

    public void setBigQuestionBizId(String bigQuestionBizId) {
        this.bigQuestionBizId = bigQuestionBizId;
    }

    public String getSmallQuestionBizId() {
        return smallQuestionBizId;
    }

    public void setSmallQuestionBizId(String smallQuestionBizId) {
        this.smallQuestionBizId = smallQuestionBizId;
    }

    public BigDecimal getUserScore() {
        return userScore;
    }

    public void setUserScore(BigDecimal userScore) {
        this.userScore = userScore;
    }

    public BigDecimal getStandardScore() {
        return standardScore;
    }

    public void setStandardScore(BigDecimal standardScore) {
        this.standardScore = standardScore;
    }

    public Boolean getObjective() {
        return isObjective;
    }

    public void setObjective(Boolean objective) {
        isObjective = objective;
    }

    public Boolean getScored() {
        return isScored;
    }

    public void setScored(Boolean scored) {
        isScored = scored;
    }

    public Boolean getJudged() {
        return isJudged;
    }

    public void setJudged(Boolean judged) {
        isJudged = judged;
    }

    public Boolean getIsCorrect() {
        return isCorrect;
    }

    public void setIsCorrect(Boolean isCorrect) {
        this.isCorrect = isCorrect;
    }
}
