package com.unipus.digitalbook.model.entity.publish;

import java.util.List;

/**
 * 教材已发布版本列表实体类
 */
public class BookPublishedVersionList {

    /**
     * 教材已发布版本列表
     */
    private List<BookPublishedVersion> versionList;

    /**
     * 总数量
     */
    private Integer totalCount;

    public BookPublishedVersionList(List<BookPublishedVersion> versionList, Integer totalCount) {
        this.versionList = versionList;
        this.totalCount = totalCount;
    }

    public List<BookPublishedVersion> getVersionList() {
        return versionList;
    }

    public void setVersionList(List<BookPublishedVersion> versionList) {
        this.versionList = versionList;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }
}
