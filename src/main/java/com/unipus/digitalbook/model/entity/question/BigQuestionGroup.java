package com.unipus.digitalbook.model.entity.question;

import com.unipus.digitalbook.common.utils.ArrayUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 题组
 */
public class BigQuestionGroup {
    /**
     * ID
     */
    private Long id;
    /**
     * 题组ID
     */
    private String bizGroupId;

    /**
     * 题组版本号
     */
    private String versionNumber;

    /**
     * 父题组主键ID
     */
    private Long parentId;

    /**
     * 题组类型
     * @see com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum
     */
    private Integer type;

    /**
     * 作答提示
     */
    private String direction;

    /**
     * 材料内容
     */
    private String content;

    /**
     * 答案解析 如果为空则说明不需要解析
     */
    private String analysis;

    /**
     * 题干
     */
    private String questionText;

    /**
     * 题组难度级别0-5
     */
    private Integer difficulty;

    /**
     * 题组总分
     */
    private BigDecimal score;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    /**
     * 题组设置
     */
    private QuestionSetting setting;

    /**
     * 题组下的题目
     */
    private List<Question> questions;

    /**
     * 题组排序序号
     */
    private Integer sortOrder;

    public Map<String, String> fetchQuestionAnalysis() {
        Map<String, String> questionAnalysis = new HashMap<>();
        if (StringUtils.isNotBlank(analysis)) {
            questionAnalysis.put(getBizGroupId(), analysis);
        }
        getQuestions().forEach(question -> {
            fetchQuestionAnalysis(question, questionAnalysis);
        });
        return questionAnalysis;
    }

    private void fetchQuestionAnalysis(Question question, Map<String, String> questionAnalysis) {
        if (StringUtils.isNotBlank(question.getAnalysis())) {
            questionAnalysis.put(question.getBizQuestionId(), question.getAnalysis());
        }
        if (!(question instanceof IQuestion)) {
            question.getQuestions().forEach(q -> fetchQuestionAnalysis(q, questionAnalysis));
        }
    }
    /**
     * 获取题目组下所有正确答案
     * @return
     */
    public Map<String, List<QuestionAnswer>> fetchCorrectAnswers() {
        Map<String, List<QuestionAnswer>> correctAnswers = new HashMap<>();
        getQuestions().forEach(question ->
            fetchCorrectAnswers(question, correctAnswers)
        );
        return correctAnswers;
    }

    private void fetchCorrectAnswers(Question question, Map<String, List<QuestionAnswer>> correctAnswers) {
        if (question instanceof IQuestion) {
            correctAnswers.put(question.getBizQuestionId(), question.getAnswers());
        } else {
            question.getQuestions().forEach(q -> fetchCorrectAnswers(q, correctAnswers));
        }
    }

    /**
     * 获取任意层级题组下所有子题ID
     * @param anyLevelGroupIds 任意层级题组ID集合
     * @return 集合key为anyLevelGroupIds中的题组ID，value为题组下所有子题ID
     */
    public Map<String, List<String>> fetchChildQuestionIds(List<String> anyLevelGroupIds) {
        if (CollectionUtils.isEmpty(anyLevelGroupIds)) {
            return Collections.emptyMap();
        }

        Map<String, List<String>> questionIdsMap = new HashMap<>();
        getQuestions().forEach(question ->
                fetchChildQuestionIds(question, questionIdsMap, anyLevelGroupIds, null)
        );
        return questionIdsMap;
    }

    /**
     * 获取任意层级题组下所有子题ID
     * @param question 当前题目
     * @param questionIdsMap 集合key为题组ID，value为题组下所有子题ID
     * @param anyLevelGroupIds 任意层级题组ID集合
     * @param childrenIds 当前子题ID集合
     */
    private void fetchChildQuestionIds(Question question, Map<String, List<String>> questionIdsMap,
                                       List<String> anyLevelGroupIds, List<String> childrenIds) {
        if( anyLevelGroupIds.contains(question.getBizQuestionId())){
            // 为目标对象创建一个空的集合
            childrenIds = new ArrayList<>();
            questionIdsMap.put(question.getBizQuestionId(), childrenIds);
        }
        if (question instanceof IQuestion) {
            // 如果存在目标对象，则将当前子题ID添加到目标对象的集合中，并返回
            if(childrenIds!=null){ childrenIds.add(question.getBizQuestionId());}

        } else if(!CollectionUtils.isEmpty(question.getQuestions())){
            // 递归遍历下级题目
            List<String> finalChildrenIds = childrenIds;
            question.getQuestions().forEach(q -> fetchChildQuestionIds(q, questionIdsMap, anyLevelGroupIds, finalChildrenIds));
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBizGroupId() {
        return bizGroupId;
    }

    public void setBizGroupId(String bizGroupId) {
        this.bizGroupId = bizGroupId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAnalysis() {
        return analysis;
    }

    public void setAnalysis(String analysis) {
        this.analysis = analysis;
    }

    public String getQuestionText() {
        return questionText;
    }

    public void setQuestionText(String questionText) {
        this.questionText = questionText;
    }

    public Integer getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(Integer difficulty) {
        this.difficulty = difficulty;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public QuestionSetting getSetting() {
        return setting;
    }

    public void setSetting(QuestionSetting setting) {
        this.setting = setting;
    }

    public List<Question> getQuestions() {
        return questions;
    }

    public void setQuestions(List<Question> questions) {
        this.questions = questions;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BigQuestionGroup that = (BigQuestionGroup) o;
        return  isBigDecimalEqual(score, that.score) &&
                Objects.equals(parentId, that.parentId) &&
                Objects.equals(bizGroupId, that.bizGroupId) &&
                Objects.equals(type, that.type) &&
                Objects.equals(direction, that.direction) &&
                Objects.equals(content, that.content) &&
                Objects.equals(questionText, that.questionText) &&
                Objects.equals(difficulty, that.difficulty) &&
                Objects.equals(analysis, that.analysis) &&
                Objects.equals(sortOrder, that.sortOrder) &&
                Objects.equals(setting, that.setting) &&
                ArrayUtil.equalsIgnoreNullAndOrder(questions, that.questions);
    }
    public static boolean isBigDecimalEqual(BigDecimal a, BigDecimal b) {
        return (a == null && b == null) || (a != null && b != null && a.compareTo(b) == 0);
    }
    @Override
    public int hashCode() {
        return Objects.hash(parentId, bizGroupId, type, direction, content, questionText, difficulty, analysis, sortOrder, setting);
    }
}


