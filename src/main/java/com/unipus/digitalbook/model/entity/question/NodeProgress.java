package com.unipus.digitalbook.model.entity.question;

public class NodeProgress {
    /**
     * 节点ID
     */
    private String id;
    /**
     * 节点类型
     */
    private String type;

    /**
     * 节点状态 1 完成
     */
    private Integer status;

    public NodeProgress() {}

    public NodeProgress(String id, String type) {
        this.id = id;
        this.type = type;
        this.status = 1;
    }
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
