package com.unipus.digitalbook.model.entity.question;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.paper.UserPaperSyncInfo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class UserAnswerData {

    private String answer;

    private String dataPackage;

    private String paper;

    private String nodeProgress;

    public static UserAnswerData buildQuestionAnswerData(BigQuestionGroup question, List<UserAnswer> userAnswers, String dataPackage) {
        UserAnswerData data = new UserAnswerData();
        data.setDataPackage(dataPackage);
        UserAnswerNode userAnswerNode = new UserAnswerNode().toUserAnswerNode(question, userAnswers);
        data.setAnswer(JsonUtil.toJsonString(userAnswerNode));
        return data;
    }

    public static UserAnswerData buildNodeProgress(String dataPackage, String nodeId, String nodeType) {
        UserAnswerData data = new UserAnswerData();
        data.setDataPackage(dataPackage);
        data.setNodeProgress(JsonUtil.toJsonString(new NodeProgress(nodeId, nodeType)));
        return data;
    }

    public static UserAnswerData buildPaperAnswerData(UserPaperSyncInfo syncInfo) {
        UserAnswerData data = new UserAnswerData();
        data.setDataPackage(syncInfo.getDataPackage());
        data.setPaper(JsonUtil.toJsonString(new PaperAnswerData(syncInfo)));
        return data;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public String getDataPackage() {
        return dataPackage;
    }

    public void setDataPackage(String dataPackage) {
        this.dataPackage = dataPackage;
    }

    public String getPaper() {
        return paper;
    }

    public void setPaper(String paper) {
        this.paper = paper;
    }

    public void setNodeProgress(String nodeProgress) {
        this.nodeProgress = nodeProgress;
    }

    public String getNodeProgress() {
        return nodeProgress;
    }

    static class PaperAnswerData {
        private String paperId;
        private String versionNumber;
        private Integer paperType;
        private BigDecimal score;
        private BigDecimal paperScore;
        private List<UserAnswerNode> answers;

        public PaperAnswerData(UserPaperSyncInfo syncInfo) {

            this.paperId = syncInfo.getPaperId();
            this.versionNumber = syncInfo.getPaperVersionNumber();
            this.paperType = syncInfo.getPaperType();
            this.score = syncInfo.getUserScore();
            this.paperScore = syncInfo.getTotalScore();
            this.answers = new ArrayList<>();

            syncInfo.getQuestionGroups().forEach(question -> {
                List<UserAnswer> userAnswers = syncInfo.getUserAnswersMap().get(question.getBizGroupId());
                UserAnswerNode userAnswerNode = new UserAnswerNode().toUserAnswerNode(question, userAnswers);
                this.answers.add(userAnswerNode);
            });
        }

        public String getPaperId() {
            return paperId;
        }

        public void setPaperId(String paperId) {
            this.paperId = paperId;
        }

        public String getVersionNumber() {
            return versionNumber;
        }

        public void setVersionNumber(String versionNumber) {
            this.versionNumber = versionNumber;
        }

        public Integer getPaperType() {
            return paperType;
        }

        public void setPaperType(Integer paperType) {
            this.paperType = paperType;
        }

        public BigDecimal getScore() {
            return score;
        }

        public void setScore(BigDecimal score) {
            this.score = score;
        }

        public BigDecimal getPaperScore() {
            return paperScore;
        }

        public void setPaperScore(BigDecimal paperScore) {
            this.paperScore = paperScore;
        }

        public List<UserAnswerNode> getAnswers() {
            return answers;
        }

        public void setAnswers(List<UserAnswerNode> answers) {
            this.answers = answers;
        }
    }
}
