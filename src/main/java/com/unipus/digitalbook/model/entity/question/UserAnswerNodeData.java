package com.unipus.digitalbook.model.entity.question;

import com.unipus.digitalbook.model.enums.LearnTypeEnum;

import java.util.List;

public class UserAnswerNodeData {
    /**
     * 答题人ID
     */
    private String openId;

    /**
     * 教材ID
     */
    private String bookId;

    /**
     * 教材版本号
     */
    private String bookVersionNumber;

    /**
     * 答题人IP
     */
    private String ip;

    /**
     * 学习类型
     */
    private String learnType;
    /**
     * 答题数据
     */
    private UserAnswerData data;

    public UserAnswerNodeData(){}

    public UserAnswerNodeData(String openId, LearnTypeEnum learnType, String bookId, String bookVersionNumber, String ip) {
        this.learnType = learnType.getCode();
        this.openId = openId;
        this.bookId = bookId;
        this.bookVersionNumber = bookVersionNumber;
        this.ip = ip;
    }

    public UserAnswerNodeData toQuestionUserAnswerNodeData(String dataPackage, BigQuestionGroup question, List<UserAnswer> userAnswers) {
        this.setBookId(bookId);
        this.setData(UserAnswerData.buildQuestionAnswerData(question, userAnswers, dataPackage));
        return this;
    }

    public UserAnswerNodeData toNodeProgress(String dataPackage, String nodeId, String nodeType) {
        this.setData(UserAnswerData.buildNodeProgress(dataPackage, nodeId, nodeType));
        return this;
    }
    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public UserAnswerData getData() {
        return data;
    }

    public void setData(UserAnswerData data) {
        this.data = data;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookVersionNumber() {
        return bookVersionNumber;
    }

    public void setBookVersionNumber(String bookVersionNumber) {
        this.bookVersionNumber = bookVersionNumber;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getLearnType() {
        return learnType;
    }

    public void setLearnType(String learnType) {
        this.learnType = learnType;
    }
}
