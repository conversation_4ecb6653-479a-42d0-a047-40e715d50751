package com.unipus.digitalbook.model.enums;

public enum DeleteEnum {
    DELETE(1, "已删除"),
    UNDELETE(0, "未删除");

    private final Integer code;
    private final String description;

    DeleteEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static DeleteEnum getStatus(Integer code) {
        for (DeleteEnum statusEnum : DeleteEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
