package com.unipus.digitalbook.model.enums;

public enum EnableEnum {
    ENABLE(1, "有效"),
    DISABLE(0, "无效");

    private final Integer code;
    private final String description;

    EnableEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static EnableEnum getStatus(Integer code) {
        for (EnableEnum statusEnum : EnableEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
