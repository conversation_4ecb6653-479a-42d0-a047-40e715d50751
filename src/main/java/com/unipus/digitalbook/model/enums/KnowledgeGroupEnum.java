package com.unipus.digitalbook.model.enums;

public enum KnowledgeGroupEnum {
    GROUP(1, "组"),
    NOT_GROUP(0, "非组");

    private final Integer code;
    private final String description;

    KnowledgeGroupEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static KnowledgeGroupEnum getStatus(Integer code) {
        for (KnowledgeGroupEnum statusEnum : KnowledgeGroupEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
