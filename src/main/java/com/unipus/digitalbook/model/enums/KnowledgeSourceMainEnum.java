package com.unipus.digitalbook.model.enums;

public enum KnowledgeSourceMainEnum {
    MAIN(1, "主"),
    SUB(0, "子");

    private final Integer code;
    private final String description;

    KnowledgeSourceMainEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static KnowledgeSourceMainEnum getStatus(Integer code) {
        for (KnowledgeSourceMainEnum statusEnum : KnowledgeSourceMainEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
