package com.unipus.digitalbook.model.enums;

public enum KnowledgeSourceStatusEnum {
    DISABLE(0, "无效"),
    ENABLE(1, "有效"),
    ;
    private final Integer code;
    private final String description;

    KnowledgeSourceStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static KnowledgeSourceStatusEnum getStatus(Integer code) {
        for (KnowledgeSourceStatusEnum statusEnum : KnowledgeSourceStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
