package com.unipus.digitalbook.model.enums;

public enum KnowledgeSourceTypeEnum {
    TEXT(1, "文本"),
    VIDEO(2, "视频"),
    QUESTION(3, "题"),
    AUDIO(4, "音频");
    ;
    private final Integer code;
    private final String description;

    KnowledgeSourceTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static KnowledgeSourceTypeEnum getStatus(Integer code) {
        for (KnowledgeSourceTypeEnum statusEnum : KnowledgeSourceTypeEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
