package com.unipus.digitalbook.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 试卷实例关系类型枚举
 */
 @Getter
public enum PaperInstanceRelationTypeEnum {
    RECOMMEND(1, "推荐关系");

    private final Integer code;
    private final String desc;

    PaperInstanceRelationTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PaperInstanceRelationTypeEnum of(Integer code) {
        for (PaperInstanceRelationTypeEnum relationTypeEnum : values()) {
            if (relationTypeEnum.code.equals(code)) {
                return relationTypeEnum;
            }
        }
        return null;
    }

    public boolean match(PaperInstanceRelationTypeEnum modeEnum) {
        return modeEnum == null ? Boolean.FALSE : modeEnum.match(this.code);
    }

    public boolean match(Integer status) {
        return Objects.equals(this.code, status);
    }
 }