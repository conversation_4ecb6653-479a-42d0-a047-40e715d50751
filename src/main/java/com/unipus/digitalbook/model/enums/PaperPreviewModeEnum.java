package com.unipus.digitalbook.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 试卷预览模式枚举
 */
@Getter
public enum PaperPreviewModeEnum {
    TEACHER(1,"教师模式"),
    STUDENT(2,"学生模式");

    private final Integer code;
    private final String desc;

    PaperPreviewModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc =desc;
    }

    public static PaperPreviewModeEnum of(Integer code) {
        for (PaperPreviewModeEnum modeEnum : values()) {
            if (modeEnum.match(code)) {
                return modeEnum;
            }
        }
        return null;
    }

    public boolean match(PaperPreviewModeEnum modeEnum) {
        return modeEnum==null ? Boolean.FALSE : modeEnum.match(this.code);
    }

    public boolean match(Integer status) {
        return Objects.equals(this.code, status);
    }
}
