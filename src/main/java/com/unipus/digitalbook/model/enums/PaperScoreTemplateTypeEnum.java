package com.unipus.digitalbook.model.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Arrays;
import java.util.Optional;

/**
 * 模板类型
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum PaperScoreTemplateTypeEnum {
    CHALLENGE(1, "挑战评价"),
    DIAGNOSTIC(2, "诊断评价");

    private final Integer code;
    private final String desc;

    PaperScoreTemplateTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static Optional<PaperScoreTemplateTypeEnum> getByCode(Integer code){
        if (ObjectUtils.isEmpty(code)){
            return Optional.empty();
        }

        return Arrays.stream(PaperScoreTemplateTypeEnum.values()).filter(p -> p.getCode().equals(code)).findFirst();
    }

    public boolean match(Integer code) {
        return this.code.equals(code);
    }

    public static PaperScoreTemplateTypeEnum getByPaperType(PaperTypeEnum paperType) {
        if (paperType == null) {
            return null;
        }
        return switch (paperType) {
            case PaperTypeEnum.REGULAR -> null;
            case PaperTypeEnum.CHALLENGE -> PaperScoreTemplateTypeEnum.CHALLENGE;
            case PaperTypeEnum.DIAGNOSTIC ->  PaperScoreTemplateTypeEnum.DIAGNOSTIC;
        };
    }
}
