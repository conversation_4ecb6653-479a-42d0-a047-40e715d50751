package com.unipus.digitalbook.model.enums;

/**
 * 题目查询来源
 */
public enum QuestionQuerySourceEnum {
    CHAPTER("chapter"),
    PAPER("paper");
    private final String source;

    QuestionQuerySourceEnum(String source) {
        this.source = source;
    }

    public static QuestionQuerySourceEnum getQuerySource(String source) {
        for (QuestionQuerySourceEnum value : QuestionQuerySourceEnum.values()) {
            if (value.source.equals(source)) {
                return value;
            }
        }
        throw new IllegalArgumentException("没有找到对应的查询源");
    }
}
