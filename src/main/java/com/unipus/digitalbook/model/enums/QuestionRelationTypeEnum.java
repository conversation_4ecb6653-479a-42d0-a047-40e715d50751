package com.unipus.digitalbook.model.enums;

import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * 试卷类型枚举类
 */
@Getter
public enum QuestionRelationTypeEnum {
    COMMON(1, "common", "普通题"),
    RECOMMEND(2, "recommend", "推荐题");

    private final Integer code;
    private final String name;
    private final String desc;

    QuestionRelationTypeEnum(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc =desc;
    }

    public static QuestionRelationTypeEnum getEnumByName(String name) {
        if (!StringUtils.hasText(name)){
            return null;
        }
        for (QuestionRelationTypeEnum typeEnum : values()) {
            if (typeEnum.getName().equals(name)){
                return typeEnum;
            }
        }
        return null;
    }

    public Boolean match(String name){
        return this.name.equals(name);
    }

}
