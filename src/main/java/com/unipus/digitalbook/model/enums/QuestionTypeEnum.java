package com.unipus.digitalbook.model.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 题型枚举
 */
@Getter
public enum QuestionTypeEnum {

    SINGLE_CHOICE(1, "single_choice", "单选题"),
    MULTI_CHOICE(2, "multi_choice", "多选题"),
    SHORT_ANSWER(3, "short_answer", "简答题"),
    MULTI_MEDIA_UPLOAD(4, "multi_media_upload", "多媒体上传"),
    TRANSLATION(5, "translation", "翻译题"),
    WRITING(6, "writing", "写作题"),
    ORAL_PERSONAL_STATE(7, "oral_personal_state", "个人陈述题"),
    VOCABULARY(8, "vocabulary", "单词本"),
    FILL_BLANKS(9, "fill_blanks", "填空题"),
    FILL_BLANKS_CHOICE(10, "fill_blanks_choice", "选词填空"),
    FILL_BLANKS_DROPDOWN(11, "fill_blanks_dropdown", "下拉选填空"),
    DISCUSSION(12, "discussion", "讨论"),
    ORAL_SENTENCE_SCOOP(13, "oral_sentence_scoop", "句子跟读（挖空）"),
    SEQUENCE(14, "sequence", "排序"),
    RICH_TEXT_READ(15, "rich_text_read", "精读课文"),
    ROLE_PLAY_SCOOP(16, "role_play_scoop", "角色扮演（挖空)"),
    TEXT_MATCH(17, "text_match", "文本匹配"),
    EVALUATION(18, "evaluation", "量表"),
    TRUE_FALSE(19, "true_false", "判断"),
    ;

    private static final Map<Integer, QuestionTypeEnum> CODE_MAP = new HashMap<>();

    private static final Map<String, QuestionTypeEnum> RETURN_ANSWER_MAP = new HashMap<>();
    // 客观题
    private static final Map<Integer, QuestionTypeEnum> OBJECTIVE_MAP = new HashMap<>();
    static {
        for (QuestionTypeEnum questionTypeEnum : QuestionTypeEnum.values()) {
            CODE_MAP.put(questionTypeEnum.getCode(), questionTypeEnum);
            if (questionTypeEnum == ORAL_SENTENCE_SCOOP || questionTypeEnum == VOCABULARY || questionTypeEnum == ROLE_PLAY_SCOOP) {
                RETURN_ANSWER_MAP.put(questionTypeEnum.getName(), questionTypeEnum);
            }
        }

        // 初始化客观题映射
        OBJECTIVE_MAP.put(SINGLE_CHOICE.getCode(), SINGLE_CHOICE);
        OBJECTIVE_MAP.put(MULTI_CHOICE.getCode(), MULTI_CHOICE);
        OBJECTIVE_MAP.put(FILL_BLANKS.getCode(), FILL_BLANKS);
        OBJECTIVE_MAP.put(FILL_BLANKS_CHOICE.getCode(), FILL_BLANKS_CHOICE);
        OBJECTIVE_MAP.put(FILL_BLANKS_DROPDOWN.getCode(), FILL_BLANKS_DROPDOWN);
        OBJECTIVE_MAP.put(SEQUENCE.getCode(), SEQUENCE);
        OBJECTIVE_MAP.put(TEXT_MATCH.getCode(), TEXT_MATCH);
        OBJECTIVE_MAP.put(TRUE_FALSE.getCode(), TRUE_FALSE);
    }

    // 题型ID
    private final Integer code;
    // 题型名称
    private final String name;
    // 题型描述
    private final String desc;

    QuestionTypeEnum(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public static QuestionTypeEnum getEnumByName(String name) {
        for (QuestionTypeEnum questionTypeEnum : QuestionTypeEnum.values()) {
            if (questionTypeEnum.getName().equals(name)) {
                return questionTypeEnum;
            }
        }
        return null;
    }

    public static QuestionTypeEnum getEnumByCode(Integer code) {
        return CODE_MAP.get(code);
    }

    public static Integer getCodeByName(String name) {
        for (QuestionTypeEnum questionTypeEnum : QuestionTypeEnum.values()) {
            if (questionTypeEnum.getName().equals(name)) {
                return questionTypeEnum.getCode();
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        return CODE_MAP.containsKey(code) ? CODE_MAP.get(code).getName() : null;
    }

    public static boolean isReturnAnswer(String name) {
        return RETURN_ANSWER_MAP.containsKey(name);
    }

    public static boolean isObjective(Integer code) {
        return OBJECTIVE_MAP.containsKey(code);
    }

    public static boolean isSubjective(Integer code) {
        return !OBJECTIVE_MAP.containsKey(code);
    }
}
