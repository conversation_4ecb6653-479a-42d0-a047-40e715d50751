package com.unipus.digitalbook.model.enums;

import lombok.Getter;

/**
 * 试卷类型枚举类
 */
@Getter
public enum ReaderTypeEnum {
    STUDENT(1, "学生"),
    TEACHER(2, "老师");

    private final Integer code;
    private final String name;

    ReaderTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ReaderTypeEnum getByCode(Integer code) {
        if(code == null) {
            return null;
        }
        for (ReaderTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public Boolean match(ReaderTypeEnum readerType) {
        if(readerType==null){
            return false;
        }
        return this.code.equals(readerType.getCode());
    }

    public Boolean match(Integer code){
        return this.code.equals(code);
    }
}
