package com.unipus.digitalbook.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 单元测试模式枚举类
 */
@Getter
public enum UnitTestModeEnum {
    DIAGNOSIS(1, "诊断模式"),
    RECOMMENDED(2, "推荐模式");

    private final Integer code;
    private final String desc;

    UnitTestModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc =desc;
    }

    public static UnitTestModeEnum of(Integer status) {
        for (UnitTestModeEnum mode : values()) {
            if (mode.match(status)) {
                return mode;
            }
        }
        return null;
    }

    public boolean match(UnitTestModeEnum modeEnum) {
        return modeEnum==null ? Boolean.FALSE : modeEnum.match(this.code);
    }

    public boolean match(Integer status) {
        return Objects.equals(this.code, status);
    }
}
