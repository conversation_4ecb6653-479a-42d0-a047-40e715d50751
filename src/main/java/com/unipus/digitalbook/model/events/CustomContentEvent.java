package com.unipus.digitalbook.model.events;

import com.unipus.digitalbook.model.enums.EventTypeEnum;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 自建内容事件
 */
@Getter
public class CustomContentEvent extends ApplicationEvent {
    
    /**
     * 自建内容业务ID
     */
    private final String bizId;
    
    /**
     * 事件类型
     */
    private final EventTypeEnum eventType;
    
    /**
     * 操作用户ID
     */
    private final Long opsUserId;

    public CustomContentEvent(Object source, String bizId, EventTypeEnum eventType, Long opsUserId) {
        super(source);
        this.bizId = bizId;
        this.eventType = eventType;
        this.opsUserId = opsUserId;
    }
}
