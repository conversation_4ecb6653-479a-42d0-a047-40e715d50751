package com.unipus.digitalbook.model.events;

import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;

public class UserActionEvent {
    private Long tenantId;
    private String openId;
    private Long chapterVersionId;
    private String chapterId;
    private ChapterNode chapterNode;
    private int offset;

    private String ip;

    private String dataPackage;

    public UserActionEvent(){}

    public UserActionEvent(UserAction userAction, ChapterNode chapterNode, String chapterId, Long chapterVersionId, int offset) {
        this.tenantId = userAction.getTenantId();
        this.openId = userAction.getOpenId();
        this.chapterNode = chapterNode;
        this.chapterVersionId = chapterVersionId;
        this.chapterId = chapterId;
        this.offset = offset;
        this.ip = userAction.getIp();
        this.dataPackage = userAction.getDataPackage();
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Long getChapterVersionId() {
        return chapterVersionId;
    }

    public void setChapterVersionId(Long chapterVersionId) {
        this.chapterVersionId = chapterVersionId;
    }

    public ChapterNode getChapterNode() {
        return chapterNode;
    }

    public void setChapterNode(ChapterNode chapterNode) {
        this.chapterNode = chapterNode;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getDataPackage() {
        return dataPackage;
    }

    public void setDataPackage(String dataPackage) {
        this.dataPackage = dataPackage;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }
}
