package com.unipus.digitalbook.model.events;

import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.enums.EventTypeEnum;
import org.springframework.context.ApplicationEvent;

import java.util.List;

public class UserAnswerEvent extends ApplicationEvent {

    private List<UserAnswer> userAnswers;

    private BigQuestionGroup question;

    private EventTypeEnum eventType;

    private String chapterId;

    private Long chapterVersionId;

    public UserAnswerEvent(Object source, EventTypeEnum eventType, String chapterId, Long chapterVersionId,BigQuestionGroup question, List<UserAnswer> userAnswers) {
        super(source);
        this.chapterId = chapterId;
        this.chapterVersionId = chapterVersionId;
        this.userAnswers = userAnswers;
        this.question = question;
        this.eventType = eventType;
    }

    public List<UserAnswer> getUserAnswers() {
        return userAnswers;
    }

    public void setUserAnswers(List<UserAnswer> userAnswers) {
        this.userAnswers = userAnswers;
    }

    public BigQuestionGroup getQuestion() {
        return question;
    }

    public void setQuestion(BigQuestionGroup question) {
        this.question = question;
    }

    public EventTypeEnum getEventType() {
        return eventType;
    }

    public void setEventType(EventTypeEnum eventType) {
        this.eventType = eventType;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public Long getChapterVersionId() {
        return chapterVersionId;
    }

    public void setChapterVersionId(Long chapterVersionId) {
        this.chapterVersionId = chapterVersionId;
    }
}
