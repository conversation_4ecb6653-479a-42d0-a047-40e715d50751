package com.unipus.digitalbook.model.params;
import com.unipus.digitalbook.model.entity.BackendUserInfo;
import com.unipus.digitalbook.model.enums.ReaderTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

/**
 * 内部认证认证参数类
 * 此类用于封装与认证相关的参数，包括用户基本信息和客户端信息
 */
@Schema(description = "内部认证参数类")
public class BackendAuthParam implements Params{
    @Schema(description = "openID")
    private String openId;

    @Schema(description = "读者类型（1:学生/2:老师）")
    private Integer readerType;

    @Schema(description = "用户数据包")
    private String dataPackage;

    @Schema(description = "客户端ID")
    private Long appId;

    @Schema(description = "环境分区")
    private String envPartition;

    @Override
    public void valid() {
        if(!StringUtils.hasText(openId)){
            throw new IllegalArgumentException("openID不能为空");
        }
        if(readerType==null){
            throw new IllegalArgumentException("读者类型（1:学生/2:老师）不能为空");
        }
        if(!StringUtils.hasText(dataPackage)){
            throw new IllegalArgumentException("用户数据包不能为空");
        }
        if(appId==null){
            throw new IllegalArgumentException("客户端ID不能为空");
        }
    }

    public BackendUserInfo toEntity(){
        BackendUserInfo entity = new BackendUserInfo();
        entity.setOpenId(openId);
        entity.setReaderType(ReaderTypeEnum.getByCode(readerType));
        entity.setDataPackage(dataPackage);
        entity.setAppId(appId);
        entity.setEnvPartition(envPartition);
        return entity;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Integer getReaderType() {
        return readerType;
    }

    public void setReaderType(Integer readerType) {
        this.readerType = readerType;
    }

    public String getDataPackage() {
        return dataPackage;
    }

    public void setDataPackage(String dataPackage) {
        this.dataPackage = dataPackage;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getEnvPartition() {
        return envPartition;
    }

    public void setEnvPartition(String envPartition) {
        this.envPartition = envPartition;
    }
}





