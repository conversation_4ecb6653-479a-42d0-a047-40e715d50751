package com.unipus.digitalbook.model.params;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public interface Params extends Serializable {

    void valid();


    class ValidationResult {
        private List<String> errors = new ArrayList<>();

        public void addError(String field, String message) {
            errors.add(field + ": " + message);
        }

        public boolean hasErrors() {
            return !errors.isEmpty();
        }

        public List<String> getErrors() {
            return errors;
        }

        public String getErrorMessage() {
            StringBuilder sb = new StringBuilder();
            for (String error : errors) {
                sb.append(error).append("\n");
            }
            return sb.toString();
        }

        public String getFirstErrorMessage() {
            return errors.getFirst();
        }
    }
}
