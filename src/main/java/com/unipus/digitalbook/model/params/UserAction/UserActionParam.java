package com.unipus.digitalbook.model.params.UserAction;

import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import io.swagger.v3.oas.annotations.media.Schema;

public class UserActionParam {
    @Schema(description = "章节id")
    private String chapterId;
    @Schema(description = "章节版本")
    private String chapterVersionNumber;
    @Schema(description = "节点ID")
    private String nodeId;

    @Schema(description = "节点类型")
    private String nodeType;
    @Schema(description = "行为类型：finishNode")
    private String actionType;
    @Schema(description = "行为参数: 行为开始时间")
    private Long a;
    @Schema(description = "行为参数: 行为结束时间")
    private Long b;

    public UserAction toEntity(Long tenantId, String openId, Long chapterVersionId, ChapterNode chapterNode, String dataPackage, String ip) {
        UserAction userAction = new UserAction();
        userAction.setTenantId(tenantId);
        userAction.setOpenId(openId);
        userAction.setChapterId(chapterId);
        userAction.setChapterVersionId(chapterVersionId);
        userAction.setChapterNode(chapterNode);
        userAction.setIp(ip);
        userAction.setDataPackage(dataPackage);
        userAction.setA(a);
        userAction.setB(b);
        return userAction;
    }
    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getChapterVersionNumber() {
        return chapterVersionNumber;
    }

    public void setChapterVersionNumber(String chapterVersionNumber) {
        this.chapterVersionNumber = chapterVersionNumber;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }
    public Long getA() {
        return a;
    }

    public void setA(Long a) {
        this.a = a;
    }

    public Long getB() {
        return b;
    }

    public void setB(Long b) {
        this.b = b;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }
}
