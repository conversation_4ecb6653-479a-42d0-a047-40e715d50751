package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import com.unipus.digitalbook.model.params.Params;
import com.unipus.digitalbook.model.params.question.BigQuestionGroupParam;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Schema(description = "增加章节版本参数")
public class AddChapterVersionParam implements Params {

    @Schema(description = "章节ID")
    private String chapterId;

    @Schema(description = "章节内容")
    private String content;

    @Schema(description = "章节资源")
    private String resource;

    @Schema(description = "学生内容")
    private String studentContent;

    @Schema(description = "章节的目录")
    private String catalog;

    @Schema(description = "章节的头图")
    private String headerImg;

    @Schema(description = "当前章节下所有的内容节点")
    private List<ChapterNodeParam> chapterNodeList;

    @Schema(description = "章节的题")
    private List<BigQuestionGroupParam> questionList;

    @Schema(description = "试卷引用列表")
    private List<PaperReferenceSimpleParam> paperReferenceList;

    public ChapterVersion toEntity(Long userId) {
        ChapterVersion.Builder builder= ChapterVersion.Builder.getInstance();
        builder.chapterId(chapterId)
                .content(content)
                .versionNumber()
                .resources(resource)
                .studentContent(studentContent)
                .catalog(catalog)
                .headerImg(headerImg)
                .questionList(Optional.ofNullable(questionList)
                        .map(qList -> qList.stream().map(q -> q.toEntity(userId, null)).toList())
                        .orElse(Collections.emptyList()))
                .paperReferenceList(Optional.ofNullable(paperReferenceList)
                        .map(pList -> pList.stream().map(PaperReferenceSimpleParam::toEntity).toList())
                        .orElse(Collections.emptyList()))
                .chapterNodeParamList(chapterNodeList)
                .createBy(userId);
        return builder.build();
    }
    @Override
    public void valid() {
        if (StringUtils.isBlank(chapterId)) {
            throw new IllegalArgumentException("章节ID不能为空");
        }
        if (content==null){
            throw new IllegalArgumentException("章节内容不能为空,清空内容传空字符串");
        }
        if (!chapterNodeList.isEmpty()){
            chapterNodeList.forEach(ChapterNodeParam::valid);
        }
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getHeaderImg() {
        return headerImg;
    }

    public void setHeaderImg(String headerImg) {
        this.headerImg = headerImg;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public List<BigQuestionGroupParam> getQuestionList() {
        return questionList;
    }

    public void setQuestionList(List<BigQuestionGroupParam> questionList) {
        this.questionList = questionList;
    }

    public List<PaperReferenceSimpleParam> getPaperReferenceList() {
        return paperReferenceList;
    }

    public void setPaperReferenceList(List<PaperReferenceSimpleParam> paperReferenceList) {
        this.paperReferenceList = paperReferenceList;
    }

    public String getStudentContent() {
        return studentContent;
    }

    public void setStudentContent(String studentContent) {
        this.studentContent = studentContent;
    }

    public String getCatalog() {
        return catalog;
    }

    public void setCatalog(String catalog) {
        this.catalog = catalog;
    }

    public List<ChapterNodeParam> getChapterNodeList() {
        return chapterNodeList;
    }

    public void setChapterNodeList(List<ChapterNodeParam> chapterNodeList) {
        this.chapterNodeList = chapterNodeList;
    }
}
