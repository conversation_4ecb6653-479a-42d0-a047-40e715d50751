package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

@Schema(description = "教材封面与简介参数")
public class BookCoverIntroParam implements Params {

    @Schema(description = "关联教材ID", example = "B001")
    private String bookId;

    @Schema(description = "教材详细介绍", example = "这是一本介绍计算机科学基础的教材，适合初学者学习。")
    private String description;

    @Schema(description = "PC端封面图片地址", example = "https://example.com/pc-cover.jpg")
    private String pcCoverUrl;

    @Schema(description = "APP横版封面图片地址", example = "https://example.com/app-horizontal-cover.jpg")
    private String appHorizontalCoverUrl;

    @Schema(description = "APP竖版封面图片地址", example = "https://example.com/app-vertical-cover.jpg")
    private String appVerticalCoverUrl;

    @Schema(description = "浅色", example = "#FFFFFF")
    private String lightColor;

    @Schema(description = "深色", example = "#000000")
    private String darkColor;

    public Book toEntity() {
        Book book = new Book();
        book.setId(this.getBookId());
        book.setDescription(this.getDescription());
        book.setPcCoverUrl(this.getPcCoverUrl());
        book.setAppHorizontalCoverUrl(this.getAppHorizontalCoverUrl());
        book.setAppVerticalCoverUrl(this.getAppVerticalCoverUrl());
        book.setLightColor(this.getLightColor());
        book.setDarkColor(this.getDarkColor());
        return book;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPcCoverUrl() {
        return pcCoverUrl;
    }

    public void setPcCoverUrl(String pcCoverUrl) {
        this.pcCoverUrl = pcCoverUrl;
    }

    public String getAppHorizontalCoverUrl() {
        return appHorizontalCoverUrl;
    }

    public void setAppHorizontalCoverUrl(String appHorizontalCoverUrl) {
        this.appHorizontalCoverUrl = appHorizontalCoverUrl;
    }

    public String getAppVerticalCoverUrl() {
        return appVerticalCoverUrl;
    }

    public void setAppVerticalCoverUrl(String appVerticalCoverUrl) {
        this.appVerticalCoverUrl = appVerticalCoverUrl;
    }

    public String getLightColor() {
        return lightColor;
    }

    public void setLightColor(String lightColor) {
        this.lightColor = lightColor;
    }

    public String getDarkColor() {
        return darkColor;
    }

    public void setDarkColor(String darkColor) {
        this.darkColor = darkColor;
    }

    @Override
    public void valid() {
        if (bookId == null) {
            throw new IllegalArgumentException("bookId不能为空");
        }
        if (!StringUtils.hasText(pcCoverUrl)) {
            throw new IllegalArgumentException("请上传PC端封面");
        }
        if (!StringUtils.hasText(appVerticalCoverUrl)) {
            throw new IllegalArgumentException("请上传APP端竖版封面");
        }
    }
}
