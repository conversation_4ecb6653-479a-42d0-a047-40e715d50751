package com.unipus.digitalbook.model.params.book;

import com.unipus.digitalbook.model.entity.book.BookTemporarySnapshot;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

/**
 * 教材临时快照参数
 */
@Schema(description = "教材临时快照参数")
public class BookTemporarySnapshotParam implements Params {

    @Schema(description = "教材ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bookId;

    @Schema(description = "章节ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String chapterId;

    @Schema(description = "快照内容 JSON字符串")
    private String content;

    @Override
    public void valid() {
        ValidationResult result = new ValidationResult();

        if (StringUtils.isBlank(bookId)) {
            result.addError("bookId", "教材ID不能为空");
        }

        if (StringUtils.isBlank(chapterId)) {
            result.addError("chapterId", "章节ID不能为空");
        }

        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getErrorMessage());
        }
    }

    /**
     * 转换为实体对象
     */
    public BookTemporarySnapshot toEntity() {
        BookTemporarySnapshot snapshot = new BookTemporarySnapshot();
        snapshot.setBookId(bookId);
        snapshot.setChapterId(chapterId);
        snapshot.setContent(content);
        return snapshot;
    }

    // Getters and Setters
    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
