package com.unipus.digitalbook.model.params.content;

import com.unipus.digitalbook.model.entity.content.CustomContent;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "教材自建内容名称信息参数")
public class CustomContentNameParam implements Params {

    @Schema(description = "内容业务ID")
    private String bizId;

    @Schema(description = "内容名称")
    private String name;

    public CustomContent toEntity(Long tenantId, String openId, String dataPackage, Long userId) {
        CustomContent customContent = new CustomContent();
        customContent.setBizId(this.bizId);
        customContent.setName(this.name);
        customContent.setTenantId(tenantId);
        customContent.setUpdateBy(userId);
        customContent.setOpenId(openId);
        customContent.setDataPackage(dataPackage);
        return customContent;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public void valid() {

    }
}
