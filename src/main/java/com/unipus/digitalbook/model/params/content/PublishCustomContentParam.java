package com.unipus.digitalbook.model.params.content;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "发布自建内容信息参数")
public class PublishCustomContentParam implements Params {

    @Schema(description = "要发布的自建内容id列表")
    private List<String> publishContentIds;

    @Schema(description = "要删除的自建内容id列表")
    private List<String> deleteContentIds;

    public List<String> getPublishContentIds() {
        return publishContentIds;
    }

    public void setPublishContentIds(List<String> publishContentIds) {
        this.publishContentIds = publishContentIds;
    }

    public List<String> getDeleteContentIds() {
        return deleteContentIds;
    }

    public void setDeleteContentIds(List<String> deleteContentIds) {
        this.deleteContentIds = deleteContentIds;
    }

    @Override
    public void valid() {

    }
}
