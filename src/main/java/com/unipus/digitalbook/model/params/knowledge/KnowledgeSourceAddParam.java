package com.unipus.digitalbook.model.params.knowledge;

import com.unipus.digitalbook.model.enums.KnowledgeGroupEnum;
import com.unipus.digitalbook.model.enums.KnowledgeSourceEnum;
import com.unipus.digitalbook.model.enums.KnowledgeSourceMainEnum;
import com.unipus.digitalbook.model.enums.KnowledgeSourceTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeResourceAddRequest;
import lombok.Data;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025年06月05日 18:10
 */
@Data
public class KnowledgeSourceAddParam implements Params {

    /**
     * 课程Id 例如：course-v2:Unipus+1234Bb+20220729
     */
    private String courseIdStr;

    /**
     * 课程Id
     */
    private Long courseId;

    /**
     * 单元Id
     */
    private String unitId;

    /**
     * 任务Id
     */
    private String taskId;

    /**
     * 资源类型 1 文字段落 2 视频 3 题目 4 音频
     */
    private Integer type;

    /**
     * 视频开始时间
     */
    private Integer startTime;

    /**
     * 视频帧图
     */
    private String startPictureUrl;

    /**
     * 视频结束时间
     */
    private Integer endTime;

    /**
     * 课程图谱Id
     */
    private Long courseKnowledgeId;

    /**
     * 多媒体文件key
     */
    private String multimediaKey;

    /**
     * 多媒体文件index
     */
    private String multimediaIndex;

    /**
     * 多媒体文件name
     */
    private String multimediaName;

    /**
     * 资源url
     */
    private String sourceUrl;

    /**
     * 是否组 0-非组 1-组
     */
    private Integer groupStatus = KnowledgeGroupEnum.NOT_GROUP.getCode();

    /**
     * 三方图谱Id
     */
    private String knowledgeId;

    /**
     * 三方子图Id
     */
    private String graphId;

    /**
     * 三方资源Id
     */
    private String sourceId;
    /**
     * 资源来源
     */
    private String source;
    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 资源目录
     */
    private String dir;

    /**
     * 资源描述
     */
    private String description;

    /**
     * 资源绝对地址
     */
    private Integer location;
    /**
     * 选中的知识节点Id列表
     * 知识标签Id列表
     */
    private List<String> nodeIds;
    /**
     * 资源标签Id列表
     */
    private List<String> labelIds;
    /**
     * 资源组的子资源url链接数组
     */
    private List<String> subSourceUrls;

    private static final long serialVersionUID = 1L;

    @Override
    public void valid() {
        // 创建一个新的ValidationResult实例，用于存储验证结果
        ValidationResult result = new ValidationResult();
        // 教材资源信息验证
        if (StringUtil.isBlank(this.getCourseIdStr())) {
            result.addError("教材IdStr为空", "请输入教材IdStr");
        }

        if (StringUtil.isBlank(this.getUnitId())) {
            result.addError("教材单元名称为空", "请输入教材单元");
        }
        if (null == this.getCourseKnowledgeId()) {
            result.addError("教材知识图谱关系Id为空", "请输入教材知识图谱关系Id");
        }

        if (null == this.getType()) {
            result.addError("资源类型为空", "请输入资源类型 1 文字段落 2 视频 3 题目 4 音频");
        }
        if (CollectionUtils.isEmpty(this.getNodeIds())) {
            result.addError("挂载知识节点集合为空", "请选择挂载的知识点");
        }
        if (StringUtil.isBlank(this.getSource())) {
            result.addError("教材资源链接源头为空", "请写入教材资源链接源头，1:ipublish 2:cms 3:UAi");
        }

        if (StringUtil.isBlank(this.getSourceUrl())) {
            result.addError("教材资源链接为空", "请勾选教材资源信息");
        }

        if (null == this.getLocation()) {
            result.addError("选中的教材资源位置信息为空", "请输入教材资源位置信息");
        }

        if (Objects.equals(KnowledgeSourceTypeEnum.VIDEO.getCode(), this.getType())) {
            if (null == this.getStartTime()) {
                result.addError("视频开始时间为空", "请输入视频开始时间");
            }

            if (StringUtil.isBlank(this.getMultimediaKey())) {
                result.addError("视频标识为空", "请输入视频主标识");
            }
        }

        // 教材用途区域验证
        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getFirstErrorMessage());
        }
    }


    /**
     * 主资源上传到三方图谱的资源表信息
     *
     * @return
     */
    public KnowledgeResourceAddRequest toThirdKnowledgeAddRequest(String sourceUrl) {
        KnowledgeResourceAddRequest request = new KnowledgeResourceAddRequest();
        request.setKnowledgeId(this.getKnowledgeId());
        request.setName(this.getResourceName());
        request.setType(this.getType() + "");
        request.setUrl(sourceUrl);
        request.setSource(KnowledgeSourceEnum.getStatus(this.getSource()).getDescription());
        request.setDir(this.getDir());
        request.setNodeIds(this.getNodeIds());
        request.setLabelIds(this.getLabelIds());
        request.setDescription(this.getDescription());
        return request;
    }

    /**
     * 目录点添加
     *
     * @param knowledgeResourceId
     * @param opUserId
     * @return
     */
    public CourseKnowledgeSourceInfoPO toPo(String knowledgeResourceId, Long opUserId) {

        CourseKnowledgeSourceInfoPO entity = new CourseKnowledgeSourceInfoPO();
        entity.setCourseIdStr(this.getCourseIdStr());
        entity.setCourseId(this.getCourseId());
        entity.setUnitId(this.getUnitId());
        entity.setTaskId(this.getTaskId());
        entity.setDir(this.getDir());
        entity.setType(this.getType());
        entity.setStartTime(this.getStartTime());
        entity.setStartPictureUrl(this.getStartPictureUrl());
        entity.setEndTime(this.getEndTime());
        entity.setCourseKnowledgeId(this.getCourseKnowledgeId());
        entity.setMultimediaKey(this.getMultimediaKey());
        entity.setMultimediaIndex(this.getMultimediaIndex());
        entity.setMultimediaName(this.getMultimediaName());
        entity.setSourceUrl(this.getSourceUrl());
        entity.setSourceHash(DigestUtils.md5Hex(this.getSourceUrl()));
        entity.setGroupStatus(this.getGroupStatus());
        entity.setMainStatus(KnowledgeSourceMainEnum.MAIN.getCode());
        entity.setKnowledgeId(this.getKnowledgeId());
        entity.setGraphId(this.getGraphId());
        entity.setKnowledgeSourceId(knowledgeResourceId);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setCreateBy(opUserId);
        entity.setUpdateBy(opUserId);
        entity.setLocation(this.getLocation());
        return entity;
    }

    public List<CourseKnowledgeSourceInfoPO> toSubPo(CourseKnowledgeSourceInfoPO mainSourceInfoPO) {
        List<CourseKnowledgeSourceInfoPO> subEntityList = new ArrayList<>();

        if (KnowledgeSourceTypeEnum.TEXT.getCode().equals(this.getType())
                && KnowledgeGroupEnum.GROUP.getCode().equals(this.getGroupStatus())
                && !CollectionUtils.isEmpty(this.getSubSourceUrls())) {

            //子目录数据填充
            for (String subUrl : this.getSubSourceUrls()) {
                CourseKnowledgeSourceInfoPO subSourceInfoPo = new CourseKnowledgeSourceInfoPO();
                BeanUtils.copyProperties(mainSourceInfoPO, subSourceInfoPo);
                subSourceInfoPo.setParentId(mainSourceInfoPO.getId());
                subSourceInfoPo.setMainStatus(KnowledgeSourceMainEnum.SUB.getCode());
                subSourceInfoPo.setSourceUrl(subUrl);
                subSourceInfoPo.setSourceHash(DigestUtils.md5Hex(subUrl));
                subEntityList.add(subSourceInfoPo);
            }
        }
        return subEntityList;
    }

    /**
     * 当前是否是组
     * @return
     */
    public boolean groupFlag() {
        if (KnowledgeSourceTypeEnum.TEXT.getCode().equals(this.getType())
                && KnowledgeGroupEnum.GROUP.getCode().equals(this.getGroupStatus())
                && !CollectionUtils.isEmpty(this.getSubSourceUrls())) {

            return true;
        }
        return false;
    }
}
