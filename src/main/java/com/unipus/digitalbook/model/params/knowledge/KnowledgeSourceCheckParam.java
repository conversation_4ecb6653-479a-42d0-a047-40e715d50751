package com.unipus.digitalbook.model.params.knowledge;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.poi.util.StringUtil;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年06月05日 18:10
 */
@Data
public class KnowledgeSourceCheckParam implements Params {

    /**
     * 课程Id 例如：course-v2:Unipus+1234Bb+20220729
     */
    private String courseIdStr;

    /**
     * 单元Id
     */
    private String unitId;

    /**
     * 任务Id
     */
    private String taskId;

    /**
     * 资源类型 1 文字段落 2 视频 3 题目 4 音频
     */
    private Integer type;

    /**
     * 课程图谱Id
     */
    private Long courseKnowledgeId;

    /**
     * 三方图谱Id
     */
    private String knowledgeId;

    private List<String> pendingUrls;
    private List<Long> ignoreIds;

    @Override
    public void valid() {
        // 创建一个新的ValidationResult实例，用于存储验证结果
        ValidationResult result = new ValidationResult();
        // 教材资源信息验证
        if (StringUtil.isBlank(this.getCourseIdStr())) {
            result.addError("教材IdStr为空", "请输入教材IdStr");
        }

        if (StringUtil.isBlank(this.getUnitId())) {
            result.addError("教材单元名称为空", "请输入教材单元");
        }

        if (null == this.getCourseKnowledgeId()) {
            result.addError("教材知识图谱关系Id为空", "请输入教材知识图谱关系Id为空");
        }

        if (null == this.getType()) {
            result.addError("资源类型为空", "请输入资源类型 1 文字段落 2 视频 3 题目 4 音频");
        }
        if (CollectionUtils.isEmpty(this.getPendingUrls())) {
            result.addError("待校验的资源urls为空", "请输入资源url");
        }


        // 教材用途区域验证
        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getFirstErrorMessage());
        }
    }

}
