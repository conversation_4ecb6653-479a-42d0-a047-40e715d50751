package com.unipus.digitalbook.model.params.knowledge;

import com.unipus.digitalbook.model.params.Params;
import com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeResourceUpdateRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年06月05日 18:10
 */
@Data
public class KnowledgeSourceDirUpdateParam implements Params {

    /**
     * 课程资源主键
     */
    private Long courseResourceId;
    /**
     * 知识图谱Id
     */
    private String knowledgeId;
    /**
     * 三方资源主键Id
     */
    private String thirdResourceId;
    /**
     * 目录信息
     */
    private String dir;

    private static final long serialVersionUID = 1L;

    @Override
    public void valid() {
        // 创建一个新的ValidationResult实例，用于存储验证结果
        ValidationResult result = new ValidationResult();
        if (null == this.getCourseResourceId()) {
            result.addError("教材资源Id主键为空", "请输入教材资源Id主键");
        }
        if (null == this.getThirdResourceId()) {
            result.addError("教材三方资源Id主键为空", "请输入教材三方资源Id主键");
        }
        if (StringUtils.isBlank(this.getDir())) {
            result.addError("教材三方资源目录为空", "请输入教材三方资源目录");
        }

        // 教材用途区域验证
        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getFirstErrorMessage());
        }
    }

    public KnowledgeResourceUpdateRequest toThirdKnowledgeResourceUpdateRequest() {
        KnowledgeResourceUpdateRequest request = new KnowledgeResourceUpdateRequest();
        request.setKnowledgeId(this.getKnowledgeId());
        request.setId(this.getThirdResourceId());
        request.setDir(this.getDir());
        return request;
    }

    public CourseKnowledgeSourceInfoPO toPo(Long opUserId) {
        CourseKnowledgeSourceInfoPO mainPO = new CourseKnowledgeSourceInfoPO();
        mainPO.setId(this.getCourseResourceId());
        mainPO.setDir(this.getDir());
        mainPO.setUpdateTime(new Date());
        mainPO.setUpdateBy(opUserId);
        return mainPO;
    }
}
