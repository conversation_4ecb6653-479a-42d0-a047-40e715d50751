package com.unipus.digitalbook.model.params.knowledge;

import com.unipus.digitalbook.model.enums.KnowledgeSourceMainEnum;
import com.unipus.digitalbook.model.enums.KnowledgeSourceTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeResourceUpdateRequest;
import lombok.Data;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025年06月05日 18:10
 */
@Data
public class KnowledgeSourceUpdateParam implements Params {

    /**
     * 课程资源主键
     */
    private Long id;

    /**
     * 三方资源Id
     */
    private String thirdResourceId;

    /**
     * 课程Id 例如：course-v2:Unipus+1234Bb+20220729
     */
    private String courseIdStr;

    /**
     * 课程Id
     */
    private Long courseId;

    /**
     * 单元Id
     */
    private String unitId;

    /**
     * 任务Id
     */
    private String taskId;

    /**
     * 资源类型 1 文字段落 2 视频 3 题目 4 音频
     */
    private Integer type;

    /**
     * 视频开始时间
     */
    private Integer startTime;

    /**
     * 视频开始帧图
     */
    private String startPictureUrl;

    /**
     * 视频结束时间
     */
    private Integer endTime;

    /**
     * 课程图谱Id
     */
    private Long courseKnowledgeId;

    /**
     * 多媒体文件key
     */
    private String multimediaKey;

    /**
     * 多媒体文件index
     */
    private String multimediaIndex;

    /**
     * 多媒体文件name
     */
    private String multimediaName;

    /**
     * 资源在章节的位置信息
     */
    private Integer location;

    /**
     * 是否组 0-非组 1-组
     */
    private Integer groupStatus;

    /**
     * 三方图谱Id
     */
    private String knowledgeId;

    /**
     * 三方子图Id
     */
    private String graphId;

    /**
     * 三方资源Id
     */
    private String sourceId;
    /**
     * 资源来源
     */
    private String source;
    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 资源目录
     */
    private String dir;

    /**
     * 资源描述
     */
    private String description;
    /**
     * 选中的知识节点Id列表
     * 知识标签Id列表
     */
    private List<String> nodeIds;
    /**
     * 资源标签Id列表
     */
    private List<String> labelIds;

    private List<Long> subResourceIds;

    private static final long serialVersionUID = 1L;

    @Override
    public void valid() {
        // 创建一个新的ValidationResult实例，用于存储验证结果
        ValidationResult result = new ValidationResult();
        if (null == this.getId()) {
            result.addError("教材Id主键为空", "请输入教材Id主键");
        }

        // 教材资源信息验证
        if (StringUtil.isBlank(this.getCourseIdStr())) {
            result.addError("教材IdStr为空", "请输入教材IdStr");
        }

        if (StringUtil.isBlank(this.getUnitId())) {
            result.addError("教材单元名称为空", "请输入教材单元");
        }
        if (null == this.getCourseKnowledgeId()) {
            result.addError("教材知识图谱关系Id为空", "请输入教材知识图谱关系Id");
        }

        if (null == this.getType()) {
            result.addError("资源类型为空", "请输入资源类型 1 文字段落 2 视频 3 题目 4 音频");
        }
        if (CollectionUtils.isEmpty(this.getNodeIds())) {
            result.addError("挂载知识节点集合为空", "请选择挂载的知识点");
        }
        if (StringUtil.isBlank(this.getSource())) {
            result.addError("教材资源链接源头为空", "请写入教材资源链接源头，1:ipublish 2:cms 3:UAi");
        }

        if (StringUtil.isBlank(this.getThirdResourceId())) {
            result.addError("教材资源三方的Id主键为空", "教材资源三方的Id主键");
        }

        if (Objects.equals(KnowledgeSourceTypeEnum.VIDEO.getCode(), this.getType())) {
            if (null == this.getStartTime()) {
                result.addError("视频开始时间为空", "请输入视频开始时间");
            }

            if (StringUtil.isBlank(this.getMultimediaKey())) {
                result.addError("视频标识为空", "请输入视频主标识");
            }
        }
        if (Objects.equals(KnowledgeSourceTypeEnum.QUESTION.getCode(), this.getType())) {
            if (StringUtil.isBlank(this.getMultimediaKey())) {
                result.addError("题目标识符不可为空", "请输入题目标识符不可为空");
            }
        }

        // 教材用途区域验证
        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getFirstErrorMessage());
        }
    }


    /**
     * 主资源上传到三方图谱的资源表信息
     *
     * @return
     */
    public KnowledgeResourceUpdateRequest toThirdKnowledgeResourceUpdateRequest() {
        KnowledgeResourceUpdateRequest request = new KnowledgeResourceUpdateRequest();
        request.setKnowledgeId(this.getKnowledgeId());
        request.setId(this.getThirdResourceId());
        request.setName(this.getResourceName());
        request.setType(this.getType() + "");
        request.setSource(this.getSource());
        request.setDir(this.getDir());
        request.setNodeIds(this.getNodeIds());
        request.setLabelIds(this.getLabelIds());
        request.setDescription(this.getDescription());
        return request;
    }

    /**
     * 目录点添加
     *
     * @param opUserId
     * @return
     */
    public List<CourseKnowledgeSourceInfoPO> toPo(Long opUserId) {

        List<CourseKnowledgeSourceInfoPO> poList = new ArrayList<>();

        CourseKnowledgeSourceInfoPO mainPO = new CourseKnowledgeSourceInfoPO();
        mainPO.setId(this.getId());
        mainPO.setCourseIdStr(this.getCourseIdStr());
        mainPO.setCourseId(this.getCourseId());
        mainPO.setUnitId(this.getUnitId());
        mainPO.setTaskId(this.getTaskId());
        mainPO.setType(this.getType());
        mainPO.setDir(this.getDir());
        mainPO.setStartPictureUrl(this.getStartPictureUrl());
        mainPO.setStartTime(this.getStartTime());
        mainPO.setEndTime(this.getEndTime());
        mainPO.setCourseKnowledgeId(this.getCourseKnowledgeId());
        mainPO.setMultimediaKey(this.getMultimediaKey());
        mainPO.setMultimediaIndex(this.getMultimediaIndex());
        mainPO.setMultimediaName(this.getMultimediaName());
        mainPO.setLocation(this.getLocation());
        mainPO.setGroupStatus(this.getGroupStatus());
        mainPO.setMainStatus(KnowledgeSourceMainEnum.MAIN.getCode());
        mainPO.setKnowledgeId(this.getKnowledgeId());
        mainPO.setGraphId(this.getGraphId());
        mainPO.setKnowledgeSourceId(this.getThirdResourceId());
        mainPO.setUpdateTime(new Date());
        mainPO.setUpdateBy(opUserId);
        poList.add(mainPO);

        if (CollectionUtils.isEmpty(this.getSubResourceIds())) {
            return poList;
        }

        //文本资源可能是组,子目录数据填充
        for (Long subId : this.getSubResourceIds()) {
            CourseKnowledgeSourceInfoPO subPO = new CourseKnowledgeSourceInfoPO();
            BeanUtils.copyProperties(mainPO, subPO);
            subPO.setId(subId);
            subPO.setParentId(mainPO.getId());
            subPO.setMainStatus(KnowledgeSourceMainEnum.SUB.getCode());
            poList.add(subPO);
        }
        return poList;
    }

}
