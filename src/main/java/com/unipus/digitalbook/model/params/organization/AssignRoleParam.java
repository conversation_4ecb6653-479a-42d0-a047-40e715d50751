package com.unipus.digitalbook.model.params.organization;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "给机构添加角色")
public class AssignRoleParam implements Params {
    @Schema(description = "角色ids")
    private List<Long> roleIds;
    @Schema(description = "机构id")
    private Long orgId;


    public List<Long> getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(List<Long> roleIds) {
        this.roleIds = roleIds;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    @Override
    public void valid() {
        if (roleIds == null || roleIds.isEmpty()) {
            throw new IllegalArgumentException("请选择角色");
        }
        if (orgId == null) {
            throw new IllegalArgumentException("请选择机构");
        }
    }
}
