package com.unipus.digitalbook.model.params.organization;

import com.unipus.digitalbook.model.entity.Organization;
import com.unipus.digitalbook.model.enums.OrganizationTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "编辑机构信息参数")
public class EditOrgParam implements Params {

    @Schema(description = "组织id", example = "123")
    Long id;

    @Schema(description = "组织名称", example = "一个组织名称")
    String name;

    @Schema(description = "组织类型", example = "1:集团、2:公司、3:部门、4:学校")
    Integer organizationType;

    @Schema(description = "上级机构id", example = "12345")
    Long parentId;

    public String getName() {
        return name;
    }

    public EditOrgParam setName(String name) {
        this.name = name;
        return this;
    }

    public Integer getOrganizationType() {
        return organizationType;
    }

    public EditOrgParam setOrganizationType(Integer organizationType) {
        this.organizationType = organizationType;
        return this;
    }

    public Long getParentId() {
        return parentId==null?0:parentId;
    }

    public EditOrgParam setParentId(Long parentId) {
        this.parentId = parentId;
        return this;
    }

    public EditOrgParam setId(Long id) {
        this.id = id;
        return this;
    }

    @Override
    public void valid() {
        if (getId() == null) {
            throw new IllegalArgumentException("机构id不能为空");
        }
        if (getName() == null || getName().isEmpty()) {
            throw new IllegalArgumentException("机构名称不能为空");
        }
        if (getName().length() > 50) {
            throw new IllegalArgumentException("机构名称不能超过50个字符");
        }
        if (getParentId()==null) {
            throw new IllegalArgumentException("上级机构id不能为空");
        }
        if (!OrganizationTypeEnum.validate(getOrganizationType())) {
            throw new IllegalArgumentException("机构类型不合法");
        }
    }

    public Long getId() {
        return id;
    }


    public Organization toEntity() {
        Organization organization = new Organization();
        organization.setId(this.getId());
        organization.setOrgName(this.getName());
        organization.setOrgType(this.getOrganizationType());
        organization.setParentId(this.getParentId());
        // 其他字段如status, createTime, updateTime, createBy, updateBy, enable, subOrgList, parentPath 在EditOrgParam中没有提供，因此不进行设置
        return organization;
    }

}
