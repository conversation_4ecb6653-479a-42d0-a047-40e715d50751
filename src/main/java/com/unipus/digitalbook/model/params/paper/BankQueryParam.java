package com.unipus.digitalbook.model.params.paper;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

/**
 * 题库信息查询参数
 */
@Schema(description = "题库信息查询参数")
public class BankQueryParam implements Params {

    @Schema(description = "试卷ID")
    private String paperId;

    @Override
    public void valid() {
        if(!StringUtils.hasText(paperId)){
            throw new IllegalArgumentException("试卷ID不能为空");
        }
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }
}
