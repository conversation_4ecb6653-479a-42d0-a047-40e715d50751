package com.unipus.digitalbook.model.params.paper;

import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.enums.PaperPreviewModeEnum;
import com.unipus.digitalbook.model.enums.UnitTestModeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

@Schema(description = "试卷实例初始化参数")
public class PaperInitParam implements Params {
    @Schema(description = "教材ID")
    private String bookId;
    @Schema(description = "教材版本(已发布的教材版本号)")
    private String bookVersionNumber;
    @Schema(description = "试卷ID (UUID)")
    private String paperId;
    @Schema(description = "诊断卷的测试模式 1:诊断模式/2:推荐模式。(非诊断卷可为空值)", nullable = true)
    private Integer testMode = 1;
    @Schema(description = "预览初始化模式: 清空历史作答数据:true/不清空历史作答数据:false", defaultValue = "false")
    private Boolean clearPreviewHistory = false;

    @Override
    public void valid() {
        if(!StringUtils.hasText(this.bookId)){
            throw new IllegalArgumentException("教材ID不能为空");
        }
        if(!StringUtils.hasText(this.bookVersionNumber)){
            throw new IllegalArgumentException("教材版本不能为空");
        }
        if(!StringUtils.hasText(this.paperId)){
            throw new IllegalArgumentException("试卷ID不能为空");
        }
        if(this.testMode!=null && UnitTestModeEnum.of(this.testMode)== null){
            throw new IllegalArgumentException("试卷测试模式不支持,诊断卷的测试模式 1:诊断模式/2:推荐模式。");
        }
    }

    public PaperInstance toEntity(String versionNumber, UserAccessInfo userAccessInfo, PaperPreviewModeEnum previewMode) {
        PaperInstance  paperInstance = new PaperInstance();
        paperInstance.setPaperId(this.paperId);
        paperInstance.setVersionNumber(versionNumber);
        paperInstance.setTestMode(UnitTestModeEnum.of(this.testMode));
        paperInstance.setOpenId(userAccessInfo.getOpenId());
        paperInstance.setTenantId(userAccessInfo.getTenantId());
        paperInstance.setPreviewMode(previewMode);
        paperInstance.setClearPreviewHistory(this.clearPreviewHistory);
        return paperInstance;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getBookVersionNumber() {
        return bookVersionNumber;
    }

    public void setBookVersionNumber(String bookVersionNumber) {
        this.bookVersionNumber = bookVersionNumber;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public Integer getTestMode() {
        return testMode;
    }

    public void setTestMode(Integer testMode) {
        this.testMode = testMode;
    }

    public Boolean getClearPreviewHistory() {
        return clearPreviewHistory;
    }

    public void setClearPreviewHistory(Boolean clearPreviewHistory) {
        this.clearPreviewHistory = clearPreviewHistory;
    }
}
