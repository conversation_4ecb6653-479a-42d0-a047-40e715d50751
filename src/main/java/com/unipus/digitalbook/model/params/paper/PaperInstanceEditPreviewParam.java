package com.unipus.digitalbook.model.params.paper;

import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.enums.PaperPreviewModeEnum;
import com.unipus.digitalbook.model.enums.UnitTestModeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

@Schema(description = "编辑态预览模式试卷参数")
public class PaperInstanceEditPreviewParam implements Params {
    @Schema(description = "试卷ID (UUID)")
    private String paperId;
    @Schema(description = "预览模式 1:教师模式/2:学生模式")
    private Integer previewMode;
    @Schema(description = "诊断卷的测试模式 1:诊断模式/2:推荐模式。(非诊断卷可为空值)", nullable = true)
    private Integer testMode = 1;
    @Schema(description = "预览初始化模式: 清空历史作答数据:true/不清空历史作答数据:false", defaultValue = "false")
    private Boolean clearPreviewHistory = false;

    @Override
    public void valid() {
        if (!StringUtils.hasText(this.paperId)) {
            throw new IllegalArgumentException("试卷ID不能为空");
        }
        if(this.previewMode==null || PaperPreviewModeEnum.of(this.previewMode)==null) {
            throw new IllegalArgumentException("预览模式不正确(1:教师模式/2:学生模式)");
        }
        if(this.testMode!=null && UnitTestModeEnum.of(this.testMode)==null) {
            throw new IllegalArgumentException("诊断卷的测试模式不正确(1:诊断模式/2:推荐模式)");
        }
    }

    public PaperInstance toEntity(UserAccessInfo userAccessInfo) {
        PaperInstance  paperInstance = new PaperInstance();
        paperInstance.setPaperId(this.paperId);
        paperInstance.setVersionNumber(IdentifierUtil.DEFAULT_VERSION_NUMBER);
        paperInstance.setPreviewMode(PaperPreviewModeEnum.of(this.previewMode));
        paperInstance.setTestMode(UnitTestModeEnum.of(this.testMode));
        paperInstance.setOpenId(userAccessInfo.getOpenId());
        paperInstance.setTenantId(userAccessInfo.getTenantId());
        paperInstance.setClearPreviewHistory(this.clearPreviewHistory);
        return paperInstance;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public Integer getPreviewMode() {
        return previewMode;
    }

    public void setPreviewMode(Integer previewMode) {
        this.previewMode = previewMode;
    }

    public Integer getTestMode() {
        return testMode;
    }

    public void setTestMode(Integer testMode) {
        this.testMode = testMode;
    }

    public Boolean getClearPreviewHistory() {
        return clearPreviewHistory;
    }

    public void setClearPreviewHistory(Boolean clearPreviewHistory) {
        this.clearPreviewHistory = clearPreviewHistory;
    }
}
