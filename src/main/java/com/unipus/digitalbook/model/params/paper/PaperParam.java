package com.unipus.digitalbook.model.params.paper;

import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import com.unipus.digitalbook.model.params.paper.question.PaperQuestionListParam;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

@Schema(description = "试卷参数")
public class PaperParam implements Params {
    @Schema(description = "教材ID (UUID)")
    private String bookId;
    @Schema(description = "试卷ID (UUID)")
    private String paperId;
    @Schema(description = "试卷类型: 1:常规卷/2:挑战卷/3:诊断卷")
    private Integer paperType;
    @Schema(description = "试卷名称")
    private String paperName;
    @Schema(description = "试卷说明")
    private String description;
    @Schema(description = "试卷内容")
    private String content;

    @Schema(description = "是否处理题目: true:处理 其他:不处理")
    private Boolean processQuestions;
    @Schema(description = "试题列表(如果删除试卷所有题目，需要传入空列表，即该字段需要赋值为空列表)")
    private PaperQuestionListParam questionListParam;

    @Override
    public void valid() {
        if (!StringUtils.hasText(this.bookId)) {
            throw new IllegalArgumentException("教材ID不能为空");
        }
        if (!StringUtils.hasText(this.paperId)) {
            throw new IllegalArgumentException("试卷ID不能为空");
        }
        if (PaperTypeEnum.getByCode(this.paperType)== null) {
            throw new IllegalArgumentException("试卷类型不能正确");
        }
        if (!StringUtils.hasText(this.paperName)) {
            throw new IllegalArgumentException("试卷名称不能为空");
        }
        if(this.processQuestions!=null && this.processQuestions) {
            if (this.questionListParam == null) {
                throw new IllegalArgumentException("试题列表未赋值");
            }
            this.questionListParam.valid();
        }
    }

    public Paper toEntity() {
        Paper paper = new Paper();
        paper.setBookId(this.bookId);
        paper.setPaperId(this.paperId);
        paper.setPaperType(PaperTypeEnum.getByCode(this.paperType));
        paper.setPaperName(this.paperName);
        paper.setDescription(this.description);
        paper.setContent(this.content);
        paper.setVersionNumber(IdentifierUtil.DEFAULT_VERSION_NUMBER);
        paper.setEnable(true);
        return paper;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public Integer getPaperType() {
        return paperType;
    }

    public void setPaperType(Integer paperType) {
        this.paperType = paperType;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Boolean getProcessQuestions() {
        return processQuestions;
    }

    public void setProcessQuestions(Boolean processQuestions) {
        this.processQuestions = processQuestions;
    }

    public PaperQuestionListParam getQuestionListParam() {
        return questionListParam;
    }

    public void setQuestionListParam(PaperQuestionListParam questionListParam) {
        this.questionListParam = questionListParam;
    }
}

