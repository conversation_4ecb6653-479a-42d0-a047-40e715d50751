package com.unipus.digitalbook.model.params.paper;

import com.unipus.digitalbook.model.entity.paper.PaperReference;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

@Schema(description = "试卷引用参数")
public class PaperReferenceParam implements Params {
    @Schema(description = "章节ID")
    private String bookId;
    @Schema(description = "章节ID")
    private String chapterId;
    @Schema(description = "试卷在章节里面的插入位置")
    private String position;
    @Schema(description = "试卷ID (UUID)")
    private String paperId;
    @Override
    public void valid() {
        if (!StringUtils.hasText(this.bookId)) {
            throw new IllegalArgumentException("教材ID不能为空");
        }
        if (!StringUtils.hasText(this.chapterId) ) {
            throw new IllegalArgumentException("章节ID不能为空");
        }
        if (!StringUtils.hasText(this.position)) {
            throw new IllegalArgumentException("试卷在章节里面的插入位置不能为空");
        }
        if (!StringUtils.hasText(this.paperId)) {
            throw new IllegalArgumentException("试卷ID不能为空");
        }
    }

    public PaperReference toEntity() {
        PaperReference paperReference = new PaperReference();
        paperReference.setBookId(this.bookId);
        paperReference.setChapterId(this.chapterId);
        paperReference.setPosition(this.position);
        paperReference.setPaperId(this.paperId);
        return paperReference;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

}
