package com.unipus.digitalbook.model.params.paper;

import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.entity.paper.QueryPaperAnswer;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

@Schema(description = "查询试卷作答记录参数")
public class QueryPaperAnswerParam implements Params {

    @Schema(description = "试卷实例ID")
    private String instanceId;
    @Schema(description = "试卷类型：1:常规卷/2:挑战卷/3:诊断卷")
    private Integer paperType;

    @Override
    public void valid() {
        if (!StringUtils.hasText(this.instanceId)) {
            throw new IllegalArgumentException("试卷实例ID不能为空");
        }
        if (PaperTypeEnum.getByCode(this.paperType) == null) {
            throw new IllegalArgumentException("无效的试卷类型");
        }
    }

    public QueryPaperAnswer toEntity(UserAccessInfo userAccessInfo){
        QueryPaperAnswer queryPaperAnswer = new QueryPaperAnswer();
        queryPaperAnswer.setInstanceId(this.instanceId);
        queryPaperAnswer.setPaperType(PaperTypeEnum.getByCode(this.paperType));
        queryPaperAnswer.setOpenId(userAccessInfo.getOpenId());
        queryPaperAnswer.setTenantId(userAccessInfo.getTenantId());
        return queryPaperAnswer;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public Integer getPaperType() {
        return paperType;
    }

    public void setPaperType(Integer paperType) {
        this.paperType = paperType;
    }
}
