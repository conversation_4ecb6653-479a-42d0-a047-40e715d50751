package com.unipus.digitalbook.model.params.paper;

import com.unipus.digitalbook.model.entity.paper.QuestionTag;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

/**
 * 题目标签参数
 */
@Schema(description = "标签参数")
public class QuestionTagParam implements Params {

    @Schema(description = "原始资源ID")
    private String baseResourceId;
    @Schema(description = "关联资源ID")
    private String targetResourceId;
//    @Schema(description = "标签列表")
//    private List<TagParam> tagParams;

    @Override
    public void valid() {
        if(!StringUtils.hasText(baseResourceId)){
            throw new IllegalArgumentException("原始资源ID不能为空");
        }
//        if(CollectionUtils.isNotEmpty(tagParams)) {
//            tagParams.forEach(TagParam::valid);
//        }
    }

    public QuestionTag toEntity() {
        QuestionTag questionTag = new QuestionTag();
        questionTag.setBaseResourceId(baseResourceId);
        questionTag.setTargetResourceId(targetResourceId);
//        if(CollectionUtils.isNotEmpty(tagParams)) {
//            questionTag.setTags(tagParams.stream().map(TagParam::toEntity).toList());
//        }
        return questionTag;
    }

    public String getBaseResourceId() {
        return baseResourceId;
    }

    public void setBaseResourceId(String baseResourceId) {
        this.baseResourceId = baseResourceId;
    }

    public String getTargetResourceId() {
        return targetResourceId;
    }

    public void setTargetResourceId(String targetResourceId) {
        this.targetResourceId = targetResourceId;
    }

//    public List<TagParam> getTagParams() {
//        return tagParams;
//    }
//
//    public void setTagParams(List<TagParam> tagParams) {
//        this.tagParams = tagParams;
//    }
}
