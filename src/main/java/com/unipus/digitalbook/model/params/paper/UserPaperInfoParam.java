package com.unipus.digitalbook.model.params.paper;

import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.entity.paper.UserPaperInfo;
import com.unipus.digitalbook.model.enums.UnitTestModeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.util.StringUtils;

@Schema(description = "用户试卷成绩提交参数")
public class UserPaperInfoParam implements Params {

    @Schema(description = "试卷ID (UUID)")
    private String paperId;
    @Schema(description = "试卷版本号")
    private String versionNumber;
    @Schema(description = "诊断卷测试模式, 1:诊断模式，2:推荐模式。(诊断卷以外无视)")
    private Integer testMode;

    @Override
    public void valid() {
        if (!StringUtils.hasText(this.paperId)) {
            throw new IllegalArgumentException("试卷ID不能为空");
        }
        if (!StringUtils.hasText(this.versionNumber)) {
            throw new IllegalArgumentException("试卷版本号不能为空");
        }
    }

    public UserPaperInfo toEntity(UserAccessInfo userAccessInfo){
        UserPaperInfo userPaperInfo = new UserPaperInfo();
        userPaperInfo.setPaperId(this.paperId);
        userPaperInfo.setVersionNumber(this.versionNumber);
        userPaperInfo.setOpenId(userAccessInfo.getOpenId());
        userPaperInfo.setTenantId(userAccessInfo.getTenantId());
        userPaperInfo.setTestMode(UnitTestModeEnum.of(this.testMode));
        return userPaperInfo;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Integer getTestMode() {
        return testMode;
    }

    public void setTestMode(Integer testMode) {
        this.testMode = testMode;
    }
}
