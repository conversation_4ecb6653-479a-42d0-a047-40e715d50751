package com.unipus.digitalbook.model.params.paper.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.ChoiceQuestion;
import com.unipus.digitalbook.model.params.question.QuestionOptionParam;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 选择题参数
 */
public class PaperChoiceQuestionParam extends PaperQuestionBaseParam {

    @Override
    public void valid() {
        List<QuestionOptionParam> options = getOptions();
        if (StringUtils.hasText(getChildId()) && CollectionUtils.isEmpty(options)) {
            throw new IllegalArgumentException("选项不能为空");
        }
    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        ChoiceQuestion choiceQuestion = new ChoiceQuestion();
        choiceQuestion.setQuestionText(new QuestionText(getQuesText(), getQuesTextString()));
        return choiceQuestion;
    }
}
