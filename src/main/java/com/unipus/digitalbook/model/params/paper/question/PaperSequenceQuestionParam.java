package com.unipus.digitalbook.model.params.paper.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.SequenceQuestion;

/**
 * 排序题参数
 */
public class PaperSequenceQuestionParam extends PaperQuestionBaseParam {


    @Override
    public void valid() {

    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        SequenceQuestion sequenceQuestion = new SequenceQuestion();
        QuestionText currentQuestionText = new QuestionText(getQuesText(), getQuesTextString());
        sequenceQuestion.setQuestionText(currentQuestionText);
        return sequenceQuestion;
    }
}
