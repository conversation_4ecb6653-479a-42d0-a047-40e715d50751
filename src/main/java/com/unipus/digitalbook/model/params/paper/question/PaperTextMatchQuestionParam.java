package com.unipus.digitalbook.model.params.paper.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.TextMatchQuestion;

/**
 * 文本匹配题参数
 */
public class PaperTextMatchQuestionParam extends PaperQuestionBaseParam {


    @Override
    public void valid() {

    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        TextMatchQuestion textMatchQuestion = new TextMatchQuestion();
        textMatchQuestion.setQuestionText(new QuestionText(getQuesText(), getQuesTextString()));
        return textMatchQuestion;
    }
}
