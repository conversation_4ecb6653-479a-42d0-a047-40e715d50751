package com.unipus.digitalbook.model.params.process;

import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "提交进度参数")
public class SubmitProgressParam implements Params {
    @Schema(description = "节点id")
    private String nodeId;
    @Schema(description = "章节id")
    private String chapterId;
    @Schema(description = "章节版本")
    private String chapterVersionNumber;

    @Override
    public void valid() {

    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getChapterVersionNumber() {
        return chapterVersionNumber;
    }

    public void setChapterVersionNumber(String chapterVersionNumber) {
        this.chapterVersionNumber = chapterVersionNumber;
    }
}
