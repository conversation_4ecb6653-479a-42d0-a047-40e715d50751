package com.unipus.digitalbook.model.params.publish;

import com.unipus.digitalbook.model.entity.book.BookBasic;
import com.unipus.digitalbook.model.entity.book.BookCopyright;
import com.unipus.digitalbook.model.entity.book.BookIntro;
import com.unipus.digitalbook.model.entity.book.BookNode;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import com.unipus.digitalbook.model.entity.complement.ComplementResource;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 教材发布参数
 */
@Data
@Schema(description = "教材发布信息参数")
public class PublishResourceParam implements Params {

    @Schema(description = "资源类型编码: 1：教材基本信息/2：教材简介/3：版权信息/4：配套资源/5：教材章节/6：试卷")
    private Integer typeCode;

    @Schema(description = "资源ID(typeCode==4时可空)")
    private String resourceId;

    @Schema(description = "资源版本id")
    private Long versionId;

    public BookNode toEntity() {
        if (typeCode == 6) {
            Paper paper = new Paper();
            paper.setId(versionId);
            paper.setPaperId(resourceId);
            return paper;
        }
        if (typeCode == 5) {
            Chapter chapter = new Chapter();
            chapter.setId(resourceId);
            ChapterVersion chapterVersion = ChapterVersion.Builder.getInstance().chapterId(resourceId).id(versionId).build();
            chapter.setChapterVersion(chapterVersion);
            return chapter;
        }
        if (typeCode == 4) {
            ComplementResource resource = new ComplementResource();
            resource.setId(versionId);
            resource.setResourceType(typeCode);
            return resource;
        }
        if (typeCode == 3) {
            BookCopyright copyright=  new BookCopyright();
            copyright.setId(versionId);
            return  copyright;
        }
        if (typeCode == 2) {
            BookIntro intro = new BookIntro();
            intro.setId(versionId);
            return intro;
        }
        if (typeCode == 1) {
            BookBasic basic = new BookBasic();
            basic.setId(versionId);
            return basic ;
        }
        return null;
    }

    @Override
    public void valid() {
        if (typeCode == null) {
            throw new IllegalArgumentException("typeCode不能为空");
        }
        if (typeCode != 4 && resourceId == null) {
            throw new IllegalArgumentException("所选节点类型非配套资源，resourceId不能为空");
        }
        if (versionId == null) {
            throw new IllegalArgumentException("chapterVersionId不能为空");
        }
    }
}
