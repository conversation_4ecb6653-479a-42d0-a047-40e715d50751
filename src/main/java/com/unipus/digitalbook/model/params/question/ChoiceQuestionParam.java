package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.ChoiceQuestion;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 选择题参数
 */
public class ChoiceQuestionParam extends QuestionBaseParam {

    @Override
    public void valid() {
        List<QuestionOptionParam> options = getOptions();
        if (StringUtils.hasText(getChildId()) && CollectionUtils.isEmpty(options)) {
            throw new IllegalArgumentException("选项不能为空");
        }
    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        ChoiceQuestion choiceQuestion = new ChoiceQuestion();
        choiceQuestion.setQuestionText(new QuestionText(getQuesText(), getQuesTextString()));
        return choiceQuestion;
    }
}
