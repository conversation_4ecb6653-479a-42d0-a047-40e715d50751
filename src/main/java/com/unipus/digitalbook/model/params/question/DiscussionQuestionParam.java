package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.DiscussionQuestion;

/**
 * 讨论题参数
 */
public class DiscussionQuestionParam extends QuestionBaseParam {


    @Override
    public void valid() {

    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        return new DiscussionQuestion();
    }
}
