package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.MultiMediaUploadQuestion;

/**
 * 多媒体上传题
 */
public class MultiMediaUploadQuestionParam extends QuestionBaseParam {

    @Override
    public void valid() {

    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        MultiMediaUploadQuestion multiMediaUploadQuestion = new MultiMediaUploadQuestion();
        multiMediaUploadQuestion.setQuestionText(new QuestionText(getQuesText(), getQuesTextString()));
        return multiMediaUploadQuestion;
    }
}
