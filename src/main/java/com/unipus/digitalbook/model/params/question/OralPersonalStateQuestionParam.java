package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.OralPersonalStateQuestion;

/**
 * 个人陈述题参数
 */
public class OralPersonalStateQuestionParam extends QuestionBaseParam {

    @Override
    public void valid() {

    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        OralPersonalStateQuestion question = new OralPersonalStateQuestion();
        QuestionText currentQuestionText = new QuestionText(getQuesText(), getQuesTextString());
        currentQuestionText.setPrepareTime(getPrepareTime());
        currentQuestionText.setAnswerTime(getAnswerTime());
        question.setQuestionText(currentQuestionText);
        return question;
    }
}
