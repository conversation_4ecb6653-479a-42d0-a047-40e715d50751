package com.unipus.digitalbook.model.params.question;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.unipus.digitalbook.model.entity.question.*;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.IntStream;

/**
 * 小题通用参数
 */
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        property = "questionType",
        visible = true
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = ChoiceQuestionParam.class, name = "single_choice"),
        @JsonSubTypes.Type(value = ChoiceQuestionParam.class, name = "multi_choice"),
        @JsonSubTypes.Type(value = ShortAnswerQuestionParam.class, name = "short_answer"),
        @JsonSubTypes.Type(value = MultiMediaUploadQuestionParam.class, name = "multi_media_upload"),
        @JsonSubTypes.Type(value = TranslationQuestionParam.class, name = "translation"),
        @JsonSubTypes.Type(value = WritingQuestionParam.class, name = "writing"),
        @JsonSubTypes.Type(value = OralPersonalStateQuestionParam.class, name = "oral_personal_state"),
        @JsonSubTypes.Type(value = VocabularyQuestionParam.class, name = "vocabulary"),
        @JsonSubTypes.Type(value = FillBlankQuestionParam.class, name = "fill_blanks"),
        @JsonSubTypes.Type(value = FillBlankQuestionParam.class, name = "fill_blanks_choice"),
        @JsonSubTypes.Type(value = FillBlankQuestionParam.class, name = "fill_blanks_dropdown"),
//        @JsonSubTypes.Type(value = DiscussionQuestionParam.class, name = "discussion"),
        @JsonSubTypes.Type(value = TextReadingQuestionParam.class, name = "oral_sentence_scoop"),
        @JsonSubTypes.Type(value = SequenceQuestionParam.class, name = "sequence"),
//        @JsonSubTypes.Type(value = RichTextReadQuestionParam.class, name = "rich_text_read"),
        @JsonSubTypes.Type(value = RolePlayQuestionParam.class, name = "role_play_scoop"),
        @JsonSubTypes.Type(value = TextMatchQuestionParam.class, name = "text_match"),
        @JsonSubTypes.Type(value = EvaluationQuestionParam.class, name = "evaluation"),
        @JsonSubTypes.Type(value = TrueFalseQuestionParam.class, name = "true_false"),
})
public abstract class QuestionBaseParam implements Params {
    @Schema(description = "题目ID")
    private String id;
    @Schema(description = "子题目ID")
    private String childId;
    @Schema(description = "题型")
    private String questionType;
    @Schema(description = "小题解析")
    private String analysis;
    @Schema(description = "题干")
    private String quesText;
    @Schema(description = "文本题干")
    private String quesTextString;
    @Schema(description = "是否计分")
    private Boolean isScoring;
    @Schema(description = "是否自动判题")
    private Boolean isJudgment;
    @Schema(description = "题目")
    private BigDecimal score;
    @Schema(description = "选项列表")
    private List<QuestionOptionParam> options;

    @Schema(description = "答案列表")
    private List<QuestionAnswerParam> answers;

    @Schema(description = "子题列表")
    private List<QuestionBaseParam> children;

    @Schema(description = "关键字")
    private List<KeywordParam> keywords;

    @Schema(description = "难度")
    private Integer difficulty;

    @Schema(description = "媒体")
    private String media;

    @Schema(description = "角色")
    private String role;

    @Schema(description = "音标")
    private String phoneticSymbol;

    @Schema(description = "答案字数限制")
    private Integer answerWordLimit;

    @Schema(description = " ")
    private Integer prepareTime;

    @Schema(description = "作答间隔时长")
    private Integer answerTime;

    @Schema(description = "关联参数")
    private List<QuestionRelevancyParam> relevancyList;

    @Schema(description = "标签列表")
    private List<List<QuestionTagParam>> tagList;

    protected abstract Question toQuestion(QuestionText questionText);

    /**
     * 转换为题目实体
     *
     * @param editQuestionUserId 编辑当前题目的用户id，可以是空
     * @return
     */
    private Question toQuestion(@Nullable Long editQuestionUserId, QuestionText questionText) {
        Question question = toQuestion(questionText);
        question.setBizQuestionId(childId);
        question.setAnalysis(analysis);
        question.setQuestionType(QuestionTypeEnum.getCodeByName(questionType));
        question.setIsScoring(isScoring);
        question.setIsJudgment(isJudgment);
        question.setScore(this.getScore());
        question.setCreateBy(editQuestionUserId);
        question.setUpdateBy(editQuestionUserId);
        question.setEnable(true);
        Optional.ofNullable(getAnswers()).ifPresent(answerParams -> {
            List<QuestionAnswer> questionAnswers = new ArrayList<>();
            for (int i = 0; i < answerParams.size(); i++) {
                QuestionAnswerParam answerParam = answerParams.get(i);
                QuestionAnswer entity = answerParam.toEntity(editQuestionUserId);
                entity.setSortOrder(i);
                questionAnswers.add(entity);
            }
            question.setAnswers(questionAnswers);
        });
        Optional.ofNullable(getOptions()).ifPresent(optionParams -> {
            List<ChoiceQuestionOption> questionOptions = new ArrayList<>();
            for (int i = 0; i < optionParams.size(); i++) {
                QuestionOptionParam questionOptionParam = optionParams.get(i);
                ChoiceQuestionOption entity = questionOptionParam.toEntity(editQuestionUserId);
                entity.setSortOrder(i);
                questionOptions.add(entity);
            }
            question.setOptions(questionOptions);
        });
        return question;
    }

    /**
     * 转换为题目实体
     *
     * @param editQuestionUserId 编辑当前题目的用户id，可以是空
     * @return
     */
    public Question toEntity(@Nullable Long editQuestionUserId) {
        SmallQuestion smallQuestion = new SmallQuestion();
        if (StringUtils.hasText(id)) {
            smallQuestion.setBizQuestionId(id);
        } else {
            smallQuestion.setBizQuestionId(childId);
        }
        QuestionText questionText = new QuestionText(quesText, quesTextString);
        if (!CollectionUtils.isEmpty(keywords)) {
            questionText.setKeywords(keywords.stream().map(KeywordParam::toEntity).toList());
        }
        if (!CollectionUtils.isEmpty(options)) {
            questionText.setOptions(options.stream().map(q -> q.toEntity(editQuestionUserId)).toList());
        }
        if (answerWordLimit != null) {
            questionText.setAnswerWordLimit(answerWordLimit);
        }
        if (!CollectionUtils.isEmpty(relevancyList)) {
            questionText.setRelevancy(relevancyList.stream().map(QuestionRelevancyParam::toEntity).toList());
        }
        smallQuestion.setQuestionText(questionText);
        smallQuestion.setAnalysis(analysis);
        smallQuestion.setDifficulty(0);
        smallQuestion.setEnable(true);
        smallQuestion.setDifficulty(difficulty);
        smallQuestion.setCreateBy(editQuestionUserId);
        smallQuestion.setUpdateBy(editQuestionUserId);
        smallQuestion.setQuestionType(QuestionTypeEnum.getCodeByName(questionType));
        if (!CollectionUtils.isEmpty(tagList)) {
            smallQuestion.setTags(QuestionTagParam.toEntityList(tagList));
        }
        smallQuestion.setQuestions(
            IntStream.range(0, children.size()).mapToObj(i -> {
                QuestionBaseParam questionParam = children.get(i);
                if (CollectionUtils.isEmpty(questionParam.getChildren())) {
                    Question entity = questionParam.toQuestion(editQuestionUserId, questionText);
                    entity.setSortOrder(i);
                    return entity;
                }
                return questionParam.toEntity(editQuestionUserId);
            }).toList()
        );
        if (QuestionTypeEnum.ROLE_PLAY_SCOOP.getCode().equals(smallQuestion.getQuestionType())) {
            // 角色扮演的小题分直接使用前端算好的分数，其他小题的分不对，自己从子题中获取
            smallQuestion.setScore(getScore());
        } else {
            smallQuestion.setScore(smallQuestion.getQuestions().stream()
                    .filter(IQuestion.class::isInstance).map(Question::getScore)
                    .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        return smallQuestion;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    public String getAnalysis() {
        return analysis;
    }

    public void setAnalysis(String analysis) {
        this.analysis = analysis;
    }

    public String getQuesText() {
        return quesText;
    }

    public void setQuesText(String quesText) {
        this.quesText = quesText;
    }

    public Boolean getIsScoring() {
        return isScoring;
    }

    public void setIsScoring(Boolean scoring) {
        isScoring = scoring;
    }

    public Boolean getIsJudgment() {
        return isJudgment;
    }

    public void setIsJudgment(Boolean judgment) {
        isJudgment = judgment;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public List<QuestionOptionParam> getOptions() {
        return options;
    }

    public void setOptions(List<QuestionOptionParam> options) {
        this.options = options;
    }

    public List<QuestionAnswerParam> getAnswers() {
        return answers;
    }

    public void setAnswers(List<QuestionAnswerParam> answers) {
        this.answers = answers;
    }

    public String getChildId() {
        return childId;
    }

    public void setChildId(String childId) {
        this.childId = childId;
    }

    public String getQuesTextString() {
        return quesTextString;
    }

    public void setQuesTextString(String quesTextString) {
        this.quesTextString = quesTextString;
    }

    public List<QuestionBaseParam> getChildren() {
        return children;
    }

    public void setChildren(List<QuestionBaseParam> children) {
        this.children = children;
    }

    public List<KeywordParam> getKeywords() {
        return keywords;
    }

    public void setKeywords(List<KeywordParam> keywords) {
        this.keywords = keywords;
    }

    public Integer getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(Integer difficulty) {
        this.difficulty = difficulty;
    }

    public String getMedia() {
        return media;
    }

    public void setMedia(String media) {
        this.media = media;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public List<QuestionRelevancyParam> getRelevancyList() {
        return relevancyList;
    }

    public void setRelevancyList(List<QuestionRelevancyParam> relevancyList) {
        this.relevancyList = relevancyList;
    }


    public Integer getAnswerWordLimit() {
        return answerWordLimit;
    }

    public void setAnswerWordLimit(Integer answerWordLimit) {
        this.answerWordLimit = answerWordLimit;
    }

    public String getPhoneticSymbol() {
        return phoneticSymbol;
    }

    public void setPhoneticSymbol(String phoneticSymbol) {
        this.phoneticSymbol = phoneticSymbol;
    }

    public Integer getPrepareTime() {
        return prepareTime;
    }

    public void setPrepareTime(Integer prepareTime) {
        this.prepareTime = prepareTime;
    }

    public Integer getAnswerTime() {
        return answerTime;
    }

    public void setAnswerTime(Integer answerTime) {
        this.answerTime = answerTime;
    }

    public List<List<QuestionTagParam>> getTagList() {
        return tagList;
    }

    public void setTagList(List<List<QuestionTagParam>> tagList) {
        this.tagList = tagList;
    }
}
