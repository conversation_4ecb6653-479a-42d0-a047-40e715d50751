package com.unipus.digitalbook.model.params.question;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.unipus.digitalbook.model.entity.question.QuestionSetting;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.IOException;

public class QuestionSettingParam implements Params {
    @Schema(description = "答题方式，如拖拽、点选")
    private String answerType;
    @Schema(description = "pc端布局类型 瀑布|分页|左右")
    private String pcLayoutType;
    @Schema(description = "app端布局类型 瀑布|分页")
    private String appLayoutType;
    @Schema(description = "音频设置信息")
    private AudioSetting audioSetting;
    @Schema(description = "视频设置信息")
    private VideoSetting videoSetting;
    @Schema(description = "答题角色 'all' | 'role' | null")
    private String answerRole;
    @Schema(description = "听原音 可选 'before' | 'after'| null")
    private String answerTiming;

    @Schema(description = "作答量级")
    private String answerLevel;


    public QuestionSetting toEntity(Long editQuestionUserId) {
        QuestionSetting questionSetting = new QuestionSetting();
        questionSetting.setAnswerType(this.answerType);
        questionSetting.setPcLayoutType(this.pcLayoutType);
        questionSetting.setAppLayoutType(this.appLayoutType);
        questionSetting.setAnswerRole(this.answerRole);
        questionSetting.setAnswerTiming(this.answerTiming);
        questionSetting.setAnswerLevel(this.answerLevel);
        if (this.audioSetting != null) {
            questionSetting.setAudioSetting(this.audioSetting.toEntity());
        }
        if (this.videoSetting != null) {
            questionSetting.setVideoSetting(this.videoSetting.toEntity());
        }
        questionSetting.setCreateBy(editQuestionUserId);
        questionSetting.setUpdateBy(editQuestionUserId);
        return questionSetting;
    }
    public String getAnswerType() {
        return answerType;
    }

    public void setAnswerType(String answerType) {
        this.answerType = answerType;
    }

    public String getPcLayoutType() {
        return pcLayoutType;
    }

    public void setPcLayoutType(String pcLayoutType) {
        this.pcLayoutType = pcLayoutType;
    }

    public String getAppLayoutType() {
        return appLayoutType;
    }

    public void setAppLayoutType(String appLayoutType) {
        this.appLayoutType = appLayoutType;
    }

    public AudioSetting getAudioSetting() {
        return audioSetting;
    }

    public void setAudioSetting(AudioSetting audioSetting) {
        this.audioSetting = audioSetting;
    }

    public VideoSetting getVideoSetting() {
        return videoSetting;
    }

    public void setVideoSetting(VideoSetting videoSetting) {
        this.videoSetting = videoSetting;
    }

    public String getAnswerRole() {
        return answerRole;
    }

    public void setAnswerRole(String answerRole) {
        this.answerRole = answerRole;
    }

    public String getAnswerTiming() {
        return answerTiming;
    }

    public void setAnswerTiming(String answerTiming) {
        this.answerTiming = answerTiming;
    }

    public String getAnswerLevel() {
        return answerLevel;
    }

    public void setAnswerLevel(String answerLevel) {
        this.answerLevel = answerLevel;
    }

    /**
     *
     */
    @Override
    public void valid() {
        //todo 补充验证逻辑
    }

    public static class AudioSetting {
        @Schema(description = "播放方式 点击播放 自动播放")
        private String playType;
        @Schema(description = "作答前显示，作答后显示")
        private String subtitle;
        @Schema(description = "是否设置播放次数")
        @JsonDeserialize(using = LimitFlagDeserializer.class)
        private boolean setPlayNum;
        @Schema(description = "设置播放次数是true的时候，填写播放多少次")
        private Integer playNum;

        public QuestionSetting.AudioSetting toEntity() {
            QuestionSetting.AudioSetting audioSetting = new QuestionSetting.AudioSetting();
            audioSetting.setPlayType(this.getPlayType());
            audioSetting.setSubtitle(this.getSubtitle());
            audioSetting.setPlayNum(this.getPlayNum());
            audioSetting.setSetPlayNum(this.isSetPlayNum());
            return audioSetting;
        }
        public String getPlayType() {
            return playType;
        }

        public void setPlayType(String playType) {
            this.playType = playType;
        }

        public String getSubtitle() {
            return subtitle;
        }

        public void setSubtitle(String subtitle) {
            this.subtitle = subtitle;
        }

        public boolean isSetPlayNum() {
            return setPlayNum;
        }

        public void setSetPlayNum(boolean setPlayNum) {
            this.setPlayNum = setPlayNum;
        }

        public Integer getPlayNum() {
            return playNum;
        }

        public void setPlayNum(Integer playNum) {
            this.playNum = playNum;
        }
    }

    public static class VideoSetting {
        @Schema(description = "播放方式 点击播放 自动播放")
        private String playType;
        @Schema(description = "作答前显示，作答后显示")
        private String subtitle;
        @Schema(description = "是否设置视频播放次数")
        @JsonDeserialize(using = LimitFlagDeserializer.class)
        private boolean setPlayNum;
        @Schema(description = "视频设置播放次数的值")
        private Integer playNum;

        public QuestionSetting.VideoSetting toEntity() {
            QuestionSetting.VideoSetting videoSetting = new QuestionSetting.VideoSetting();
            videoSetting.setPlayType(this.getPlayType());
            videoSetting.setSubtitle(this.getSubtitle());
            videoSetting.setPlayNum(this.getPlayNum());
            videoSetting.setSetPlayNum(this.isSetPlayNum());
            return videoSetting;
        }
        public String getPlayType() {
            return playType;
        }

        public void setPlayType(String playType) {
            this.playType = playType;
        }

        public String getSubtitle() {
            return subtitle;
        }

        public void setSubtitle(String subtitle) {
            this.subtitle = subtitle;
        }

        public boolean isSetPlayNum() {
            return setPlayNum;
        }

        public void setSetPlayNum(boolean setPlayNum) {
            this.setPlayNum = setPlayNum;
        }

        public Integer getPlayNum() {
            return playNum;
        }

        public void setPlayNum(Integer playNum) {
            this.playNum = playNum;
        }
    }

    public static class LimitFlagDeserializer extends JsonDeserializer<Boolean> {
        public LimitFlagDeserializer() {
        }
        @Override
        public Boolean deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String value = p.getText();
            if ("limit".equalsIgnoreCase(value)) {
                return true;
            } else if ("noLimit".equalsIgnoreCase(value)) {
                return false;
            }
            return Boolean.parseBoolean(value);
        }
    }
}
