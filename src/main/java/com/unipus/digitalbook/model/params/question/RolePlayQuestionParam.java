package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.RolePlayQuestion;
import org.springframework.util.CollectionUtils;

/**
 * 角色扮演题参数
 */
public class RolePlayQuestionParam extends QuestionBaseParam {

    @Override
    public void valid() {

    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        RolePlayQuestion rolePlayQuestion = new RolePlayQuestion();
        QuestionText currentQuestionText = new QuestionText(getQuesText(), getQuesTextString());
        currentQuestionText.setMedia(getMedia());
        currentQuestionText.setRole(getRole());
        if (!CollectionUtils.isEmpty(getRelevancyList())) {
            currentQuestionText.setRelevancy(getRelevancyList().stream().map(QuestionRelevancyParam::toEntity).toList());
        }
        currentQuestionText.setPrepareTime(getPrepareTime());
        currentQuestionText.setAnswerTime(getAnswerTime());
        rolePlayQuestion.setQuestionText(currentQuestionText);
        return rolePlayQuestion;
    }
}
