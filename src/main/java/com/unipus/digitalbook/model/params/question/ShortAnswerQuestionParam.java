package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.ShortAnswerQuestion;

public class ShortAnswerQuestionParam extends QuestionBaseParam{

    @Override
    public void valid() {

    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        ShortAnswerQuestion shortAnswerQuestion = new ShortAnswerQuestion();
        QuestionText currentQuestionText = new QuestionText();
        currentQuestionText.setAnswerWordLimit(getAnswerWordLimit());
        shortAnswerQuestion.setQuestionText(currentQuestionText);
        return shortAnswerQuestion;
    }
}
