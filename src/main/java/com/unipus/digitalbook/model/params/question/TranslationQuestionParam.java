package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.TranslationQuestion;

/**
 * 写作题题
 */
public class TranslationQuestionParam extends QuestionBaseParam {

    @Override
    public void valid() {

    }

    @Override
    protected Question toQuestion(QuestionText questionText) {
        QuestionText currentQuestionText = new QuestionText(questionText.getText(), questionText.getPlainText());
        currentQuestionText.setAnswerWordLimit(getAnswerWordLimit());
        TranslationQuestion question = new TranslationQuestion();
        question.setQuestionText(currentQuestionText);
        return question;
    }
}
