package com.unipus.digitalbook.model.params.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.type.WritingQuestion;

/**
 * 写作题题
 */
public class WritingQuestionParam extends QuestionBaseParam {

    @Override
    public void valid() {

    }


    @Override
    protected Question toQuestion(QuestionText questionText) {
        QuestionText currentQuestionText = new QuestionText(questionText.getText(), questionText.getPlainText());
        currentQuestionText.setAnswerWordLimit(getAnswerWordLimit());
        WritingQuestion question = new WritingQuestion();
        question.setQuestionText(currentQuestionText);
        return question;
    }
}
