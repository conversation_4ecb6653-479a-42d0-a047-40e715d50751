package com.unipus.digitalbook.model.params.template;

import com.unipus.digitalbook.model.entity.template.PaperScoreTemplate;
import com.unipus.digitalbook.model.enums.PaperScoreTemplateTypeEnum;
import com.unipus.digitalbook.model.params.Params;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Data
@Schema(description = "编辑模板参数")
public class EditPaperScoreTemplateParam implements Params {

    @Schema(description = "模板Id")
    private Long id;

    @Schema(description = "模板名称")
    private String name;

    /**
     * {@link PaperScoreTemplateTypeEnum}
     */
    @Schema(description = "模板类型，1:挑战评价,2:诊断评价")
    private Integer type;

    @Schema(description = "模板详情")
    private List<EditPaperScoreTemplateDetailParam> templateDetailList;

    @Override
    public void valid() {
        // 创建一个新的ValidationResult实例，用于存储验证结果
        ValidationResult result = new ValidationResult();

        if (ObjectUtils.isEmpty(this.getId())){
            result.addError("模板Id", "请输入模板Id");
        }

        if (StringUtils.isBlank(this.getName())) {
            result.addError("模板名称", "请输入模板名称");
        } else if (this.getName().length() > 30) {
            result.addError("模板名称", "最长可输入30个字符");
        }

        if (ObjectUtils.isEmpty(this.getType())) {
            result.addError("模板类型", "请选择模板类型");
        } else if (PaperScoreTemplateTypeEnum.getByCode(this.getType()).isEmpty()) {
            result.addError("模板类型", "模板类型数据类型错误");
        }

        if (CollectionUtils.isEmpty(this.getTemplateDetailList())) {
            result.addError("模板详情", "请填写模板详情");
        } else {
            for (EditPaperScoreTemplateDetailParam addTemplateDetailParam : this.getTemplateDetailList()) {
                addTemplateDetailParam.valid();
                if (PaperScoreTemplateTypeEnum.CHALLENGE.getCode().equals(this.getType())){
                    if (ObjectUtils.isEmpty(addTemplateDetailParam.getEvaluatePhrases())){
                        result.addError("评价短语", "请选择评价短语");
                    }
                    if (addTemplateDetailParam.getEvaluateText().length() > 100){
                        result.addError("激励文案", "最长可输入100个字符");
                    }
                } else if (PaperScoreTemplateTypeEnum.DIAGNOSTIC.getCode().equals(this.getType())){
                    if (ObjectUtils.isNotEmpty(addTemplateDetailParam.getEvaluatePhrases())){
                        result.addError("评价短语", "无需选择评价短语");
                    }
                    if (addTemplateDetailParam.getEvaluateText().length() > 300){
                        result.addError("激励文案", "最长可输入100个字符");
                    }
                }
            }
        }

        if (result.hasErrors()) {
            throw new IllegalArgumentException(result.getErrorMessage());
        }

    }

    public PaperScoreTemplate toEntity() {
        PaperScoreTemplate paperScoreTemplate = new PaperScoreTemplate();
        paperScoreTemplate.setId(this.getId());
        paperScoreTemplate.setName(this.getName());
        paperScoreTemplate.setType(this.getType());
        paperScoreTemplate.setTemplateDetailList(this.getTemplateDetailList().stream().map(EditPaperScoreTemplateDetailParam::toEntity).collect(Collectors.toList()));
        return paperScoreTemplate;
    }

}
