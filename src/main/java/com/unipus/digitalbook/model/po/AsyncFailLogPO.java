package com.unipus.digitalbook.model.po;

import com.unipus.digitalbook.model.entity.AsyncFailLog;

import java.io.Serializable;
import java.util.Date;

/**
 * 异步处理异常日志表
 * @TableName sync_fail_log
 */
public class AsyncFailLogPO implements Serializable {

    private Long id;
    /**
     * 操作：create update delete
     */
    private String op;
    /**
     * 对象类型
     */
    private String type;
    /**
     * 对象
     */
    private String object;
    /**
     * 失败原因
     */
    private String failReason;
    /**
     * 状态  0-重试后成功 1-首次错误 2-重试错误
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 重试失败时间
     */
    private Date retryFailTime;

    /**
     * 成功时间
     */
    private Date successTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOp() {
        return op;
    }

    public void setOp(String op) {
        this.op = op;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getObject() {
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getRetryFailTime() {
        return retryFailTime;
    }

    public void setRetryFailTime(Date retryFailTime) {
        this.retryFailTime = retryFailTime;
    }

    public Date getSuccessTime() {
        return successTime;
    }

    public void setSuccessTime(Date successTime) {
        this.successTime = successTime;
    }

    public static AsyncFailLogPO builder(AsyncFailLog asyncFailLog) {
        AsyncFailLogPO asyncFailLogPO = new AsyncFailLogPO();
        asyncFailLogPO.setOp(asyncFailLog.getOp());
        asyncFailLogPO.setType(asyncFailLog.getType());
        asyncFailLogPO.setObject(asyncFailLog.getObject());
        asyncFailLogPO.setFailReason(asyncFailLog.getFailReason());
        asyncFailLogPO.setStatus(asyncFailLog.getStatus());
        asyncFailLogPO.setCreateTime(asyncFailLog.getCreateTime());
        asyncFailLogPO.setRetryFailTime(asyncFailLog.getRetryFailTime());
        asyncFailLogPO.setSuccessTime(asyncFailLog.getSuccessTime());
        return asyncFailLogPO;
    }

    public AsyncFailLog toEntity() {
        return AsyncFailLog.builder()
                .setId(this.id)
                .setOp(this.op)
                .setType(this.type)
                .setObject(this.object)
                .setFailReason(this.failReason)
                .setStatus(this.status)
                .setRetryFailTime(this.retryFailTime)
                .setSuccessTime(this.successTime)
                .build();
    }
}
