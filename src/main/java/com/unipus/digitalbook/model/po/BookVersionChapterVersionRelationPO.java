package com.unipus.digitalbook.model.po;

import com.unipus.digitalbook.model.entity.chapter.Chapter;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 *   表：book_version_chapter_version_relation
 *
 * <AUTHOR>
 * @date 2025年04月08日 21:08:19
 */
public class BookVersionChapterVersionRelationPO implements Serializable {
    /**
     * 
     *  关系ID
     *
     * 数据库字段： book_version_chapter_version_relation.id
     *
     * <AUTHOR>
     */
    private Long id;

    /**
     * 
     *  教材版本ID
     *
     * 数据库字段： book_version_chapter_version_relation.book_version_id
     *
     * <AUTHOR>
     */
    private Long bookVersionId;

    /**
     *
     * 数据库字段： book_version_chapter_version_relation.chapter_version_id
     *
     * <AUTHOR>
     */
    private Long chapterVersionId;

    /**
     * 
     *  章节编号（上架资源为章节时）
     *
     * 数据库字段： book_version_chapter_version_relation.chapter_number
     *
     * <AUTHOR>
     */
    private Integer chapterNumber;

    /**
     *
     * 数据库字段： book_version_chapter_version_relation.chapter_name
     *
     * <AUTHOR>
     */
    private String chapterName;

    /**
     * 
     *  创建时间
     *
     * 数据库字段： book_version_chapter_version_relation.create_time
     *
     * <AUTHOR>
     */
    private Date createTime;

    /**
     * 
     *  最后更新时间
     *
     * 数据库字段： book_version_chapter_version_relation.update_time
     *
     * <AUTHOR>
     */
    private Date updateTime;

    /**
     * 
     *  创建者ID
     *
     * 数据库字段： book_version_chapter_version_relation.create_by
     *
     * <AUTHOR>
     */
    private Long createBy;

    /**
     * 
     *  最后更新者ID
     *
     * 数据库字段： book_version_chapter_version_relation.update_by
     *
     * <AUTHOR>
     */
    private Long updateBy;

    /**
     * 
     *  是否有效 0-无效 1-有效
     *
     * 数据库字段： book_version_chapter_version_relation.enable
     *
     * <AUTHOR>
     */
    private Boolean enable;

    public Chapter toChapterEntity(String chapterId) {
        Chapter chapter = new Chapter();
        chapter.setId(chapterId);
        chapter.setBookId(this.getBookVersionId().toString());
        chapter.setChapterNumber(this.getChapterNumber());
        chapter.setName(this.getChapterName());
        return chapter;
    }
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table book_version_chapter_version_relation
     *
     * <AUTHOR>
     */
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取book_version_chapter_version_relation.id
     *
     * @return  book_version_chapter_version_relation 的值.id
     *
     * <AUTHOR>
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置book_version_chapter_version_relation.id
     *
     * @param id the value for book_version_chapter_version_relation.id
     *
     * <AUTHOR>
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取book_version_chapter_version_relation.book_version_id
     *
     * @return  book_version_chapter_version_relation 的值.book_version_id
     *
     * <AUTHOR>
     */
    public Long getBookVersionId() {
        return bookVersionId;
    }

    /**
     * 设置book_version_chapter_version_relation.book_version_id
     *
     * @param bookVersionId the value for book_version_chapter_version_relation.book_version_id
     *
     * <AUTHOR>
     */
    public void setBookVersionId(Long bookVersionId) {
        this.bookVersionId = bookVersionId;
    }

    /**
     * 获取book_version_chapter_version_relation.chapter_version_id
     *
     * @return  book_version_chapter_version_relation 的值.chapter_version_id
     *
     * <AUTHOR>
     */
    public Long getChapterVersionId() {
        return chapterVersionId;
    }

    /**
     * 设置book_version_chapter_version_relation.chapter_version_id
     *
     * @param chapterVersionId the value for book_version_chapter_version_relation.chapter_version_id
     *
     * <AUTHOR>
     */
    public void setChapterVersionId(Long chapterVersionId) {
        this.chapterVersionId = chapterVersionId;
    }

    /**
     * 获取book_version_chapter_version_relation.chapter_number
     *
     * @return  book_version_chapter_version_relation 的值.chapter_number
     *
     * <AUTHOR>
     */
    public Integer getChapterNumber() {
        return chapterNumber;
    }

    /**
     * 设置book_version_chapter_version_relation.chapter_number
     *
     * @param chapterNumber the value for book_version_chapter_version_relation.chapter_number
     *
     * <AUTHOR>
     */
    public void setChapterNumber(Integer chapterNumber) {
        this.chapterNumber = chapterNumber;
    }

    /**
     * 获取book_version_chapter_version_relation.chapter_name
     *
     * @return  book_version_chapter_version_relation 的值.chapter_name
     *
     * <AUTHOR>
     */
    public String getChapterName() {
        return chapterName;
    }

    /**
     * 设置book_version_chapter_version_relation.chapter_name
     *
     * @param chapterName the value for book_version_chapter_version_relation.chapter_name
     *
     * <AUTHOR>
     */
    public void setChapterName(String chapterName) {
        this.chapterName = chapterName == null ? null : chapterName.trim();
    }

    /**
     * 获取book_version_chapter_version_relation.create_time
     *
     * @return  book_version_chapter_version_relation 的值.create_time
     *
     * <AUTHOR>
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置book_version_chapter_version_relation.create_time
     *
     * @param createTime the value for book_version_chapter_version_relation.create_time
     *
     * <AUTHOR>
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取book_version_chapter_version_relation.update_time
     *
     * @return  book_version_chapter_version_relation 的值.update_time
     *
     * <AUTHOR>
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置book_version_chapter_version_relation.update_time
     *
     * @param updateTime the value for book_version_chapter_version_relation.update_time
     *
     * <AUTHOR>
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取book_version_chapter_version_relation.create_by
     *
     * @return  book_version_chapter_version_relation 的值.create_by
     *
     * <AUTHOR>
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 设置book_version_chapter_version_relation.create_by
     *
     * @param createBy the value for book_version_chapter_version_relation.create_by
     *
     * <AUTHOR>
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 获取book_version_chapter_version_relation.update_by
     *
     * @return  book_version_chapter_version_relation 的值.update_by
     *
     * <AUTHOR>
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置book_version_chapter_version_relation.update_by
     *
     * @param updateBy the value for book_version_chapter_version_relation.update_by
     *
     * <AUTHOR>
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 获取book_version_chapter_version_relation.enable
     *
     * @return  book_version_chapter_version_relation 的值.enable
     *
     * <AUTHOR>
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 设置book_version_chapter_version_relation.enable
     *
     * @param enable the value for book_version_chapter_version_relation.enable
     *
     * <AUTHOR>
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * 
     * 转字符 
     * 
     * @return String
     *
     * <AUTHOR>
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bookVersionId=").append(bookVersionId);
        sb.append(", chapterVersionId=").append(chapterVersionId);
        sb.append(", chapterNumber=").append(chapterNumber);
        sb.append(", chapterName=").append(chapterName);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", enable=").append(enable);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}