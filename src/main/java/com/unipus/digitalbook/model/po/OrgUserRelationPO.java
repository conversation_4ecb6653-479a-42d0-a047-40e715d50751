package com.unipus.digitalbook.model.po;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.StringJoiner;

/**
 * 用户机构关系表
 * 表：org_user_relation
 *
 * <AUTHOR>
 * @date 2024年12月03日 02:27:54
 */
public class OrgUserRelationPO implements Serializable {
    /**
     * 主键ID
     * <p>
     * 数据库字段： org_user_relation.id
     *
     */
    private Long id;

    /**
     * 用户ID
     * <p>
     * 数据库字段： org_user_relation.user_id
     *
     */
    private Long userId;

    /**
     * 机构ID
     * <p>
     * 数据库字段： org_user_relation.org_id
     *
     */
    private Long orgId;

    /**
     * 加入时间
     * <p>
     * 数据库字段： org_user_relation.join_time
     *
     */
    private Date joinTime;

    /**
     * 离开时间
     * <p>
     * 数据库字段： org_user_relation.leave_time
     *
     */
    private Date leaveTime;

    /**
     * 状态：1-正常 2-冻结 3-退出
     * <p>
     * 数据库字段： org_user_relation.status
     *
     */
    private Integer status;

    /**
     * 备注信息
     * <p>
     * 数据库字段： org_user_relation.remark
     *
     */
    private String remark;

    /**
     * 创建时间
     * <p>
     * 数据库字段： org_user_relation.create_time
     *
     */
    private Date createTime;

    /**
     * 最后更新时间
     * <p>
     * 数据库字段： org_user_relation.update_time
     *
     */
    private Date updateTime;

    /**
     * 创建者ID
     * <p>
     * 数据库字段： org_user_relation.create_by
     *
     */
    private Long createBy;

    /**
     * 最后更新者ID
     * <p>
     * 数据库字段： org_user_relation.update_by
     *
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     * <p>
     * 数据库字段： org_user_relation.enable
     *
     */
    private Boolean enable;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table org_user_relation
     *
     */
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取org_user_relation.id
     *
     * @return org_user_relation 的值.id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置org_user_relation.id
     *
     * @param id the value for org_user_relation.id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取org_user_relation.user_id
     *
     * @return org_user_relation 的值.user_id
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * 设置org_user_relation.user_id
     *
     * @param userId the value for org_user_relation.user_id
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * 获取org_user_relation.org_id
     *
     * @return org_user_relation 的值.org_id
     */
    public Long getOrgId() {
        return orgId;
    }

    /**
     * 设置org_user_relation.org_id
     *
     * @param orgId the value for org_user_relation.org_id
     */
    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取org_user_relation.join_time
     *
     * @return org_user_relation 的值.join_time
     */
    public Date getJoinTime() {
        return joinTime;
    }

    /**
     * 设置org_user_relation.join_time
     *
     * @param joinTime the value for org_user_relation.join_time
     */
    public void setJoinTime(Date joinTime) {
        this.joinTime = joinTime;
    }

    /**
     * 获取org_user_relation.leave_time
     *
     * @return org_user_relation 的值.leave_time
     */
    public Date getLeaveTime() {
        return leaveTime;
    }

    /**
     * 设置org_user_relation.leave_time
     *
     * @param leaveTime the value for org_user_relation.leave_time
     */
    public void setLeaveTime(Date leaveTime) {
        this.leaveTime = leaveTime;
    }

    /**
     * 获取org_user_relation.status
     *
     * @return org_user_relation 的值.status
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置org_user_relation.status
     *
     * @param status the value for org_user_relation.status
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取org_user_relation.remark
     *
     * @return org_user_relation 的值.remark
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置org_user_relation.remark
     *
     * @param remark the value for org_user_relation.remark
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    /**
     * 获取org_user_relation.create_time
     *
     * @return org_user_relation 的值.create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置org_user_relation.create_time
     *
     * @param createTime the value for org_user_relation.create_time
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取org_user_relation.update_time
     *
     * @return org_user_relation 的值.update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置org_user_relation.update_time
     *
     * @param updateTime the value for org_user_relation.update_time
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取org_user_relation.create_by
     *
     * @return org_user_relation 的值.create_by
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 设置org_user_relation.create_by
     *
     * @param createBy the value for org_user_relation.create_by
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 获取org_user_relation.update_by
     *
     * @return org_user_relation 的值.update_by
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置org_user_relation.update_by
     *
     * @param updateBy the value for org_user_relation.update_by
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 获取org_user_relation.enable
     *
     * @return org_user_relation 的值.enable
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 设置org_user_relation.enable
     *
     * @param enable the value for org_user_relation.enable
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * 转字符
     *
     * @return String
     */
    @Override
    public String toString() {
        return new StringJoiner(", ", OrgUserRelationPO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("userId=" + userId)
                .add("orgId=" + orgId)
                .add("joinTime=" + joinTime)
                .add("leaveTime=" + leaveTime)
                .add("status=" + status)
                .add("remark='" + remark + "'")
                .add("createTime=" + createTime)
                .add("updateTime=" + updateTime)
                .add("createBy=" + createBy)
                .add("updateBy=" + updateBy)
                .add("enable=" + enable)
                .toString();
    }

    public static class Builder {
        private OrgUserRelationPO orgUserRelationPO;

        public Builder() {
            this.orgUserRelationPO = new OrgUserRelationPO();
        }

        public Builder id(Long id) {
            this.orgUserRelationPO.setId(id);
            return this;
        }

        public Builder userId(Long userId) {
            this.orgUserRelationPO.setUserId(userId);
            return this;
        }

        public Builder orgId(Long orgId) {
            this.orgUserRelationPO.setOrgId(orgId);
            return this;
        }

        public Builder joinTime(Date joinTime) {
            this.orgUserRelationPO.setJoinTime(joinTime);
            return this;
        }

        public Builder leaveTime(Date leaveTime) {
            this.orgUserRelationPO.setLeaveTime(leaveTime);
            return this;
        }

        public Builder status(Integer status) {
            this.orgUserRelationPO.setStatus(status);
            return this;
        }

        public Builder remark(String remark) {
            this.orgUserRelationPO.setRemark(remark);
            return this;
        }

        public Builder createBy(Long createBy) {
            this.orgUserRelationPO.setCreateBy(createBy);
            return this;
        }

        public OrgUserRelationPO build() {
            // 检验逻辑自洽，类似于加入的relation 存在了离开时间，等一系列的逻辑校验

            // 检查加入时间是否为空
            if (this.orgUserRelationPO.getJoinTime() == null) {
                throw new IllegalStateException("加入时间（joinTime）不能为空");
            }

            // 检查离开时间是否早于加入时间
            if (this.orgUserRelationPO.getLeaveTime() != null && this.orgUserRelationPO.getJoinTime().after(this.orgUserRelationPO.getLeaveTime())) {
                throw new IllegalStateException("加入时间（joinTime）不能晚于离开时间（leaveTime）");
            }

            // 检查状态是否为空
            if (this.orgUserRelationPO.getStatus() == null) {
                throw new IllegalStateException("状态（status）不能为空");
            }

            // 检查状态是否在有效范围内
            if (this.orgUserRelationPO.getStatus() < 1 || this.orgUserRelationPO.getStatus() > 3) {
                throw new IllegalStateException("状态（status）必须是1-正常、2-冻结或3-退出");
            }

            // 检查用户ID是否为空
            if (this.orgUserRelationPO.getUserId() == null) {
                throw new IllegalStateException("用户ID（userId）不能为空");
            }

            // 检查机构ID是否为空
            if (this.orgUserRelationPO.getOrgId() == null) {
                throw new IllegalStateException("机构ID（orgId）不能为空");
            }

            // 检查离开时间和状态的一致性
            if (this.orgUserRelationPO.getLeaveTime() != null && this.orgUserRelationPO.getStatus() != 3) {
                throw new IllegalStateException("如果存在离开时间（leaveTime），状态（status）必须是3-退出");
            }

            return this.orgUserRelationPO;
        }


    }
}