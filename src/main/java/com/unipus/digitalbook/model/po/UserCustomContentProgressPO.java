package com.unipus.digitalbook.model.po;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/25 10:18
 */
/**
 * 用户自建内容进度表
 */
public class UserCustomContentProgressPO {
    /**
    * 主键ID
    */
    private Long id;

    /**
    * 用户OpenID
    */
    private String openId;

    /**
    * 租户ID
    */
    private Long tenantId;

    /**
    * 自建内容ID
    */
    private Long contentId;

    /**
    * 进度
    */
    private byte[] progressBit;

    /**
    * 是否启用
    */
    private Boolean enable;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getContentId() {
        return contentId;
    }

    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    public byte[] getProgressBit() {
        return progressBit;
    }

    public void setProgressBit(byte[] progressBit) {
        this.progressBit = progressBit;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}