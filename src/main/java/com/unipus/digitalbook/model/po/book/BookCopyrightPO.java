package com.unipus.digitalbook.model.po.book;

import com.unipus.digitalbook.model.entity.book.BookCopyright;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 教材版权信息表
 *
 * @TableName book_copyright
 */
public class BookCopyrightPO implements Serializable {
    /**
     * 版权信息ID
     */
    private Long id;

    /**
     * 关联教材ID
     */
    private String bookId;

    /**
     * 版本
     */
    private String versionNumber;

    /**
     * 总主编
     */
    private String chiefEditor;

    /**
     * 主编
     */
    private String editor;

    /**
     * 副主编
     */
    private String deputyEditor;

    /**
     * 项目策划
     */
    private String projectPlanner;

    /**
     * 责任编辑
     */
    private String executiveEditor;

    /**
     * 责任校对
     */
    private String proofreader;

    /**
     * 数字编辑
     */
    private String digitalEditor;

    /**
     * 封面设计
     */
    private String coverDesigner;

    /**
     * 版式设计
     */
    private String layoutDesigner;

    /**
     * 出版发行
     */
    private String publisher;

    /**
     * 字数
     */
    private String wordCount;

    /**
     * 出版时间-年
     */
    private Integer publishYear;

    /**
     * 出版时间-月
     */
    private Integer publishMonth;

    /**
     * 出版时间-日
     */
    private Integer publishDay;

    /**
     * ISBN
     */
    private String isbn;

    /**
     * 定价
     */
    private BigDecimal price;

    /**
     * 版次
     */
    private String edition;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;


    public BookCopyrightPO() {
    }

    public BookCopyright toEntity() {
        BookCopyright bookCopyright = new BookCopyright();
        bookCopyright.setId(this.id);
        bookCopyright.setBookId(this.bookId);
        bookCopyright.setChiefEditor(this.chiefEditor);
        bookCopyright.setEditor(this.editor);
        bookCopyright.setDeputyEditor(this.deputyEditor);
        bookCopyright.setProjectPlanner(this.projectPlanner);
        bookCopyright.setExecutiveEditor(this.executiveEditor);
        bookCopyright.setProofreader(this.proofreader);
        bookCopyright.setDigitalEditor(this.digitalEditor);
        bookCopyright.setCoverDesigner(this.coverDesigner);
        bookCopyright.setLayoutDesigner(this.layoutDesigner);
        bookCopyright.setPublisher(this.publisher);
        bookCopyright.setWordCount(this.wordCount);
        bookCopyright.setIsbn(this.isbn);
        bookCopyright.setPrice(this.price);
        bookCopyright.setEdition(this.edition);
        bookCopyright.setCreateBy(this.createBy);
        bookCopyright.setUpdateBy(this.updateBy);
        bookCopyright.setEnable(this.enable);
        bookCopyright.setVersionNumber(this.versionNumber);
        bookCopyright.setCreateTime(this.createTime);
        bookCopyright.setUpdateTime(this.updateTime);
        bookCopyright.setPublishYear(this.publishYear);
        bookCopyright.setPublishMonth(this.publishMonth);
        bookCopyright.setPublishDay(this.publishDay);
        return bookCopyright;
    }

    public BookCopyrightPO(BookCopyright bookCopyright) {
        this.bookId = bookCopyright.getBookId();
        this.chiefEditor = bookCopyright.getChiefEditor();
        this.editor = bookCopyright.getEditor();
        this.deputyEditor = bookCopyright.getDeputyEditor();
        this.projectPlanner = bookCopyright.getProjectPlanner();
        this.executiveEditor = bookCopyright.getExecutiveEditor();
        this.proofreader = bookCopyright.getProofreader();
        this.digitalEditor = bookCopyright.getDigitalEditor();
        this.coverDesigner = bookCopyright.getCoverDesigner();
        this.layoutDesigner = bookCopyright.getLayoutDesigner();
        this.publisher = bookCopyright.getPublisher();
        this.wordCount = bookCopyright.getWordCount();
        this.publishYear = bookCopyright.getPublishYear();
        this.publishMonth = bookCopyright.getPublishMonth();
        this.publishDay = bookCopyright.getPublishDay();
        this.isbn = bookCopyright.getIsbn();
        this.price = bookCopyright.getPrice();
        this.edition = bookCopyright.getEdition();
        this.createBy = bookCopyright.getCreateBy();
        this.updateBy = bookCopyright.getUpdateBy();
    }

    public BookCopyrightPO fromEntity(BookCopyright bookIntro) {
        if (bookIntro == null) {
            return null;
        }
        BookCopyrightPO bookCopyrightPO = new BookCopyrightPO();
        bookCopyrightPO.setId(bookIntro.getId());
        bookCopyrightPO.setBookId(bookIntro.getBookId()); // 假设BookCopyright中有getBookId方法
        bookCopyrightPO.setVersionNumber(bookIntro.getVersionNumber());
        bookCopyrightPO.setChiefEditor(bookIntro.getChiefEditor());
        bookCopyrightPO.setEditor(bookIntro.getEditor());
        bookCopyrightPO.setDeputyEditor(bookIntro.getDeputyEditor());
        bookCopyrightPO.setProjectPlanner(bookIntro.getProjectPlanner());
        bookCopyrightPO.setExecutiveEditor(bookIntro.getExecutiveEditor());
        bookCopyrightPO.setProofreader(bookIntro.getProofreader());
        bookCopyrightPO.setDigitalEditor(bookIntro.getDigitalEditor());
        bookCopyrightPO.setCoverDesigner(bookIntro.getCoverDesigner());
        bookCopyrightPO.setLayoutDesigner(bookIntro.getLayoutDesigner());
        bookCopyrightPO.setPublisher(bookIntro.getPublisher());
        bookCopyrightPO.setWordCount(bookIntro.getWordCount());
        bookCopyrightPO.setPublishYear(bookIntro.getPublishYear());
        bookCopyrightPO.setPublishMonth(bookIntro.getPublishMonth());
        bookCopyrightPO.setPublishDay(bookIntro.getPublishDay());
        bookCopyrightPO.setIsbn(bookIntro.getIsbn());
        bookCopyrightPO.setPrice(bookIntro.getPrice());
        bookCopyrightPO.setEdition(bookIntro.getEdition());
        bookCopyrightPO.setCreateTime(bookIntro.getCreateTime());
        bookCopyrightPO.setUpdateTime(bookIntro.getUpdateTime());
        bookCopyrightPO.setCreateBy(bookIntro.getCreateBy());
        bookCopyrightPO.setUpdateBy(bookIntro.getUpdateBy());
        bookCopyrightPO.setEnable(bookIntro.getEnable());

        return bookCopyrightPO;
    }


    /**
     * 版权信息ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 版权信息ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 关联教材ID
     */
    public String getBookId() {
        return bookId;
    }

    /**
     * 关联教材ID
     */
    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    /**
     * 版本
     */
    public String getVersionNumber() {
        return versionNumber;
    }

    /**
     * 版本
     */
    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    /**
     * 总主编
     */
    public String getChiefEditor() {
        return chiefEditor;
    }

    /**
     * 总主编
     */
    public void setChiefEditor(String chiefEditor) {
        this.chiefEditor = chiefEditor;
    }

    /**
     * 主编
     */
    public String getEditor() {
        return editor;
    }

    /**
     * 主编
     */
    public void setEditor(String editor) {
        this.editor = editor;
    }

    /**
     * 副主编
     */
    public String getDeputyEditor() {
        return deputyEditor;
    }

    /**
     * 副主编
     */
    public void setDeputyEditor(String deputyEditor) {
        this.deputyEditor = deputyEditor;
    }

    /**
     * 项目策划
     */
    public String getProjectPlanner() {
        return projectPlanner;
    }

    /**
     * 项目策划
     */
    public void setProjectPlanner(String projectPlanner) {
        this.projectPlanner = projectPlanner;
    }

    /**
     * 责任编辑
     */
    public String getExecutiveEditor() {
        return executiveEditor;
    }

    /**
     * 责任编辑
     */
    public void setExecutiveEditor(String executiveEditor) {
        this.executiveEditor = executiveEditor;
    }

    /**
     * 责任校对
     */
    public String getProofreader() {
        return proofreader;
    }

    /**
     * 责任校对
     */
    public void setProofreader(String proofreader) {
        this.proofreader = proofreader;
    }

    /**
     * 数字编辑
     */
    public String getDigitalEditor() {
        return digitalEditor;
    }

    /**
     * 数字编辑
     */
    public void setDigitalEditor(String digitalEditor) {
        this.digitalEditor = digitalEditor;
    }

    /**
     * 封面设计
     */
    public String getCoverDesigner() {
        return coverDesigner;
    }

    /**
     * 封面设计
     */
    public void setCoverDesigner(String coverDesigner) {
        this.coverDesigner = coverDesigner;
    }

    /**
     * 版式设计
     */
    public String getLayoutDesigner() {
        return layoutDesigner;
    }

    /**
     * 版式设计
     */
    public void setLayoutDesigner(String layoutDesigner) {
        this.layoutDesigner = layoutDesigner;
    }

    /**
     * 出版发行
     */
    public String getPublisher() {
        return publisher;
    }

    /**
     * 出版发行
     */
    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }

    /**
     * 字数
     */
    public String getWordCount() {
        return wordCount;
    }

    /**
     * 字数
     */
    public void setWordCount(String wordCount) {
        this.wordCount = wordCount;
    }

    /**
     * 出版时间-年
     */
    public Integer getPublishYear() {
        return publishYear;
    }

    /**
     * 出版时间-年
     */
    public void setPublishYear(Integer publishYear) {
        this.publishYear = publishYear;
    }

    /**
     * 出版时间-月
     */
    public Integer getPublishMonth() {
        return publishMonth;
    }

    /**
     * 出版时间-月
     */
    public void setPublishMonth(Integer publishMonth) {
        this.publishMonth = publishMonth;
    }

    /**
     * 出版时间-日
     */
    public Integer getPublishDay() {
        return publishDay;
    }

    /**
     * 出版时间-日
     */
    public void setPublishDay(Integer publishDay) {
        this.publishDay = publishDay;
    }

    /**
     * ISBN
     */
    public String getIsbn() {
        return isbn;
    }

    /**
     * ISBN
     */
    public void setIsbn(String isbn) {
        this.isbn = isbn;
    }

    /**
     * 定价
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 定价
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * 版次
     */
    public String getEdition() {
        return edition;
    }

    /**
     * 版次
     */
    public void setEdition(String edition) {
        this.edition = edition;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 最后更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建者ID
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 创建者ID
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 最后更新者ID
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 最后更新者ID
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}