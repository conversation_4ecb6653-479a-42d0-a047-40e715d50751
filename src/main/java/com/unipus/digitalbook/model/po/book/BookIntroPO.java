package com.unipus.digitalbook.model.po.book;

import com.unipus.digitalbook.model.entity.book.BookIntro;

import java.io.Serializable;
import java.util.Date;

/**
 * 教材简介表
 *
 * @TableName book_intro
 */
public class BookIntroPO implements Serializable {
    /**
     * 简介ID
     */
    private Long id;

    /**
     * 关联教材ID
     */
    private String bookId;

    /**
     * 教材详细介绍
     */
    private String description;

    /**
     * 版本号
     */
    private String versionNumber;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    public BookIntro toEntity() {
        BookIntro bookIntro = new BookIntro();
        bookIntro.setId(this.getId());
        bookIntro.setBookId(this.getBookId());
        bookIntro.setDescription(this.getDescription());
        bookIntro.setVersionNumber(this.getVersionNumber());
        bookIntro.setCreateTime(this.getCreateTime());
        bookIntro.setUpdateTime(this.getUpdateTime());
        bookIntro.setCreateBy(this.getCreateBy());
        bookIntro.setUpdateBy(this.getUpdateBy());
        bookIntro.setEnable(this.getEnable());
        return bookIntro;
    }

    public BookIntroPO fromEntity(BookIntro bookIntro) {
        if (bookIntro == null) {
            return null;
        }
        BookIntroPO bookIntroPO = new BookIntroPO();
        bookIntroPO.setId(bookIntro.getId());
        bookIntroPO.setBookId(bookIntro.getBookId());
        bookIntroPO.setDescription(bookIntro.getDescription());
        bookIntroPO.setVersionNumber(bookIntro.getVersionNumber());
        bookIntroPO.setCreateTime(bookIntro.getCreateTime());
        bookIntroPO.setUpdateTime(bookIntro.getUpdateTime());
        bookIntroPO.setCreateBy(bookIntro.getCreateBy());
        bookIntroPO.setUpdateBy(bookIntro.getUpdateBy());
        bookIntroPO.setEnable(bookIntro.getEnable());

        return bookIntroPO;
    }


    /**
     * 简介ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 简介ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 关联教材ID
     */
    public String getBookId() {
        return bookId;
    }

    /**
     * 关联教材ID
     */
    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    /**
     * 教材详细介绍
     */
    public String getDescription() {
        return description;
    }

    /**
     * 教材详细介绍
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 版本号
     */
    public String getVersionNumber() {
        return versionNumber;
    }

    /**
     * 版本号
     */
    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 最后更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建者ID
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 创建者ID
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 最后更新者ID
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 最后更新者ID
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}