package com.unipus.digitalbook.model.po.book;

import com.unipus.digitalbook.model.entity.book.AddBookOperationLog;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.StringJoiner;

/**
 * 教材操作日志表
 * 表：book_operation_log
 *
 * <AUTHOR>
 * @date 2024年12月16日 14:17:25
 */
public class BookOperationLogPO implements Serializable {
    /**
     * 主键ID
     * <p>
     * 数据库字段： book_operation_log.id
     */
    private Long id;

    /**
     * 教材ID
     * <p>
     * 数据库字段： book_operation_log.book_id
     */
    private String bookId;

    /**
     * 操作用户ID
     * <p>
     * 数据库字段： book_operation_log.operation_user_id
     */
    private Long operationUserId;

    /**
     * 操作时间
     * <p>
     * 数据库字段： book_operation_log.operation_time
     */
    private Date operationTime;

    /**
     * 操作内容
     * <p>
     * 数据库字段： book_operation_log.operation_content
     */
    private String operationContent;

    /**
     * 操作类型：1-新增 2-编辑 3-删除
     * <p>
     * 数据库字段： book_operation_log.operation_type
     */
    private Integer operationType;

    /**
     * 创建时间
     * <p>
     * 数据库字段： book_operation_log.create_time
     */
    private Date createTime;

    /**
     * 最后更新时间
     * <p>
     * 数据库字段： book_operation_log.update_time
     */
    private Date updateTime;

    /**
     * 创建者ID
     * <p>
     * 数据库字段： book_operation_log.create_by
     */
    private Long createBy;

    /**
     * 最后更新者ID
     * <p>
     * 数据库字段： book_operation_log.update_by
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     * <p>
     * 数据库字段： book_operation_log.enable
     */
    private Boolean enable;

    public BookOperationLogPO() {
    }

    public BookOperationLogPO(AddBookOperationLog addBookOperationLog) {
        if (addBookOperationLog == null) {
            return;
        }
        this.bookId = addBookOperationLog.getBookId();
        this.operationUserId = addBookOperationLog.getOperationUserId();
        this.operationTime = addBookOperationLog.getOperationTime();
        this.operationContent = addBookOperationLog.getOperationContent();
        this.operationType = addBookOperationLog.getOperationType();
        this.createTime = addBookOperationLog.getCreateTime();
        this.createBy = addBookOperationLog.getCreateBy();
    }

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table book_operation_log
     */
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取book_operation_log.id
     *
     * @return book_operation_log 的值.id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置book_operation_log.id
     *
     * @param id the value for book_operation_log.id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取book_operation_log.book_id
     *
     * @return book_operation_log 的值.book_id
     */
    public String getBookId() {
        return bookId;
    }

    /**
     * 设置book_operation_log.book_id
     *
     * @param bookId the value for book_operation_log.book_id
     */
    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    /**
     * 获取book_operation_log.operation_user_id
     *
     * @return book_operation_log 的值.operation_user_id
     */
    public Long getOperationUserId() {
        return operationUserId;
    }

    /**
     * 设置book_operation_log.operation_user_id
     *
     * @param operationUserId the value for book_operation_log.operation_user_id
     */
    public void setOperationUserId(Long operationUserId) {
        this.operationUserId = operationUserId;
    }

    /**
     * 获取book_operation_log.operation_time
     *
     * @return book_operation_log 的值.operation_time
     */
    public Date getOperationTime() {
        return operationTime;
    }

    /**
     * 设置book_operation_log.operation_time
     *
     * @param operationTime the value for book_operation_log.operation_time
     */
    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    /**
     * 获取book_operation_log.operation_content
     *
     * @return book_operation_log 的值.operation_content
     */
    public String getOperationContent() {
        return operationContent;
    }

    /**
     * 设置book_operation_log.operation_content
     *
     * @param operationContent the value for book_operation_log.operation_content
     */
    public void setOperationContent(String operationContent) {
        this.operationContent = operationContent == null ? null : operationContent.trim();
    }

    /**
     * 获取book_operation_log.operation_type
     *
     * @return book_operation_log 的值.operation_type
     */
    public Integer getOperationType() {
        return operationType;
    }

    /**
     * 设置book_operation_log.operation_type
     *
     * @param operationType the value for book_operation_log.operation_type
     */
    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    /**
     * 获取book_operation_log.create_time
     *
     * @return book_operation_log 的值.create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置book_operation_log.create_time
     *
     * @param createTime the value for book_operation_log.create_time
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取book_operation_log.update_time
     *
     * @return book_operation_log 的值.update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置book_operation_log.update_time
     *
     * @param updateTime the value for book_operation_log.update_time
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取book_operation_log.create_by
     *
     * @return book_operation_log 的值.create_by
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 设置book_operation_log.create_by
     *
     * @param createBy the value for book_operation_log.create_by
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 获取book_operation_log.update_by
     *
     * @return book_operation_log 的值.update_by
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置book_operation_log.update_by
     *
     * @param updateBy the value for book_operation_log.update_by
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 获取book_operation_log.enable
     *
     * @return book_operation_log 的值.enable
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 设置book_operation_log.enable
     *
     * @param enable the value for book_operation_log.enable
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * 转字符
     *
     * @return String
     * <AUTHOR>
     */
    @Override
    public String toString() {
        return new StringJoiner(", ", BookOperationLogPO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("bookId='" + bookId + "'")
                .add("operationUserId=" + operationUserId)
                .add("operationTime=" + operationTime)
                .add("operationContent='" + operationContent + "'")
                .add("operationType=" + operationType)
                .add("createTime=" + createTime)
                .add("updateTime=" + updateTime)
                .add("createBy=" + createBy)
                .add("updateBy=" + updateBy)
                .add("enable=" + enable)
                .toString();
    }
}