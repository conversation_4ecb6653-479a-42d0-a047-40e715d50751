package com.unipus.digitalbook.model.po.book;

import com.unipus.digitalbook.model.entity.book.Book;

import java.io.Serializable;
import java.util.Date;

/**
 * 教材表
 *
 * @TableName book
 */
public class BookPO implements Serializable {
    /**
     * 教材ID
     */
    private String id;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 编者
     */
    private Long editorId;

    /**
     * 教材状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    public BookPO() {
    }

    public BookPO(Book book) {
        if (book == null) {
            return;
        }
        this.setId(book.getId());
        this.setOrgId(book.getOrgId());
        this.setEditorId(book.getEditorId());
        this.setCreateTime(book.getCreateTime());
        this.setUpdateTime(book.getUpdateTime());
        this.setCreateBy(book.getCreateBy());
        this.setUpdateBy(book.getUpdateBy());
        this.setEnable(book.getEnable());
    }

    public Book toEntity() {
        Book book = new Book();
        book.setId(id);
        book.setOrgId(orgId);
        book.setStatus(status);
        book.setEditorId(editorId);
        book.setCreateTime(createTime);
        book.setUpdateTime(updateTime);
        book.setEnable(enable);
        book.setCreateBy(createBy);
        return book;
    }

    public void fromEntity(Book book) {
        this.setId(book.getId());
        this.setOrgId(book.getOrgId());
        this.setEditorId(book.getEditorId());
        this.setStatus(1);
        this.setCreateTime(book.getCreateTime());
        this.setUpdateTime(book.getUpdateTime());
        this.setCreateBy(book.getCreateBy());
        this.setUpdateBy(book.getUpdateBy());
        this.setEnable(true);
    }

    /**
     * 教材ID
     */
    public String getId() {
        return id;
    }

    /**
     * 教材ID
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 机构ID
     */
    public Long getOrgId() {
        return orgId;
    }

    /**
     * 机构ID
     */
    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    /**
     * 编者
     */
    public Long getEditorId() {
        return editorId;
    }

    /**
     * 编者
     */
    public void setEditorId(Long editorId) {
        this.editorId = editorId;
    }

    /**
     * 教材状态
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 教材状态
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 最后更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建者ID
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 创建者ID
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 最后更新者ID
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 最后更新者ID
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 是否有效 true 有效
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 是否有效 true 有效
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}