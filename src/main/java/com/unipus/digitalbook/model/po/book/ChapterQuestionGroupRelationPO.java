package com.unipus.digitalbook.model.po.book;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName chapter_question_group_relation
 */
public class ChapterQuestionGroupRelationPO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 版本章节id
     */
    private Long versionChapterId;

    /**
     * 题组id
     */
    private Long groupId;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private Long createBy;

    /**
     * 创建人
     */
    private Long updateBy;

    /**
     * 是否有效 1有效 0无效
     */
    private Boolean enable;

    public ChapterQuestionGroupRelationPO() {

    }
    public ChapterQuestionGroupRelationPO(Long groupId, Long versionChapterId, Long currentUserId) {
        this.groupId = groupId;
        this.versionChapterId = versionChapterId;
        this.createBy = currentUserId;
        this.updateBy = currentUserId;
    }


    /**
     * 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 版本章节id
     */
    public Long getVersionChapterId() {
        return versionChapterId;
    }

    /**
     * 版本章节id
     */
    public void setVersionChapterId(Long versionChapterId) {
        this.versionChapterId = versionChapterId;
    }

    /**
     * 题组id
     */
    public Long getGroupId() {
        return groupId;
    }

    /**
     * 题组id
     */
    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 更新人
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 更新人
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 创建人
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 创建人
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 是否有效 1有效 0无效
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 是否有效 1有效 0无效
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

}