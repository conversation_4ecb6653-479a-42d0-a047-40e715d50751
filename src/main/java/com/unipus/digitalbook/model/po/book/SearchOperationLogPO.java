package com.unipus.digitalbook.model.po.book;

import com.unipus.digitalbook.model.entity.book.SearchOperationLog;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户教材操作日志
 *
 * <AUTHOR>
 * @date 2024年12月16日 14:17:25
 */
public class SearchOperationLogPO implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 教材ID
     */
    private String bookId;

    /**
     * 操作用户ID
     */
    private Long operationUserId;

    /**
     * 操作用户名称
     */
    private String operationUserName;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 操作内容
     */
    private String operationContent;

    /**
     * 操作类型：1-新增 2-编辑 3-删除
     */
    private Integer operationType;

    public SearchOperationLog toEntity() {
        SearchOperationLog entity = new SearchOperationLog();
        entity.setId(this.getId());
        entity.setBookId(this.getBookId());
        entity.setOperationUserId(this.getOperationUserId());
        entity.setOperationUserName(this.getOperationUserName());
        entity.setOperationTime(this.getOperationTime());
        entity.setOperationContent(this.getOperationContent());
        entity.setOperationType(this.getOperationType());
        return entity;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public Long getOperationUserId() {
        return operationUserId;
    }

    public void setOperationUserId(Long operationUserId) {
        this.operationUserId = operationUserId;
    }

    public String getOperationUserName() {
        return operationUserName;
    }

    public void setOperationUserName(String operationUserName) {
        this.operationUserName = operationUserName;
    }

    public Date getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    public String getOperationContent() {
        return operationContent;
    }

    public void setOperationContent(String operationContent) {
        this.operationContent = operationContent;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    /**
     * 转字符
     *
     * @return String
     * <AUTHOR>
     */
    @Override
    public String toString() {
        return getClass().getSimpleName() +
                " [" +
                "Hash = " + hashCode() +
                ", id=" + id +
                ", bookId=" + bookId +
                ", operationUserId=" + operationUserId +
                ", operationUserName=" + operationUserName +
                ", operationTime=" + operationTime +
                ", operationContent=" + operationContent +
                ", operationType=" + operationType +
                "]";
    }
}