package com.unipus.digitalbook.model.po.chapter;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 章节版本表
 * 表：chapter_version
 *
 * <AUTHOR>
 * @date 2025年01月02日 16:48:04
 */
@Data
public class ChapterVersionPO implements Serializable {
    /**
     * 版本ID
     * <p>
     * 数据库字段： chapter_version.id
     *
     * <AUTHOR>
     */
    private Long id;

    /**
     * 关联章节ID
     * <p>
     * 数据库字段： chapter_version.chapter_id
     *
     * <AUTHOR>
     */
    private String chapterId;

    /**
     * 版本号
     * <p>
     * 数据库字段： chapter_version.version_number
     *
     * <AUTHOR>
     */
    private String versionNumber;

    /**
     * 创建时间
     * <p>
     * 数据库字段： chapter_version.create_time
     *
     * <AUTHOR>
     */
    private Date createTime;

    /**
     * 更新时间
     * <p>
     * 数据库字段： chapter_version.update_time
     *
     * <AUTHOR>
     */
    private Date updateTime;

    /**
     * 创建者ID
     * <p>
     * 数据库字段： chapter_version.create_by
     *
     * <AUTHOR>
     */
    private Long createBy;

    /**
     * 最后更新者ID
     * <p>
     * 数据库字段： chapter_version.update_by
     *
     * <AUTHOR>
     */
    private Long updateBy;

    /**
     * 是否有效 ture 有效
     * <p>
     * 数据库字段： chapter_version.enable
     *
     * <AUTHOR>
     */
    private Boolean enable;

    /**
     * 内容
     * <p>
     * 数据库字段： chapter_version.content
     *
     * <AUTHOR>
     */
    private String content;

    /**
     * 资源
     * <p>
     * 数据库字段： chapter_version.resource_json
     *
     * <AUTHOR>
     */
    private String resourceJson;

    /**
     * 目录
     * <p>
     * 数据库字段： chapter_version.catalog_json
     *
     * <AUTHOR>
     */
    private String catalogJson;

    /**
     * 章节内容的全部结构
     * <p>
     * 数据库字段： chapter_version.total_struct
     *
     * <AUTHOR>
     */
    private List<ChapterNodePO> totalStruct;

    /**
     * 学生内容json
     * <p>
     * 数据库字段： chapter_version.student_content
     *
     * <AUTHOR>
     */
    private String studentContent;

    /**
     * 章节名称
     * <p>
     * 数据库字段： chapter_version.name
     *
     * <AUTHOR>
     */
    private String name;

    /**
     * 封面图片
     * <p>
     * 数据库字段： chapter_version.header_img
     *
     * <AUTHOR>
     */
    private String headerImg;
    /**
     * 获取chapter_version.id
     *
     * @return chapter_version 的值.id
     * <AUTHOR>
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置chapter_version.id
     *
     * @param id the value for chapter_version.id
     * <AUTHOR>
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取chapter_version.chapter_id
     *
     * @return chapter_version 的值.chapter_id
     * <AUTHOR>
     */
    public String getChapterId() {
        return chapterId;
    }

    /**
     * 设置chapter_version.chapter_id
     *
     * @param chapterId the value for chapter_version.chapter_id
     * <AUTHOR>
     */
    public void setChapterId(String chapterId) {
        this.chapterId = chapterId == null ? null : chapterId.trim();
    }

    /**
     * 获取chapter_version.version_number
     *
     * @return chapter_version 的值.version_number
     * <AUTHOR>
     */
    public String getVersionNumber() {
        return versionNumber;
    }

    /**
     * 设置chapter_version.version_number
     *
     * @param versionNumber the value for chapter_version.version_number
     * <AUTHOR>
     */
    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber == null ? null : versionNumber.trim();
    }

    /**
     * 获取chapter_version.create_time
     *
     * @return chapter_version 的值.create_time
     * <AUTHOR>
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置chapter_version.create_time
     *
     * @param createTime the value for chapter_version.create_time
     * <AUTHOR>
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取chapter_version.update_time
     *
     * @return chapter_version 的值.update_time
     * <AUTHOR>
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置chapter_version.update_time
     *
     * @param updateTime the value for chapter_version.update_time
     * <AUTHOR>
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取chapter_version.create_by
     *
     * @return chapter_version 的值.create_by
     * <AUTHOR>
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 设置chapter_version.create_by
     *
     * @param createBy the value for chapter_version.create_by
     * <AUTHOR>
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 获取chapter_version.update_by
     *
     * @return chapter_version 的值.update_by
     * <AUTHOR>
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置chapter_version.update_by
     *
     * @param updateBy the value for chapter_version.update_by
     * <AUTHOR>
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 获取chapter_version.enable
     *
     * @return chapter_version 的值.enable
     * <AUTHOR>
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 设置chapter_version.enable
     *
     * @param enable the value for chapter_version.enable
     * <AUTHOR>
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * 获取chapter_version.content
     *
     * @return chapter_version 的值.content
     * <AUTHOR>
     */
    public String getContent() {
        return content;
    }

    /**
     * 设置chapter_version.content
     *
     * @param content the value for chapter_version.content
     * <AUTHOR>
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    public String getResourceJson() {
        return resourceJson;
    }

    public void setResourceJson(String resourceJson) {
        this.resourceJson = resourceJson;
    }

    public String getCatalogJson() {
        return catalogJson;
    }

    public void setCatalogJson(String catalogJson) {
        this.catalogJson = catalogJson;
    }

    public String getStudentContent() {
        return studentContent;
    }

    public void setStudentContent(String studentContent) {
        this.studentContent = studentContent;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHeaderImg() {
        return headerImg;
    }

    public void setHeaderImg(String headerImg) {
        this.headerImg = headerImg;
    }

    /**
     * 转字符
     *
     * @return String
     * <AUTHOR>
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", chapterId=").append(chapterId);
        sb.append(", versionNumber=").append(versionNumber);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", enable=").append(enable);
        sb.append(", content=").append(content);
        sb.append(", resourceJson=").append(resourceJson);
        sb.append(", catalogJson=").append(catalogJson);
        sb.append(", studentContent=").append(studentContent);
        sb.append(", name=").append(name);
        sb.append(", headerImg=").append(headerImg);
        sb.append("]");
        return sb.toString();
    }

    public void fromEntity(ChapterVersion entity) {
        if (entity == null) {
            return ;
        }
        this.setId(entity.getId());
        this.setChapterId(entity.getChapterId());
        this.setVersionNumber(entity.getVersionNumber());
        this.setCreateTime(entity.getCreateTime());
        this.setCreateBy(entity.getCreateBy());
        this.setContent(entity.getContent());
        // 设置默认值或根据业务逻辑设置其他字段
        this.setUpdateTime(entity.getCreateTime()); // 假设创建时间和更新时间相同
        this.setUpdateBy(entity.getCreateBy()); // 假设创建者和更新者相同
        this.setEnable(true); // 假设新创建的版本默认有效
        this.setResourceJson(entity.getResource());
        this.setCatalogJson(JSON.toJSONString(entity.getHeaderNodeList()));
        this.setStudentContent(entity.getStudentContent());
        this.setName(entity.getName());
        this.setHeaderImg(entity.getHeaderImg());
        this.setTotalStruct( entity.getChapterNodeList().stream().map(ChapterNodePO::new).toList());
    }

    //todo 通过建造器完成类型转换
    public ChapterVersion toEntity() {
        ChapterVersion.Builder builder = ChapterVersion.Builder.getInstance();
        builder.id(this.getId())
                .chapterId(this.getChapterId())
                .versionNumber(this.getVersionNumber())
                .createTime(this.getCreateTime())
                .content(this.getContent())
                .resources(this.getResourceJson())
                .studentContent(this.getStudentContent())
                .name(this.getName())
                .headerImg(this.getHeaderImg())
                .catalog(this.getCatalogJson())
                .chapterNodePOList(this.getTotalStruct())
                .createBy(this.getCreateBy());
        return builder.build();
    }


    public List<ChapterNodePO> getTotalStruct() {
        return totalStruct;
    }

    public void setTotalStruct(List<ChapterNodePO> totalStruct) {
        this.totalStruct = totalStruct;
    }
}