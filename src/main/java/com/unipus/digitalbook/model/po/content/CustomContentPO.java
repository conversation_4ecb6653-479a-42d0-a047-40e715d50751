package com.unipus.digitalbook.model.po.content;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.model.entity.content.CustomContent;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25 10:18
 */
/**
 * 自建内容信息表
 */
public class CustomContentPO {
    /**
    * 主键ID
    */
    private Long id;

    /**
    * 内容业务ID
    */
    private String bizId;

    /**
    * 内容类型  1：自定义章节/2：自定义段落
    */
    private Integer type;

    /**
    * 内容状态  0：编写中/1：待发布/2：已发布
    */
    private Integer status;

    /**
    * 内容名称
    */
    private String name;

    /**
    * 内容
    */
    private String content;

    /**
    * 学生内容
    */
    private String studentContent;

    /**
    * 头图地址
    */
    private String headerImg;

    /**
    * 目录结构
    */
    private String catalog;

    /**
    * 整体结构
    */
    private List<CustomContentNodePO> totalStruct;

    /**
    * 资源信息
    */
    private String resource;

    /**
    * 租户ID
    */
    private Long tenantId;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 最后更新时间
    */
    private Date updateTime;

    /**
    * 创建者ID
    */
    private Long createBy;

    /**
    * 最后更新者ID
    */
    private Long updateBy;

    /**
    * 是否有效 0-无效 1-有效
    */
    private Boolean enable;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getStudentContent() {
        return studentContent;
    }

    public void setStudentContent(String studentContent) {
        this.studentContent = studentContent;
    }

    public String getHeaderImg() {
        return headerImg;
    }

    public void setHeaderImg(String headerImg) {
        this.headerImg = headerImg;
    }

    public String getCatalog() {
        return catalog;
    }

    public void setCatalog(String catalog) {
        this.catalog = catalog;
    }

    public List<CustomContentNodePO> getTotalStruct() {
        return totalStruct;
    }

    public void setTotalStruct(List<CustomContentNodePO> totalStruct) {
        this.totalStruct = totalStruct;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * 默认构造方法
     */
    public CustomContentPO() {
    }

    /**
     * 从CustomContent实体构造PO对象
     */
    public CustomContentPO(CustomContent customContent) {
        this.id = customContent.getId();
        this.bizId = customContent.getBizId();
        this.type = customContent.getType();
        this.status = customContent.getStatus();
        this.name = customContent.getName();
        this.content = customContent.getContent();
        this.studentContent = customContent.getStudentContent();
        this.headerImg = customContent.getHeaderImg();
        // 转换目录节点列表为JSON字符串（参考ChapterVersionPO的catalogJson处理）
        if (customContent.getCatalogNodeList() != null) {
            this.catalog = JSON.toJSONString(customContent.getCatalogNodeList());
        }
        // 转换整体结构节点列表为PO列表（参考ChapterVersionPO的totalStruct处理）
        if (customContent.getTotalStructNodeList() != null) {
            this.totalStruct = customContent.getTotalStructNodeList().stream()
                    .map(CustomContentNodePO::new)
                    .toList();
        }
        this.resource = customContent.getResource();
        this.tenantId = customContent.getTenantId();
        this.createTime = customContent.getCreateTime();
        this.updateTime = customContent.getUpdateTime();
        this.createBy = customContent.getCreateBy();
        this.updateBy = customContent.getUpdateBy();
        this.enable = customContent.getEnable();
    }

    /**
     * 转换为CustomContent实体对象
     */
    public CustomContent toEntity() {
        CustomContent customContent = new CustomContent();
        customContent.setId(this.id);
        customContent.setBizId(this.bizId);
        customContent.setType(this.type);
        customContent.setStatus(this.status);
        customContent.setName(this.name);
        customContent.setContent(this.content);
        customContent.setStudentContent(this.studentContent);
        customContent.setHeaderImg(this.headerImg);
        customContent.setResource(this.resource);
        customContent.setTenantId(this.tenantId);
        customContent.setCreateTime(this.createTime);
        customContent.setUpdateTime(this.updateTime);
        customContent.setCreateBy(this.createBy);
        customContent.setUpdateBy(this.updateBy);
        customContent.setEnable(this.enable);
        // 转换目录节点列表（从JSON字符串解析，参考ChapterVersionPO的catalog处理）
        // 区分null和空字符串：null时保持null，非null时进行解析（包括空字符串）
        if (this.catalog != null) {
            customContent.fillCatalogNodeList(this.catalog);
        }
        // 转换整体结构节点列表（从PO列表转换，参考ChapterVersionPO的totalStruct处理）
        // 区分null和空集合：null时保持null，非null时转换（包括空集合）
        if (this.totalStruct != null) {
            customContent.setTotalStructNodeList(this.totalStruct.stream()
                    .map(CustomContentNodePO::toEntity)
                    .toList());
        }
        return customContent;
    }
}