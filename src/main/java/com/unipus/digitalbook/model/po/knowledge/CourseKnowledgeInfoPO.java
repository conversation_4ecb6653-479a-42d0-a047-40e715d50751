package com.unipus.digitalbook.model.po.knowledge;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName course_knowledge_info
 */
@Data
public class CourseKnowledgeInfoPO implements Serializable {
    /**
     *
     */
    private Long id;

    /**
     * 课程Id 例如：course-v2:Unipus+1234Bb+20220729
     */
    private String courseIdStr;

    /**
     * 课程Id
     */
    private Long courseId;

    /**
     * 图谱名称
     */
    private String name;

    /**
     * 图谱描述
     */
    private String description;

    /**
     * 图谱Id 云知声返回的图谱Id
     */
    private String knowledgeId;

    /**
     * 知识标签集 【知识标签1】
     */
    private String knoweldgeTag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否删除 0-未删除 1-删除
     */
    private Integer deleteStatus;

    private static final long serialVersionUID = 1L;
}