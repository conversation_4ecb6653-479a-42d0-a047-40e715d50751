package com.unipus.digitalbook.model.po.knowledge;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName course_knowledge_source_info
 */
@Data
public class CourseKnowledgeSourceInfoPO implements Serializable {
    /**
     *
     */
    private Long id;

    private Long parentId;

    /**
     * 课程Id 例如：course-v2:Unipus+1234Bb+20220729
     */
    private String courseIdStr;

    /**
     * 课程Id
     */
    private Long courseId;

    /**
     * 单元Id
     */
    private String unitId;

    /**
     * 任务Id
     */
    private String taskId;

    /**
     * 目录信息
     */
    private String dir;

    /**
     * 资源类型 1 文字段落 2 视频 3 题目 4 音频
     */
    private Integer type;

    /**
     * 视频开始时间
     */
    private Integer startTime;
    /**
     * 视频开始的帧图
     */
    private String startPictureUrl;

    /**
     * 视频结束时间
     */
    private Integer endTime;

    /**
     * 课程图谱Id
     */
    private Long courseKnowledgeId;

    /**
     * 多媒体文件key
     */
    private String multimediaKey;

    /**
     * 多媒体文件索引
     */
    private String multimediaIndex;

    /**
     * 多媒体文件名称
     */
    private String multimediaName;

    /**
     * 资源url
     */
    private String sourceUrl;

    /**
     * 资源urlhash
     */
    private String sourceHash;

    /**
     * 是否组 0-非组 1-组
     */
    private Integer groupStatus;

    /**
     * 是否主资源 0-非主资源 1-主资源
     */
    private Integer mainStatus;

    /**
     * 三方图谱Id
     */
    private String knowledgeId;

    /**
     * 三方子图Id
     */
    private String graphId;

    /**
     * 三方子图某个节点Id
     */
    private String graphNodeId;

    /**
     * 图谱资源Id 云知声返回的图谱资源Id
     */
    private String knowledgeSourceId;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer enableStatus;

    /**
     * 是否删除 0-未删除 1-删除
     */
    private Integer deleteStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 当前打标内容在章节的绝对地址
     */
    private Integer location;

    private static final long serialVersionUID = 1L;
}