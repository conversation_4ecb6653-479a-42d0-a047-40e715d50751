package com.unipus.digitalbook.model.po.paper;

import com.unipus.digitalbook.model.entity.paper.PaperReference;

import java.util.Date;

/**
 * 题组关联表
 * @TableName paper_chapter_reference
 */
public class PaperChapterReferencePO {
    // 主键ID
    private Long id;
    // 题组ID
    private String paperId;
    // 教材ID
    private String bookId;
    // 章节ID
    private String chapterId;
    // 试卷在章节中的引用位置
    private String position;
    // 创建时间
    private Date createTime;
    // 更新时间
    private Date updateTime;
    // 创建人ID
    private Long createBy;
    // 更新人ID
    private Long updateBy;
    // 是否启用
    private Boolean enable;
    // 试卷名称
    private String paperName;
    // 试卷版本ID
    private Long paperPrimaryId;

    public PaperChapterReferencePO() {}

    public PaperChapterReferencePO(PaperReference paperReference, Long currentUserId) {
        this.paperId = paperReference.getPaperId();
        this.bookId = paperReference.getBookId();
        this.chapterId = paperReference.getChapterId();
        this.position = paperReference.getPosition();
        this.createBy = currentUserId;
        this.updateBy = currentUserId;
        this.enable = Boolean.TRUE;
    }

    public PaperReference toEntity(){
        PaperReference paperReference = new PaperReference();
        paperReference.setId(this.id);
        paperReference.setPaperId(this.paperId);
        paperReference.setBookId(this.bookId);
        paperReference.setChapterId(this.chapterId);
        paperReference.setPosition(this.position);
        paperReference.setCreateTime(this.createTime);
        paperReference.setPaperName(this.paperName);
        paperReference.setPaperPrimaryId(this.paperPrimaryId);
        return paperReference;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public Long getPaperPrimaryId() {
        return paperPrimaryId;
    }

    public void setPaperPrimaryId(Long paperPrimaryId) {
        this.paperPrimaryId = paperPrimaryId;
    }
}
