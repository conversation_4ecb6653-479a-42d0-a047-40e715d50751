package com.unipus.digitalbook.model.po.paper;

import java.util.Date;

/**
 * 试卷章节引用版本实体类
 * @TableName paper_chapter_reference_version
 */
public class PaperChapterReferenceVersionPO {
    // 关系 ID
    private Long id;
    // 试卷 ID
    private String paperId;
    // 教材 ID
    private String bookId;
    // 章节 ID
    private String chapterId;
    // 章节版本 ID
    private Long chapterVersionId;
    // 试卷在章节中插入的引用位置
    private String position;
    // 创建时间
    private Date createTime;
    // 最后更新时间
    private Date updateTime;
    // 创建者ID
    private Long createBy;
    // 最后更新者ID
    private Long updateBy;
    // 是否有效 0-无效 1-有效
    private Boolean enable;

    public PaperChapterReferenceVersionPO() {}

    // 有参构造函数
    public PaperChapterReferenceVersionPO(String paperId, String bookId, String chapterId, Long chapterVersionId, String position, Long userId) {
        this.paperId = paperId;
        this.bookId = bookId;
        this.chapterId = chapterId;
        this.chapterVersionId = chapterVersionId;
        this.position = position;
        this.createBy = userId;
        this.updateBy = userId;
        this.enable = Boolean.TRUE;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public Long getChapterVersionId() {
        return chapterVersionId;
    }

    public void setChapterVersionId(Long chapterVersionId) {
        this.chapterVersionId = chapterVersionId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}
