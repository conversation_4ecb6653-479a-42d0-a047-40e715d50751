package com.unipus.digitalbook.model.po.paper;

import com.unipus.digitalbook.model.entity.paper.Paper;

import java.util.Date;

/**
 * 试卷扩展实体类
 */
public class PaperExtendPO {
    // 主键ID
    private Long id;
    // 试卷主键ID
    private Long paperVersionId;
    // 试卷ID
    private String paperId;
    // 试卷名称
    private String paperName;
    // 试卷内容
    private String paperContent;
    // 试卷描述
    private String paperDescription;
    // 版本号
    private String versionNumber;
    // 创建时间
    private Date createTime;
    // 更新时间
    private Date updateTime;
    // 创建人ID
    private Long createBy;
    // 更新人ID
    private Long updateBy;
    // 是否启用
    private Boolean enable;

    public PaperExtendPO() {
    }

    public PaperExtendPO(Paper paper, Long userId) {
        this.paperVersionId = paper.getId();
        this.paperId = paper.getPaperId();
        this.paperName = paper.getPaperName();
        this.paperContent = paper.getContent();
        this.paperDescription = paper.getDescription();
        this.versionNumber = paper.getVersionNumber();
        this.createBy = userId;
        this.updateBy = userId;
        this.enable = paper.getEnable();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPaperVersionId() {
        return paperVersionId;
    }

    public void setPaperVersionId(Long paperVersionId) {
        this.paperVersionId = paperVersionId;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public String getPaperContent() {
        return paperContent;
    }

    public void setPaperContent(String paperContent) {
        this.paperContent = paperContent;
    }

    public String getPaperDescription() {
        return paperDescription;
    }

    public void setPaperDescription(String paperDescription) {
        this.paperDescription = paperDescription;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}