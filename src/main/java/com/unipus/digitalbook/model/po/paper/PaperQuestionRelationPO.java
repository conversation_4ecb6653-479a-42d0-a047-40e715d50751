package com.unipus.digitalbook.model.po.paper;

import com.unipus.digitalbook.model.entity.paper.QuestionTag;
import org.springframework.util.StringUtils;

import java.util.Date;

/**
 * 诊断卷推荐提关系PO
 * @TableName paper_question_relation
 */
public class PaperQuestionRelationPO {
    // 主键ID
    private Long id;
    // 试卷ID
    private String paperId;
    // 基础题目ID
    private String bizQuestionIdBase;
    // 目标题目ID
    private String bizQuestionIdTarget;
    // 试卷版本号
    private String paperVersionNumber;
    // 创建时间
    private Date createTime;
    // 更新时间
    private Date updateTime;
    // 创建人ID
    private Long createBy;
    // 更新人ID
    private Long updateBy;
    // 是否启用
    private Boolean enable;

    public PaperQuestionRelationPO() {}

    public PaperQuestionRelationPO(QuestionTag questionTag, String paperId, String versionNumber, Long userId) {
        this.paperId = paperId;
        this.bizQuestionIdBase = questionTag.getBaseResourceId();
        this.bizQuestionIdTarget = StringUtils.hasText(questionTag.getTargetResourceId()) ? questionTag.getTargetResourceId() : "";
        this.paperVersionNumber = versionNumber;
        this.createBy = userId;
        this.updateBy = userId;
        this.enable = true;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPaperId() {
        return paperId;
    }

    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    public String getBizQuestionIdBase() {
        return bizQuestionIdBase;
    }

    public void setBizQuestionIdBase(String bizQuestionIdBase) {
        this.bizQuestionIdBase = bizQuestionIdBase;
    }

    public String getBizQuestionIdTarget() {
        return bizQuestionIdTarget;
    }

    public void setBizQuestionIdTarget(String bizQuestionIdTarget) {
        this.bizQuestionIdTarget = bizQuestionIdTarget;
    }

    public String getPaperVersionNumber() {
        return paperVersionNumber;
    }

    public void setPaperVersionNumber(String paperVersionNumber) {
        this.paperVersionNumber = paperVersionNumber;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}