package com.unipus.digitalbook.model.po.paper;

import com.unipus.digitalbook.model.entity.paper.QuestionBank;

import java.util.Date;

/**
 * 问题库策略实体类，用于存储问题库的相关策略信息。
 * @TableName question_bank_strategy
 */
public class QuestionBankStrategyPO {
    // 主键ID
    private Long id;
    // 问题库ID
    private String bankId;
    // 问题库名称
    private String bankName;
    // 每轮问题数量
    private Integer questionsPerRound;
    // 每题分数
    private Integer questionScore;
    // 版本号
    private String versionNumber;
    // 创建时间
    private Date createTime;
    // 更新时间
    private Date updateTime;
    // 创建人ID
    private Long createBy;
    // 更新人ID
    private Long updateBy;
    // 是否启用
    private Boolean enable;

    public QuestionBankStrategyPO() {
    }

    public QuestionBankStrategyPO(QuestionBank questionBank, Long userId) {
        this.id = questionBank.getId();
        this.bankId = questionBank.getBankId();
        this.bankName = questionBank.getBankName();
        this.questionsPerRound = questionBank.getQuestionsPerRound();
        this.questionScore = questionBank.getQuestionScore();
        this.versionNumber = questionBank.getVersionNumber();
        this.createBy = userId;
        this.updateBy = userId;
    }

    public QuestionBank toBankEntity() {
        QuestionBank questionBank = new QuestionBank();
        questionBank.setId(this.id);
        questionBank.setBankId(this.bankId);
        questionBank.setBankName(this.bankName);
        questionBank.setQuestionsPerRound(this.questionsPerRound);
        questionBank.setQuestionScore(this.questionScore);
        questionBank.setVersionNumber(this.versionNumber);
        return questionBank;
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBankId() {
        return bankId;
    }

    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public Integer getQuestionsPerRound() {
        return questionsPerRound;
    }

    public void setQuestionsPerRound(Integer questionsPerRound) {
        this.questionsPerRound = questionsPerRound;
    }

    public Integer getQuestionScore() {
        return questionScore;
    }

    public void setQuestionScore(Integer questionScore) {
        this.questionScore = questionScore;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

}
