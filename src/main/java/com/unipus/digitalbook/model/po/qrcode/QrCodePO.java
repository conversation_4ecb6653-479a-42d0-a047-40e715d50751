package com.unipus.digitalbook.model.po.qrcode;

import com.unipus.digitalbook.model.entity.qrcode.QrCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.StringJoiner;

/**
 * 教材二维码表
 * 表：qr_code
 *
 * <AUTHOR>
 * @date 2025年02月21日 11:26:57
 */
public class QrCodePO implements Serializable {
    /**
     * 二维码ID
     */
    private Long id;

    /**
     * 二维码短链地址
     */
    private String qrCodeUrl;

    /**
     * 二维码尺寸
     */
    private String qrCodeSize;

    /**
     * 实体教材位置
     */
    private String realBookLocation;

    /**
     * 教材ID
     */
    private String bookId;

    /**
     * 教材内部链接
     */
    private String bookInnerUrl;

    /**
     * 链接验证状态
     */
    private Boolean linkVerificationStatus;

    /**
     * 链接验证时间
     */
    private Date linkVerificationTime;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    @Serial
    private static final long serialVersionUID = 1L;

    public QrCodePO() {
    }

    public QrCodePO(QrCode qrCode) {
        if (qrCode == null) {
            return;
        }
        this.id = qrCode.getId();
        this.qrCodeUrl = qrCode.getQrCodeUrl();
        this.qrCodeSize = qrCode.getQrCodeSize();
        this.realBookLocation = qrCode.getRealBookLocation();
        this.bookId = qrCode.getBookId();
        this.bookInnerUrl = qrCode.getBookInnerUrl();
        this.linkVerificationStatus = qrCode.getLinkVerificationStatus();
        this.linkVerificationTime = qrCode.getLinkVerificationTime();
        this.remarks = qrCode.getRemarks();
        this.createTime = qrCode.getCreateTime();
        this.updateTime = qrCode.getUpdateTime();
        this.createBy = qrCode.getCreateBy();
        this.updateBy = qrCode.getUpdateBy();
        this.enable = qrCode.getEnable();
    }

    public QrCode toEntity() {
        QrCode qrCode = new QrCode();
        qrCode.setId(this.id);
        qrCode.setQrCodeUrl(this.qrCodeUrl);
        qrCode.setQrCodeSize(this.qrCodeSize);
        qrCode.setRealBookLocation(this.realBookLocation);
        qrCode.setBookId(this.bookId);
        qrCode.setBookInnerUrl(this.bookInnerUrl);
        qrCode.setLinkVerificationStatus(this.linkVerificationStatus);
        qrCode.setLinkVerificationTime(this.linkVerificationTime);
        qrCode.setRemarks(this.remarks);
        qrCode.setCreateTime(this.createTime);
        qrCode.setUpdateTime(this.updateTime);
        qrCode.setCreateBy(this.createBy);
        qrCode.setUpdateBy(this.updateBy);
        qrCode.setEnable(this.enable);
        return qrCode;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getQrCodeUrl() {
        return qrCodeUrl;
    }

    public void setQrCodeUrl(String qrCodeUrl) {
        this.qrCodeUrl = qrCodeUrl == null ? null : qrCodeUrl.trim();
    }

    public String getQrCodeSize() {
        return qrCodeSize;
    }

    public void setQrCodeSize(String qrCodeSize) {
        this.qrCodeSize = qrCodeSize == null ? null : qrCodeSize.trim();
    }

    public String getRealBookLocation() {
        return realBookLocation;
    }

    public void setRealBookLocation(String realBookLocation) {
        this.realBookLocation = realBookLocation == null ? null : realBookLocation.trim();
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId == null ? null : bookId.trim();
    }

    public String getBookInnerUrl() {
        return bookInnerUrl;
    }

    public void setBookInnerUrl(String bookInnerUrl) {
        this.bookInnerUrl = bookInnerUrl == null ? null : bookInnerUrl.trim();
    }

    public Boolean getLinkVerificationStatus() {
        return linkVerificationStatus;
    }

    public void setLinkVerificationStatus(Boolean linkVerificationStatus) {
        this.linkVerificationStatus = linkVerificationStatus;
    }

    public Date getLinkVerificationTime() {
        return linkVerificationTime;
    }

    public void setLinkVerificationTime(Date linkVerificationTime) {
        this.linkVerificationTime = linkVerificationTime;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks == null ? null : remarks.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    /**
     * 转字符
     *
     * @return String
     */
    @Override
    public String toString() {
        return new StringJoiner(", ", QrCodePO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("qrCodeUrl='" + qrCodeUrl + "'")
                .add("qrCodeSize='" + qrCodeSize + "'")
                .add("realBookLocation='" + realBookLocation + "'")
                .add("bookId='" + bookId + "'")
                .add("bookInnerUrl='" + bookInnerUrl + "'")
                .add("linkVerificationStatus=" + linkVerificationStatus)
                .add("linkVerificationTime=" + linkVerificationTime)
                .add("remarks='" + remarks + "'")
                .add("createTime=" + createTime)
                .add("updateTime=" + updateTime)
                .add("createBy=" + createBy)
                .add("updateBy=" + updateBy)
                .add("enable=" + enable)
                .toString();
    }
}