package com.unipus.digitalbook.model.po.question;

import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionText;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * @TableName question
 */
public class QuestionPO implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 业务题ID
     */
    private String bizQuestionId;

    /**
     * 版本
     */
    private String versionNumber;

    /**
     * 题组主键ID
     */
    private Long groupId;

    /**
     * 题目类型 单选、多选
     */
    private Integer questionType;

    /**
     * 题干
     */
    private String questionText;

    /**
     * 是否判题
     */
    private Boolean isJudgment;

    /**
     * 是否记分
     */
    private Boolean isScoring;
    /**
     * 分数
     */
    private BigDecimal score;

    /**
     * 题目顺序
     */
    private Integer sortOrder;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    /**
     * 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     */
    public String getBizQuestionId() {
        return bizQuestionId;
    }

    /**
     *
     */
    public void setBizQuestionId(String bizQuestionId) {
        this.bizQuestionId = bizQuestionId;
    }

    /**
     *
     */
    public String getVersionNumber() {
        return versionNumber;
    }

    /**
     *
     */
    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    /**
     * 题组主键ID
     */
    public Long getGroupId() {
        return groupId;
    }

    /**
     * 题组主键ID
     */
    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    /**
     * 题目类型 单选、多选
     */
    public Integer getQuestionType() {
        return questionType;
    }

    /**
     * 题目类型 单选、多选
     */
    public void setQuestionType(Integer questionType) {
        this.questionType = questionType;
    }

    /**
     * 分数
     */
    public BigDecimal getScore() {
        return score;
    }

    /**
     * 分数
     */
    public void setScore(BigDecimal score) {
        this.score = score;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 最后更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建者ID
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 创建者ID
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 最后更新者ID
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 最后更新者ID
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getQuestionText() {
        return questionText;
    }

    public void setQuestionText(String questionText) {
        this.questionText = questionText;
    }

    public Boolean getIsJudgment() {
        return isJudgment;
    }

    public void setIsJudgment(Boolean judgment) {
        isJudgment = judgment;
    }

    public Boolean getIsScoring() {
        return isScoring;
    }

    public void setIsScoring(Boolean scoring) {
        isScoring = scoring;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public QuestionPO(){}
    /**
     * 将Question实体转换为QuestionPO
     *
     * @param question Question实体
     */
    public QuestionPO(Question question, Long groupId) {
        if (question == null) {
            throw new IllegalArgumentException("question is null");
        }
        this.setBizQuestionId(question.getBizQuestionId());
        this.setEnable(question.getEnable());
        this.setVersionNumber(question.getVersionNumber());
        this.setQuestionType(question.getQuestionType());
        this.setGroupId(groupId);
        this.setScore(question.getScore());
        this.setIsJudgment(question.getIsJudgment());
        this.setIsScoring(question.getIsScoring());
        Optional.ofNullable(question.getQuestionText())
                .ifPresent(qText ->
                        this.setQuestionText(qText.serialize()));
        this.setSortOrder(question.getSortOrder());
        this.setCreateBy(question.getCreateBy());
        this.setUpdateBy(question.getUpdateBy());
    }

    public Question toEntity(Supplier<Question> supplier) {
        Question question = supplier.get();
        question.setId(this.getId());
        question.setBizQuestionId(this.getBizQuestionId());
        question.setVersionNumber(this.getVersionNumber());
        question.setQuestionType(this.getQuestionType());
        question.setScore(this.getScore());
        question.setIsJudgment(this.getIsJudgment());
        question.setIsScoring(this.getIsScoring());
        question.setQuestionText(QuestionText.deserialize(this.getQuestionText()));
        question.setEnable(this.getEnable());
        question.setCreateBy(this.getCreateBy());
        question.setUpdateBy(this.getUpdateBy());
        question.setCreateTime(this.getCreateTime());
        question.setUpdateTime(this.getUpdateTime());
        question.setSortOrder(this.getSortOrder());
        return question;
    }
}