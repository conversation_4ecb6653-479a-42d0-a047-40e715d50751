package com.unipus.digitalbook.model.po.question;

import com.unipus.digitalbook.model.entity.question.UserAnswer;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @TableName user_answer
 */
public class UserAnswerPO implements Serializable {
    /**
     * 答案ID
     */
    private Long id;

    /**
     * 题目业务ID
     */
    private String bizQuestionId;

    /**
     * 题目版本
     */
    private String questionVersionNumber;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 环境分区
     */
    private String envPartition;

    /**
     * 用户OpenId
     */
    private String openId;

    /**
     * 得分
     */
    private BigDecimal score;

    /**
     * 作答内容
     */
    private String answer;

    /**
     * 作答业务ID
     */
    private String bizAnswerId;

    /**
     * 评测结果
     */
    private String evaluation;

    /**
     * 作答批次
     */
    private String batchId;

    /**
     * 状态(0:未判分,1:错误,2:正确,3:半对)
     */
    private Integer status;

    /**
     * 是否通过
     */
    private Boolean pass;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    public UserAnswerPO() {
    }

    /**
     * 将UserAnswer实体转换为PO
     *
     * @param userAnswer UserAnswer实体
     */
    public UserAnswerPO(UserAnswer userAnswer) {
        if (userAnswer == null) {
            return;
        }
        this.id = userAnswer.getId();
        this.bizQuestionId = userAnswer.getBizQuestionId();
        this.questionVersionNumber = userAnswer.getQuestionVersionNumber();
        this.openId = userAnswer.getOpenId();
        this.tenantId = userAnswer.getTenantId();
        this.envPartition = userAnswer.getEnvPartition();
        this.score = userAnswer.getScore();
        this.answer = userAnswer.getAnswer();
        this.bizAnswerId = userAnswer.getBizAnswerId();
        this.evaluation = userAnswer.getEvaluation();
        this.batchId = userAnswer.getBatchId();
        this.status = userAnswer.getStatus();
        this.pass = userAnswer.getPass() != null && userAnswer.getPass();
        this.createTime = userAnswer.getCreateTime();
        this.updateTime = userAnswer.getUpdateTime();
        this.enable = userAnswer.getEnable() == null || userAnswer.getEnable();
    }

    /**
     * 将PO转换为UserAnswer实体
     *
     * @return UserAnswer实体
     */
    public UserAnswer toEntity() {
        UserAnswer answer = new UserAnswer();
        answer.setId(this.id);
        answer.setBizQuestionId(this.bizQuestionId);
        answer.setQuestionVersionNumber(this.questionVersionNumber);
        answer.setOpenId(this.openId);
        answer.setTenantId(this.tenantId);
        answer.setScore(this.score);
        answer.setAnswer(this.answer);
        answer.setBizAnswerId(this.bizAnswerId);
        answer.setEvaluation(this.evaluation);
        answer.setBatchId(this.batchId);
        answer.setStatus(this.status);
        answer.setCreateTime(this.createTime);
        answer.setUpdateTime(this.updateTime);
        answer.setEnable(this.enable);
        return answer;
    }

    public static List<UserAnswerPO> toPOList(List<UserAnswer> userAnswers) {
        // 批次一致性检查
        boolean hasEmptyBatchId = userAnswers.stream()
                .map(UserAnswer::getBatchId)
                .anyMatch(batch -> !StringUtils.hasText(batch));
        if (hasEmptyBatchId) {
            throw new IllegalArgumentException("用户作答批次不能为空");
        }
        String batchNo = userAnswers.getFirst().getBatchId();
        boolean hasDifferentBatchId = userAnswers.stream()
                .skip(1)
                .map(UserAnswer::getBatchId)
                .anyMatch(batchId -> !batchNo.equals(batchId));
        if (hasDifferentBatchId) {
            throw new IllegalArgumentException("用户作答批次不一致");
        }
        // 按照bizQuestionId和batch作为唯一标识，进行批量新增或者更新
        return userAnswers.stream()
                .map(UserAnswerPO::new)
                .toList();
    }

    /**
     * 答案ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 答案ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 题目业务ID
     */
    public String getBizQuestionId() {
        return bizQuestionId;
    }

    /**
     * 题目业务ID
     */
    public void setBizQuestionId(String bizQuestionId) {
        this.bizQuestionId = bizQuestionId;
    }

    /**
     * 题目版本
     */
    public String getQuestionVersionNumber() {
        return questionVersionNumber;
    }

    /**
     * 题目版本
     */
    public void setQuestionVersionNumber(String questionVersionNumber) {
        this.questionVersionNumber = questionVersionNumber;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    /**
     * 得分
     */
    public BigDecimal getScore() {
        return score;
    }

    /**
     * 得分
     */
    public void setScore(BigDecimal score) {
        this.score = score;
    }

    /**
     * 作答内容
     */
    public String getAnswer() {
        return answer;
    }

    /**
     * 作答内容
     */
    public void setAnswer(String answer) {
        this.answer = answer;
    }

    /**
     * 作答业务ID
     */
    public String getBizAnswerId() {
        return bizAnswerId;
    }

    /**
     * 作答业务ID
     */
    public void setBizAnswerId(String bizAnswerId) {
        this.bizAnswerId = bizAnswerId;
    }

    public String getEvaluation() {
        return evaluation;
    }

    public void setEvaluation(String evaluation) {
        this.evaluation = evaluation;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 最后更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getPass() {
        return pass;
    }

    public void setPass(Boolean pass) {
        this.pass = pass;
    }

    public String getEnvPartition() {
        return envPartition;
    }

    public void setEnvPartition(String envPartition) {
        this.envPartition = envPartition;
    }
}