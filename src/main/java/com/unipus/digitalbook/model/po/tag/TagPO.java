package com.unipus.digitalbook.model.po.tag;

import com.unipus.digitalbook.model.entity.tag.Tag;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Date;

public class TagPO {
    private Long id;
    private Long parentId;
    private Integer tagType;
    private String tagName;
    private String fullPathId;
    private Date createTime;
    private Date updateTime;
    private Long createBy;
    private Long updateBy;
    private Boolean enable;
    private Integer level;

    public TagPO() {}

    public TagPO(Tag tag, Long parentId, Long userId) {
        this.parentId = parentId;
        this.tagType = tag.getTagType();
        this.tagName = tag.getTagName();
        this.createBy = userId;
        this.updateBy = userId;
    }

    public TagPO(Tag tag, TagPO parentTag, Long userId) {
        this.parentId = tag.getParentId();
        this.tagType = tag.getTagType();
        this.tagName = tag.getTagName();
        if(parentTag != null) {
            if (StringUtils.hasText(parentTag.getFullPathId())) {
                this.fullPathId = parentTag.getFullPathId() + "," + parentTag.getId();
            } else {
                this.fullPathId = String.valueOf(parentTag.getId());
            }
        }
        this.createBy = userId;
        this.updateBy = userId;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getTagType() {
        return tagType;
    }

    public void setTagType(Integer tagType) {
        this.tagType = tagType;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getFullPathId() {
        return fullPathId;
    }

    public void setFullPathId(String fullPathId) {
        this.fullPathId = fullPathId;
    }

    public Tag toEntity(){
        Tag tag = new Tag();
        tag.setTagId(this.getId());
        tag.setParentId(this.getParentId());
        tag.setTagType(this.getTagType());
        tag.setTagName(this.getTagName());
        tag.setLevel(this.getLevel());
        if (StringUtils.hasText(this.getFullPathId())) {
            tag.setFullPathIdList(Arrays.stream(this.getFullPathId().split(",")).map(Long::parseLong).toList());
        }
        if(tag.getLevel()==null){
            tag.setLevel(tag.getFullPathIdList()==null?0:tag.getFullPathIdList().size());
        }
        return tag;
    }
}