package com.unipus.digitalbook.model.po.template;

import com.unipus.digitalbook.model.entity.template.PaperScoreTemplate;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplateRelation;
import lombok.Data;

import java.util.Date;

@Data
public class PaperScoreTemplateRelationPO {
    private Long id;

    private String bookId;

    private Long paperScoreTemplateId;

    private Date createTime;

    private Date updateTime;

    private Long createBy;

    private Long updateBy;

    private Boolean enable;

    public void fromEntity(PaperScoreTemplateRelation paperScoreTemplateRelation) {
        this.setBookId(paperScoreTemplateRelation.getBookId());
        this.setPaperScoreTemplateId(paperScoreTemplateRelation.getPaperScoreTemplateId());
    }
}