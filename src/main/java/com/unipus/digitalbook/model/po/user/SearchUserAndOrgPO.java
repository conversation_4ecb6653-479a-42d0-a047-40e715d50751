package com.unipus.digitalbook.model.po.user;

import com.unipus.digitalbook.model.entity.user.SearchUser;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.StringJoiner;

/**
 * 表：user_info
 *
 * <AUTHOR>
 * @date 2024年11月27日 11:13:07
 */
public class SearchUserAndOrgPO implements Serializable {
    /**
     * 数据库字段： user_info.id
     */
    private Long id;

    /**
     * sso ID
     * <p>
     * 数据库字段： user_info.sso_id
     */
    private String ssoId;

    /**
     * 姓名
     * <p>
     * 数据库字段： user_info.name
     */
    private String name;

    /**
     * 手机号
     * <p>
     * 数据库字段： user_info.cell_phone
     */
    private String cellPhone;

    /**
     * 邮箱地址
     * <p>
     * 数据库字段： user_info.email
     */
    private String email;

    /**
     * 性别
     * 0:女 1:男 2:未知
     * <p>
     * 数据库字段： user_info.gender
     */
    private Integer gender;

    /**
     * 简介
     * <p>
     * 数据库字段： user_info.desc
     */
    private String desc;

    /**
     * 头像地址
     * <p>
     * 数据库字段： user_info.avatar_url
     */
    private String avatarUrl;

    /**
     * 创建时间
     * <p>
     * 数据库字段： user_info.create_time
     */
    private Date createTime;

    /**
     * 激活时间
     * <p>
     * 数据库字段： user_info.active_time
     */
    private Date activeTime;

    /**
     * 状态
     * 0:未激活 1:已激活
     */
    private Integer status;

    /**
     * 机构ID
     * <p>
     * 数据库字段： org_user_relation.org_id
     */
    private Long orgId;

    public SearchUser toEntity() {
        SearchUser searchUser = new SearchUser();
        searchUser.setId(this.getId());
        searchUser.setName(this.getName());
        searchUser.setCellPhone(this.getCellPhone());
        searchUser.setStatus(this.getStatus());
        searchUser.setActiveTime(this.getActiveTime());
        searchUser.setCreateTime(this.getCreateTime());
        searchUser.setOrgId(this.getOrgId());
        return searchUser;
    }

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table user_info
     */
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取user_info.id
     *
     * @return user_info 的值.id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置user_info.id
     *
     * @param id the value for user_info.id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取user_info.sso_id
     *
     * @return user_info 的值.sso_id
     */
    public String getSsoId() {
        return ssoId;
    }

    /**
     * 设置user_info.sso_id
     *
     * @param ssoId the value for user_info.sso_id
     */
    public void setSsoId(String ssoId) {
        this.ssoId = ssoId == null ? null : ssoId.trim();
    }

    /**
     * 获取user_info.name
     *
     * @return user_info 的值.name
     */
    public String getName() {
        return name;
    }

    /**
     * 设置user_info.name
     *
     * @param name the value for user_info.name
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * 获取user_info.cell_phone
     *
     * @return user_info 的值.cell_phone
     */
    public String getCellPhone() {
        return cellPhone;
    }

    /**
     * 设置user_info.cell_phone
     *
     * @param cellPhone the value for user_info.cell_phone
     */
    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone == null ? null : cellPhone.trim();
    }

    /**
     * 获取user_info.email
     *
     * @return user_info 的值.email
     */
    public String getEmail() {
        return email;
    }

    /**
     * 设置user_info.email
     *
     * @param email the value for user_info.email
     */
    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    /**
     * 获取user_info.gender
     *
     * @return user_info 的值.gender
     */
    public Integer getGender() {
        return gender;
    }

    /**
     * 设置user_info.gender
     *
     * @param gender the value for user_info.gender
     */
    public void setGender(Integer gender) {
        this.gender = gender;
    }

    /**
     * 获取user_info.desc
     *
     * @return user_info 的值.desc
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 设置user_info.desc
     *
     * @param desc the value for user_info.desc
     */
    public void setDesc(String desc) {
        this.desc = desc == null ? null : desc.trim();
    }

    /**
     * 获取user_info.avatar_url
     *
     * @return user_info 的值.avatar_url
     */
    public String getAvatarUrl() {
        return avatarUrl;
    }

    /**
     * 设置user_info.avatar_url
     *
     * @param avatarUrl the value for user_info.avatar_url
     */
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl == null ? null : avatarUrl.trim();
    }

    /**
     * 获取user_info.create_time
     *
     * @return user_info 的值.create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置user_info.create_time
     *
     * @param createTime the value for user_info.create_time
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取user_info.active_time
     *
     * @return user_info 的值.active_time
     */
    public Date getActiveTime() {
        return activeTime;
    }

    /**
     * 设置user_info.active_time
     *
     * @param activeTime the value for user_info.active_time
     */
    public void setActiveTime(Date activeTime) {
        this.activeTime = activeTime;
    }

    /**
     * 获取user_info.status
     *
     * @return user_info 的值.status
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置user_info.status
     *
     * @param status the value for user_info.status
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取org_user_relation.org_id
     *
     * @return org_user_relation 的值.org_id
     */
    public Long getOrgId() {
        return orgId;
    }

    /**
     * 设置org_user_relation.org_id
     *
     * @param orgId the value for org_user_relation.org_id
     */
    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    /**
     * 转字符
     *
     * @return String
     */
    @Override
    public String toString() {
        return new StringJoiner(", ", SearchUserAndOrgPO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("ssoId='" + ssoId + "'")
                .add("name='" + name + "'")
                .add("cellPhone='" + cellPhone + "'")
                .add("email='" + email + "'")
                .add("gender=" + gender)
                .add("desc='" + desc + "'")
                .add("avatarUrl='" + avatarUrl + "'")
                .add("createTime=" + createTime)
                .add("activeTime=" + activeTime)
                .add("status=" + status)
                .add("orgId=" + orgId)
                .toString();
    }
}