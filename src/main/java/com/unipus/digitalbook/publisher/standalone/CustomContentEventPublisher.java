package com.unipus.digitalbook.publisher.standalone;

import com.unipus.digitalbook.model.enums.EventTypeEnum;
import com.unipus.digitalbook.model.events.CustomContentEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
public class CustomContentEventPublisher {
    private final ApplicationEventPublisher eventPublisher;

    public CustomContentEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    public void customContentEventPublisher(String bizId, EventTypeEnum eventType, Long opsUserId) {
        eventPublisher.publishEvent(new CustomContentEvent(this, bizId, eventType, opsUserId));
    }
}
