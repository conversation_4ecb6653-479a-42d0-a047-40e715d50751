package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.dto.book.BookSimpleDataDTO;
import com.unipus.digitalbook.model.entity.book.*;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.permission.ResourceUser;
import com.unipus.digitalbook.model.params.book.BookSearchParam;
import jakarta.validation.constraints.NotNull;

import java.util.List;
import java.util.Map;

/**
 * 教材相关业务
 *
 * <AUTHOR>
 */
public interface BookService {
    /**
     * 新增教材
     *
     * @return
     */
    String addBook(Book book, Long createBy);

    /**
     * 添加章节到教材
     *
     * @param chapter  章节对象，包含章节的详细信息
     * @param createBy 创建者ID，用于记录谁创建了这个章节
     * @return 返回新添加的章节的ID
     */
    String addChapter(Chapter chapter, Long createBy);

    /**
     * 编辑教材
     *
     * @return
     */
    boolean editBook(Book book);

    /**
     * 获取用户教材列表
     *
     * @param userId
     * @return
     */
    UserBookList getUserBooks(Long userId, BookSearchParam param, Long orgId, Integer topN);

    UserBookList getMyLastVersionBookList(Long userId, BookSearchParam param, Long orgId);


    /**
     * 获取用户预览列表
     *
     * @param userId
     * @return
     */
    UserBookList getUserPreviewBooks(Long userId, BookSearchParam param);


    /**
     * 根据教材id获取教材信息
     *
     * @param bookId
     * @return
     */
    Book getBookById(String bookId);

    /**
     * 根据教材id获取教材封面和简介信息
     *
     * @param bookId 教材ID，用于查询教材和简介信息
     * @return 返回一个Book对象
     */
    Book getBookCoverIntro(String bookId);

    /**
     * 编辑教材封面和简介信息
     *
     * @param userId 用户ID
     * @param param  教材封面和简介参数
     * @return 操作是否成功
     */
    Boolean editBookCoverIntro(Long userId, Book param);

    /**
     * 获取教材的协作者列表
     *
     * @param bookIds 教材ID列表
     * @return 返回一个Map，key为教材ID，value为协作者列表
     */
    Map<String, List<ResourceUser>> getBookCollaborators(List<String> bookIds, Long orgId);

    /**
     * 获取章节的协作者列表
     *
     * @param chapterIds 章节ID列表
     * @return 返回一个Map，key为教材ID，value为协作者列表
     */
    Map<String, List<ResourceUser>> getChapterCollaborators(List<String> chapterIds, Long orgId);


    /**
     * 根据章节编号对指定书籍的章节列表进行排序。
     *
     * @param bookId      书籍ID，用于验证章节列表中的每个章节是否属于该书籍。
     * @param chapterList 待排序的章节列表。
     * @param updateBy    执行更新操作的用户ID，用于记录最后更新者信息。
     * @return 如果所有章节都成功排序且属于给定的书籍，则返回true；否则返回false。
     */
    Boolean sortChapterListInBook(String bookId, List<Chapter> chapterList, Long updateBy);

    /**
     * 获取书籍的编者ID
     *
     * @param bookId
     * @return
     */
    Long getEditorId(String bookId);


    /**
     * 获取有编辑权限章节ID列表
     *
     * @param userId
     * @param bookId
     * @return
     */
    @NotNull
    List<String> getHasEditChapterIdsByBookId(Long userId, String bookId);

    /**
     * 根据教材名称获取教材列表
     *
     * @param bookName 教材名称
     * @return 教材列表
     */
    List<Book> getByBookName(String bookName);

    /**
     * 根据教材ID获取教材基本信息
     *
     * @param bookId
     * @return
     */
    BookBasic getBookBasicByBookId(String bookId);

    /**
     * 根据教材ID获取教材简介
     *
     * @param bookId
     * @return
     */
    BookBasic generateBookBasicVersion(String bookId);

    /**
     * 根据教材ID获取教材简介
     *
     * @param bookId
     * @return
     */
    BookIntro getBookIntroByBookId(String bookId);

    /**
     * 生成教材简介版本
     *
     * @param bookId
     * @return
     */
    BookIntro generateBookIntroVersion(String bookId);

    /**
     * 编辑教材版权信息
     *
     * @param bookCopyright
     * @return
     */
    boolean editBookCopyright(BookCopyright bookCopyright);

    /**
     * 根据教材ID获取教材版权信息
     *
     * @param bookId
     * @return
     */
    BookCopyright getBookCopyright(String bookId);

    /**
     * 根据简介ID，获取简介
     *
     * @param introId 简介ID
     * @return 简介
     */
    BookIntro getBookIntroById(Long introId);

    /**
     * 根据基本信息ID，获取基本信息
     *
     * @param basicInfoId 基础信息ID
     * @return 基本信息
     */
    BookBasic getBookBasicInfoById(Long basicInfoId);

    /**
     * 根据版权ID，获取版权信息
     *
     * @param copyrightId 版权ID
     * @return 版权信息
     */
    BookCopyright getBookCopyrightById(Long copyrightId);

    /**
     * 保存教材基本信息
     *
     * @param bookBasic 教材基本信息
     * @return 新教材基础信息的id
     */
    Long saveBookBasic(BookBasic bookBasic);

    /**
     * 保存教材简介
     *
     * @param bookIntro 教材简介
     * @return 新教材简介的id
     */
    Long saveBookIntro(BookIntro bookIntro);

    /**
     * 保存教材版权信息
     *
     * @param copyright 版权信息
     * @return 新版权信息的id
     */
    Long saveBookCopyright(BookCopyright copyright);

    /**
     * 根据教材ID和版本号，获取书籍信息(基本信息、教材简介、版权、章节内容、配套资源)
     *
     * @param bookId 书籍ID
     * @param versionNum 版本号
     * @return 书籍信息
     */
    Book getBookByBookIdAndVersion(String bookId, String versionNum);

    /**
     * 根据版本ID,获取书籍信息(基本信息、教材简介、版权、章节内容、配套资源)
     *
     * @param versionId 版本ID
     * @return 书籍信息
     */
    Book getBookByVersionId(Long versionId);

    /**
     * 获取教材版本下的变更内容（章节、基础信息、版权等）
     *
     * @param bookVersionId 教材版本ID
     * @return 变更内容列表
     */
    List<BookChangeItemEntity> getBookChangeListByVersionId(Long bookVersionId);

    /**
     * 根据教材ID和版本号，获取教材基本信息
     * @param bookId 教材ID
     * @param versionNum 教材版本号
     * @return 教材信息
     */
    Book getBookBasicInfoByIdAndVersion(String bookId, String versionNum);

    /**
     * 获取全部书籍的简要数据
     */
    List<BookSimpleDataDTO> getBookSimpleDataList();
}
