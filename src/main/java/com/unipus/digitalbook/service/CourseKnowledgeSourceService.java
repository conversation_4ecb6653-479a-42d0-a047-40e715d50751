package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO;

/**
 * 图谱创建相关接口
 */
public interface CourseKnowledgeSourceService {

    /**
     * 根据主键删除记录
     *
     * @param id 记录的主键
     * @return 删除的记录数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入一条记录
     *
     * @param record 要插入的记录
     * @return 插入的记录数
     */
    int insert(CourseKnowledgeSourceInfoPO record);

    /**
     * 插入一条记录（选择性字段）
     *
     * @param record 要插入的记录
     * @return 插入的记录数
     */
    int insertSelective(CourseKnowledgeSourceInfoPO record);

    /**
     * 根据主键查询记录
     *
     * @param id 记录的主键
     * @return 查询到的记录
     */
    CourseKnowledgeSourceInfoPO selectByPrimaryKey(Long id);

    /**
     * 根据主键更新记录（选择性字段）
     *
     * @param record 要更新的记录
     * @return 更新的记录数
     */
    int updateByPrimaryKeySelective(CourseKnowledgeSourceInfoPO record);

    /**
     * 根据主键更新记录
     *
     * @param record 要更新的记录
     * @return 更新的记录数
     */
    int updateByPrimaryKey(CourseKnowledgeSourceInfoPO record);


}
