package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.content.CustomContent;

import java.util.List;
import java.util.Map;

public interface CustomContentService {

    /**
     * 保存自建内容
     *
     * @param customContent 自建内容实体
     * @return 保存结果
     */
    Boolean saveCustomContent(CustomContent customContent);

    /**
     * 批量保存自建内容
     *
     * @param customContentList 自建内容列表
     * @return 保存结果
     */
    Boolean saveBatchCustomContent(List<CustomContent> customContentList);

    /**
     * 更新自建内容名称
     *
     * @param customContent 自建内容实体
     * @return 更新结果
     */
    Boolean updateCustomContentName(CustomContent customContent);

    /**
     * 根据业务ID获取编写中的自建内容
     *
     * @param bizId    业务ID
     * @param tenantId 租户ID
     * @return 自建内容实体
     */
    CustomContent getEditingCustomContentByBizId(String bizId, Long tenantId);

    /**
     * 根据业务ID列表批量获取编写中的自建内容
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容列表
     */
    List<CustomContent> getEditingCustomContentByBizIds(List<String> bizIds, Long tenantId);

    /**
     * 根据业务ID获取已发布的自建内容
     *
     * @param bizId    业务ID
     * @param tenantId 租户ID
     * @return 自建内容实体
     */
    CustomContent getPublishedCustomContentByBizId(String bizId, Long tenantId);

    /**
     * 根据业务ID列表批量获取已发布的自建内容
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容列表
     */
    List<CustomContent> getPublishedCustomContentByBizIds(List<String> bizIds, Long tenantId);

    /**
     * 根据业务ID列表批量删除自建内容
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @param userId   用户ID
     * @return 删除结果
     */
    Boolean deleteCustomContentByBizIds(List<String> bizIds, Long tenantId, Long userId);

    /**
     * 发布自建内容
     *
     * @param publishContentIds 要发布的自建内容ID列表
     * @param deleteContentIds  要删除的自建内容ID列表
     * @param tenantId          租户ID
     * @param userId            用户ID
     * @return 发布结果
     */
    Boolean publishCustomContent(List<String> publishContentIds, List<String> deleteContentIds, Long tenantId, Long userId);

    /**
     * 复制已发布的自建内容
     *
     * @param bizIds 已发布内容的bizId列表
     * @param userId 当前用户ID
     * @param tenantId 租户ID
     * @return 复制前后bizId的映射关系 Map<原bizId, 新bizId>
     */
    Map<String, String> copyPublishedCustomContentByBizIds(List<String> bizIds, Long tenantId, Long userId);
}