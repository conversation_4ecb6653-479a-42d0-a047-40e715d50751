package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.dto.knowledge.CourseKnowledgeSourceInfoDTO;
import com.unipus.digitalbook.model.dto.knowledge.CourseKnowledgeSourceInfoGroupDTO;
import com.unipus.digitalbook.model.dto.knowledge.KnowledgeSourceCheckDTO;
import com.unipus.digitalbook.model.params.knowledge.*;

import java.util.List;

/**
 * 图谱创建相关接口
 */
public interface KnowledgeResourceService {


    String knowledgeSourceAdd(KnowledgeSourceAddParam param, Long opUserId);

    void knowledgeSourceUpdate(KnowledgeSourceUpdateParam param, Long opUserId);

    void knowledgeSourceDisable(KnowledgeSourceIdParam param, Long opUserId);

    void knowledgeSourceDirUpdate(KnowledgeSourceDirUpdateParam param, Long opUserId);

    void knowledgeSourceDelete(KnowledgeSourceDeleteParam param, Long opUserId);

    void knowledgeSourceBatchDelete(KnowledgeSourceBatchDeleteParam param, Long opUserId);

    /**
     * 知识点删除，级联删除资源信息
     *
     * @param param
     * @param opUserId
     */
    void knowledgeSourceDeleteByThirdIds(KnowledgeSourceDeleteByThirdIdsParam param, Long opUserId);

    List<CourseKnowledgeSourceInfoDTO> knowledgeSourceQuery(KnowledgeSourceQueryParam param, Long opUserId);

    List<CourseKnowledgeSourceInfoGroupDTO> knowledgeSourceQueryGroup(KnowledgeSourceQueryGroupParam param, Long opUserId);

    KnowledgeSourceCheckDTO check(KnowledgeSourceCheckParam param, Long opUserId);

}
