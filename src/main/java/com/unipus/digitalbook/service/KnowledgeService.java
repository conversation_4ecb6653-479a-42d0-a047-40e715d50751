package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.DataListDTO;
import com.unipus.digitalbook.model.dto.knowledge.CourseKnowledgeInfoGetDTO;
import com.unipus.digitalbook.model.params.knowledge.KnowledgeAddParam;
import com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeInfoPO;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.Knowledge;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgePrimaryIdRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgeTag;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeAddRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeUpdateRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeResourceNodeResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.ResourceLabelResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 图谱创建相关接口
 */
public interface KnowledgeService {

    CourseKnowledgeInfoGetDTO courseKnowledgeInfoGet(String courseIdStr, Integer publishedStatus);

    /**
     * 新增知识图谱
     *
     * @param params
     * @return knoledgeId
     */
    CourseKnowledgeInfoPO knowledgeAdd(KnowledgeAddParam params, Long opUserId);

    /**
     * 更新知识图谱
     *
     * @param params
     * @return null
     */
    void knowledgeUpdate(KnowledgeUpdateRequest params, Long opUserId);

    /**
     * 删除知识图谱
     *
     * @param params
     * @return null
     */
    void knowledgeDelete(KnowledgePrimaryIdRequest params, Long opUserId);

    /**
     * 获取知识图谱
     *
     * @param knowledgeId
     * @return null
     */
    Knowledge knowledgeGet(String knowledgeId);

    /**
     * 知识图谱上线
     *
     * @return
     */
    void knowledgePublish(KnowledgePrimaryIdRequest params, Long opUserId);


    /**
     * 知识图谱文件上传
     *
     * @param
     * @return fileUrl
     */
    String knowledgeFileUpload(MultipartFile file);


    /**
     * 知识图谱的列表
     *
     * @param keyword
     * @return
     */
    DataListDTO<Knowledge> knowledgeList(String keyword);

    /**
     * 联想搜索知识图谱
     *
     * @param query 搜索条件
     * @return 知识图谱搜索响应
     */
    DataListDTO<Knowledge> knowledgeGraphSearch(String query);


    List<KnowledgeResourceNodeResponse.ResourceNode> getKnowledgeResourceNodeDetail(String knowledgeId);

    List<KnowledgeTag> getResourceLabelDetail();

}
