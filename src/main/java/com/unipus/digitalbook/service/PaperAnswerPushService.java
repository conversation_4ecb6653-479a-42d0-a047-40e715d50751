package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.paper.UserPaperSyncInfo;
import com.unipus.digitalbook.service.remote.restful.ucontent.BaseResponse;
import com.unipus.digitalbook.service.remote.restful.ucontent.PushResponseData;

/**
 * 试卷答案推送服务
 */
public interface PaperAnswerPushService {

    BaseResponse<PushResponseData> checkUserPaperAnswerByThird(UserPaperSyncInfo userPaperSyncInfo);

    BaseResponse<PushResponseData> pushUserPaperAnswerToThird(UserPaperSyncInfo userPaperSyncInfo);
}
