package com.unipus.digitalbook.service;


import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.UserAnswerNodeData;
import com.unipus.digitalbook.service.remote.restful.ucontent.BaseResponse;
import com.unipus.digitalbook.service.remote.restful.ucontent.PushResponseData;

import java.util.List;

/**
 * 题目和作答推送第三方相关接口
 */
public interface QuestionAnswerPushService {
    /**
     * 推送题目
     * @param question
     * @param userAnswer
     */
    BaseResponse<PushResponseData> pushUserAnswerToThird(BigQuestionGroup question, List<UserAnswer> userAnswers, Long chapterVersionId, String dataPackage, String clientIp);

    /**
     * 推送内容进度
     * @param nodeId
     * @param nodeType
     */
    BaseResponse<PushResponseData> pushUserContentProgressToThird(String openId, String nodeId, String nodeType, String bookId, String bookVersionNumber, String dataPackage, String clientIp);

}
