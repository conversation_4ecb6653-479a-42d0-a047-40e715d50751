package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.paper.QuestionBank;

import java.util.List;

/**
 * 题库服务接口基类
 */
public interface QuestionBankService {

    /**
     * 保存题库
     * @param questionBank 题库对象
     * @param userId 用户ID
     * @return 题库ID
     */
    String saveQuestionBank(QuestionBank questionBank, Long userId);

    /**
     * 获取题库列表
     * @param paperId 试卷ID
     * @return 题库对象列表
     */
    List<QuestionBank> getQuestionBankList(String paperId);

    /**
     * 获取题库列表
     * @param bankId 题库ID
     * @return 题库对象
     */
    QuestionBank getQuestionBankDetail(String bankId, String versionNumber);

    /**
     * 删除题库
     * @param bankId  题库业务ID
     * @param userId 当前用户ID
     * @return 题库ID
     */
    Boolean deleteQuestionBank(String bankId, Long userId);
}
