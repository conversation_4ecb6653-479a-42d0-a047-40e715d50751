package com.unipus.digitalbook.service;


import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;

import java.util.concurrent.CompletableFuture;

/**
 * 题目和作答推送第三方相关接口
 */
public interface QuestionPushService {
    /**
     * 推送题目
     * @param question
     * @param tenantId
     */
    CompletableFuture<String> pushQuestionToThirdAsync(BigQuestionGroup question, Long tenantId);

    /**
     * 检查题目推送状态
     * 看下题目数据是否都推送成功
     * @return
     */
    boolean checkQuestionPushStatus();


    /**
     * 重试推送题目
     */
    void retryPushQuestionToThird();
}
