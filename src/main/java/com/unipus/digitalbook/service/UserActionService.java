package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.entity.action.ReadProgressList;
import com.unipus.digitalbook.model.entity.action.UserAction;

public interface UserActionService {

    /**
     * 完成节点
     * @param userAction 用户行为
     * @return 是否成功 true 表示成功
     */
    Response<Boolean> finishNode(UserAction userAction);

    /**
     * 完成节点但是不发送消息
     * @return 是否成功 true 表示成功
     */
    Response<Boolean> directFinishNodeWithOutSendMessage(Long tenantId, String openId, String chapterId, Long chapterVersionId, String nodeId);

    /**
     * 获取章节进度
     *
     * @param tenantId 租户id
     * @param openId 用户id
     * @param chapterVersionId 章节版本id
     * @return
     */
    Response<ReadProgressList> getChapterProgress(Long tenantId, String openId, Long chapterVersionId);

    /**
     * 获取节点进度
     *
     * @param tenantId 租户id
     * @param openId 用户id
     * @param chapterVersionId 章节版本id
     * @param nodeId 节点id
     * @return
     */
    Response<Boolean> getNodeProgress(Long tenantId, String openId, Long chapterVersionId, String nodeId);

    /**
     * 获取节点进度值
     * @param tenantId 租户id
     * @param openId 用户id
     * @param chapterVersionId 章节版本id
     * @param nodeId 节点id
     * @return
     */
    Response<byte[]> getNodeProgressValueFromCache(Long tenantId, String openId, Long chapterVersionId);

    /**
     * 清空章节进度
     *
     * @param tenantId 租户id
     * @param openId 用户id
     * @param chapterVersionId 章节版本id
     * @return
     */
    Response<Boolean> clearChapterProgress(Long tenantId, String openId, Long chapterVersionId);

    /**
     * 迁移用户进度数据
     *
     * @return
     */
    Response<Boolean> migratedProgress(Long tenantId, String bookId, String bookVersion);


    /**
     * 获取章节进度的最新版本id
     *
     * @param tenantId 租户id
     * @param chapterId 章节id
     * @return
     */
    Response<Long> getLatestPublishChapterVersionId(Long tenantId, String chapterId);
}
