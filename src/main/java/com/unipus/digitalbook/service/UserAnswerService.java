package com.unipus.digitalbook.service;

import com.alibaba.fastjson2.JSONObject;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.JudgeTaskTicket;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.UserAnswerList;

import java.util.Collection;
import java.util.List;

/**
 * 用户作答服务
 *
 * <AUTHOR>
 */
public interface UserAnswerService {

    /**
     * 提交用户答案（异步作答题型调用）
     * 此方法将用户答案提交到后台判题系统，异步处理
     *
     * @param question    当前题组信息
     * @param userAnswers 用户作答列表
     * @return 返回处理后的用户答案对象，包含任务ID或处理状态等信息
     */
    List<JudgeTaskTicket> judgeStart(BigQuestionGroup question, UserAnswerList userAnswers);

    /**
     * 获取评测（判题）结果，通常用于轮询
     * 在提交答案之后，客户端可以定时轮询调用本方法以获取评测完成的最终结果
     *
     * @param userAnswerList 提交后的用户答案对象（含任务ID等信息）
     * @return 返回包含判题结果的用户答案列表
     */
    UserAnswerList fetchJudgeResult(UserAnswerList userAnswerList);

    /**
     * 判分请求
     * 在用户提交答案后，进行打分
     *
     * @param question    当前题组信息
     * @param userAnswers 用户作答列表
     * @return 返回评分结果（如总分等）
     */
    UserAnswerList judgeScore(BigQuestionGroup question, UserAnswerList userAnswers);

    /**
     * 提交用户答案
     * 进行判题，并存储用户作答记录
     * @param question    当前题组信息
     * @param userAnswers 用户作答列表
     * @return 返回处理后的用户答案对象，包含任务ID或处理状态等信息
     */
    UserAnswerList submitScoreAndPush(BigQuestionGroup question, UserAnswerList userAnswers, String chapterId, String chapterVersionNumber, String dataPackage, String clientIp);

    /**
     * 提交用户答案
     * 进行判题，并存储用户作答记录
     * @param question    当前题组信息
     * @param userAnswers 用户作答列表
     * @return 返回处理后的用户答案对象，包含任务ID或处理状态等信息
     */
    UserAnswerList submitScore(BigQuestionGroup question, UserAnswerList userAnswers);

    /**
     * 新增用户作答
     *
     * @param userAnswers 用户作答列表
     * @return 是否保存成功
     */
    List<UserAnswer> addUserAnswers(List<UserAnswer> userAnswers);


    /**
     * 保存用户作答
     *
     * @param userAnswers 用户作答列表
     * @return 是否更新成功
     */
    void saveUserAnswers(List<UserAnswer> userAnswers);


    /**
     * 查询最近一次同一批次下的作答记录
     *
     * @param bizQuestionIds        题目业务ID列表
     * @param questionVersionNumber 题目版本
     * @param openId                用户ssoID
     * @param tenantId              租户ID
     * @return 最近一次作答记录列表
     */
    List<UserAnswer> getLatestUserAnswers(Collection<String> bizQuestionIds, String questionVersionNumber, String openId, Long tenantId);

    /**
     * 查询作答记录
     *
     * @param bizQuestionIds        题目业务ID列表
     * @param questionVersionNumber 题目版本
     * @param openId                用户ssoID
     * @param tenantId              租户ID
     * @return 作答记录列表
     */
    List<UserAnswer> getUserAnswers(Collection<String> bizQuestionIds, String questionVersionNumber, String openId, Long tenantId);

    /**
     * 根据批次ID查询用户作答记录
     *
     * @param batchId
     * @return
     */
    List<UserAnswer> getUserAnswersByBatchId(String batchId);

    /**
     * 设置用户异步作答回调记录
     *
     * @param bizAnswerId   作答业务ID
     * @param callbackValue 用户作答详情
     */
    void setAnswerCallback(String bizAnswerId, String callbackValue);

    /**
     * 获取用户异步作答回调记录
     *
     * @param bizAnswerId 作答业务ID
     * @return 用户作答详情
     */
    JSONObject getAnswerCallback(String bizAnswerId);

    /**
     * 获取用户异步作答回调记录评测结果
     *
     * @param bizAnswerId 作答业务ID
     * @return 用户评测结果
     */
    String getAnswerCallbackEvaluation(String bizAnswerId);

    /**
     * 设置用户异步作答回调记录评测结果
     *
     * @param bizAnswerId 作答业务ID
     * @param evaluation  评测结果
     * @return 是否更改成功
     */
    Boolean setAnswerCallbackEvaluation(String bizAnswerId, String evaluation);
}
