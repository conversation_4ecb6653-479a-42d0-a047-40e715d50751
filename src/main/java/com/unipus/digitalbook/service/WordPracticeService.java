package com.unipus.digitalbook.service;

import com.unipus.digitalbook.model.entity.WordPractice;

import java.util.List;

/**
 * 词汇学练服务接口
 *
 * <AUTHOR>
 * @date 2025年06月27日 10:55:16
 */
public interface WordPracticeService {

    /**
     * 添加词汇学练信息
     *
     * @param wordPractice 词汇学练实体对象，包含需要添加的词汇学练相关信息
     * @return 返回添加操作的结果标识，通常为添加成功后的唯一标识字符串
     */
    String addWordPractice(WordPractice wordPractice);

    /**
     * 编辑词汇学练信息
     *
     * @param wordPractice 词汇学练实体对象，包含需要更新的词汇学练相关信息
     * @return 编辑操作是否成功，成功返回 true，失败返回 false
     */
    boolean editWordPractice(WordPractice wordPractice);

    /**
     * 根据ID获取词汇学练信息
     *
     * @param id 词汇学练信息的唯一标识ID
     * @return 对应的词汇学练实体对象
     */
    WordPractice getwordPracticeById(Long id);

    /**
     * 根据多个ID批量删除词汇学练信息
     *
     * @param ids 待删除的词汇学练信息的唯一标识ID列表
     * @return 删除操作影响的记录数
     */
    int deleteWordPracticeByIds(List<Long> ids);

    /**
     * 根据多个ID批量更新词汇学练信息的状态
     *
     * @param ids 待更新状态的词汇学练信息的唯一标识ID列表
     * @return 更新操作影响的记录数
     */
    int updateStatusByIds(List<Long> ids);
}
