package com.unipus.digitalbook.service.converter.tenant;

import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.common.exception.tenant.TenantMessageException;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.enums.ProduceResultEnum;

import java.util.function.Function;

public class BaseBodyConverter {
    public <S, T> T convert(S source, TypeReference<T> targetType, Function<S, T> function) {
        if (JsonUtil.isTypeReference(source, targetType)) {
            if (source instanceof String) {
                throw new TenantMessageException(ProduceResultEnum.STRING_TO_STRING_NOT_ALLOW.getMessage());
            }
            return (T)source;
        }
        boolean isReturnString = targetType.getType().equals(String.class);
        if (source instanceof String sourceType && !isReturnString) {
            return JsonUtil.parseObject(sourceType, targetType);
        }
        if (!(source instanceof String) && !isReturnString) {
            return function.apply(source);
        }
        return (T)JsonUtil.toJsonString(source);
    }

}
