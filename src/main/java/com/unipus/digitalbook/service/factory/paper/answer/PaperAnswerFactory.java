package com.unipus.digitalbook.service.factory.paper.answer;

import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 试卷答案策略工厂
 */
@Component
@RequiredArgsConstructor
public class PaperAnswerFactory {
    private final ChallengeAnswerStrategy challengeStrategy;
    private final DiagnosticAnswerStrategy diagnosticStrategy;
    private final RegularAnswerStrategy regularStrategy;

    public PaperAnswerStrategy createStrategy(PaperTypeEnum paperType) {

        return switch (paperType) {
            case REGULAR -> regularStrategy;
            case CHALLENGE -> challengeStrategy;
            case DIAGNOSTIC -> diagnosticStrategy;
        };
    }
}
