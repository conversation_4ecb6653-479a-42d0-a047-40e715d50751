package com.unipus.digitalbook.service.factory.paper.answer;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.exception.paper.UserPaperNotPassException;
import com.unipus.digitalbook.common.utils.DateUtil;
import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.entity.paper.UserPaperSubmitExtInfo;
import com.unipus.digitalbook.model.entity.paper.UserPaperSyncInfo;
import com.unipus.digitalbook.model.entity.paper.UserQuestionScore;
import com.unipus.digitalbook.model.entity.question.*;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import com.unipus.digitalbook.service.PaperAnswerPushService;
import com.unipus.digitalbook.service.UserAnswerService;
import com.unipus.digitalbook.service.remote.restful.ucontent.BaseResponse;
import com.unipus.digitalbook.service.remote.restful.ucontent.PushResponseData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.MessageFormat;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户作答处理器
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UserAnswerProcessor {
    private final UserAnswerService userAnswerService;
    private final PaperAnswerPushService paperAnswerPushService;

    /**
     * 处理用户大题
     * @param groups 大题组对象
     * @param answersMap 用户题目解答列表
     * @param ext 用户试卷成绩提交扩展信息
     * @return 用户作答（包含客观题分数）
     */
    public List<UserQuestionScore> processAnswers(List<BigQuestionGroup> groups, Map<String, List<UserAnswer>> answersMap,
                                                  UserPaperSubmitExtInfo ext) {

        if (CollectionUtils.isEmpty(groups) || answersMap == null) {
            return List.of();
        }

        // 用户作答记录（包含成绩）
        Map<String, UserAnswerList> userAnswerListMap = new HashMap<>();
        
        // 1.处理客观题作答并获取得分
        processObjectivesQuestions(groups, answersMap, userAnswerListMap);
        log.debug("客观题大题ID列表:{}", userAnswerListMap.keySet());

        // 因为没有独立的检测接口，这个阶段不做检测。通过后面的成绩同步接口全量提交。
        // 如果全量提交，存在未通过状态，则试卷实例成绩已入库数据处于未提交状态
        // 2.判断客观题过关率(是否所有题目全部检擦（主观题是否剔除）)
        // if(ext!=null && ext.isCheckPassRate()) {
        //     checkPassRate(groups, userAnswerListMap, ext);
        // }

        // 2.处理主观题作答
        processSubjectiveQuestions(groups, answersMap, userAnswerListMap);

        // 3.保存作答记录并组装成绩映射
        return saveUserAnswersWithScore(groups, userAnswerListMap);
    }

    // 客观题过关率检测接口
    private void checkPassRate(List<BigQuestionGroup> groups, Map<String, UserAnswerList> userAnswerListMap, UserPaperSubmitExtInfo ext) {

        // 1. 构建用户试卷客观题作答信息
        UserPaperSyncInfo userPaperBaseInfo = new UserPaperSyncInfo(groups, userAnswerListMap, ext);

        // 2. 获取用户作答通过率
        BaseResponse<PushResponseData> pushResponse = paperAnswerPushService.checkUserPaperAnswerByThird(userPaperBaseInfo);

        PushResponseData responseData = pushResponse.getData();
        boolean isPass = responseData.isPass();
        PushResponseData.StrategyNode strategyNode = responseData.getStrategyNode();
        double taskMiniScorePct = strategyNode==null ? 0.0 : strategyNode.getTask_mini_score_pct();
        log.debug("客观题正确率检查, 试卷实例ID:{}, 过关率:{}, 检查结果:{}, 返回消息:{}",
                ext.getInstanceId(), taskMiniScorePct, isPass ? "通过" : "未通过", responseData.getMessage());

        // 3.检查是否通过
        if (!isPass && strategyNode != null) {
            // 检查学习是否在有效时间段外
            checkValidPeriod(strategyNode);
            // 抛出用户未通过异常
            String message = MessageFormat.format("当前作答客观题正确率未达到{0}%, 请检查作答内容", taskMiniScorePct*100);
            throw new UserPaperNotPassException(message);
        }
    }

    // 检查学习是否在有效时间段外
    private void checkValidPeriod(PushResponseData.StrategyNode strategyNode) {
        // 检查是否在有效时间段外
        long nowEpochSecond = Instant.now().getEpochSecond();
        long startTime = strategyNode.getStartTime();
        long endTime = strategyNode.getEndTime();
        if(startTime==0 || endTime==0 ){
            log.debug("学习策略的单元有效期未设置,不做单元有效期检查。{}", strategyNode);
            return;
        }

        boolean isOutsideValidPeriod = startTime > nowEpochSecond ||  endTime < nowEpochSecond;
        if (isOutsideValidPeriod) {
            // 使用DateUtil工具类简化日期转换和格式化
            String startDateStr = DateUtil.dateTimeFormatYMD(startTime);
            String endDateStr = DateUtil.dateTimeFormatYMD(endTime);
            String errorMessage = MessageFormat.format("非单元有效期学习成绩不统计，请在单元有效期{0}～{1}内学习", startDateStr, endDateStr);
            throw new IllegalStateException(errorMessage);
        }
    }

    // 保存用户作答记录并组装成绩映射
    private List<UserQuestionScore> saveUserAnswersWithScore(List<BigQuestionGroup> groups, Map<String, UserAnswerList> userAnswerListMap) {
        // 用户小题维度成绩列表
        List<UserQuestionScore> scores = new ArrayList<>();

        for (BigQuestionGroup group : groups) {
            UserAnswerList userAnswerList = userAnswerListMap.get(group.getBizGroupId());
            if (userAnswerList != null && !CollectionUtils.isEmpty(userAnswerList.getUserAnswers())) {
                // 设置作答通过
                userAnswerList.getUserAnswers().forEach(ua->ua.setPass(Boolean.TRUE));
                // 保存用户作答记录
                userAnswerService.saveUserAnswers(userAnswerList.getUserAnswers());
                // 组装小题得分
                scores.addAll(UserQuestionScore.buildUserSmallQuestionScores(group, userAnswerList));
            }
        }
        return scores;
    }

    // 处理客观题
    private void processObjectivesQuestions(List<BigQuestionGroup> groups, Map<String, List<UserAnswer>> answersMap,
                                            Map<String, UserAnswerList> userAnswerListMap) {
        List<BigQuestionGroup> objectives = groups.stream()
                .filter(group -> QuestionGroupTypeEnum.isObjective(group.getType()))
                .toList();
        for (BigQuestionGroup group : objectives) {
            List<UserAnswer> answers = answersMap.getOrDefault(group.getBizGroupId(), new ArrayList<>());
            UserAnswerList userAnswerList = userAnswerService.judgeScore(group, new UserAnswerList(answers));
            userAnswerListMap.put(group.getBizGroupId(), userAnswerList);
        }
    }

    // 处理主观题
    private void processSubjectiveQuestions(List<BigQuestionGroup> groups, Map<String, List<UserAnswer>> answersMap,
                                            Map<String, UserAnswerList> userAnswerListMap) {
        List<BigQuestionGroup> subjectives = groups.stream()
                .filter(group -> !QuestionGroupTypeEnum.isObjective(group.getType()))
                .toList();
        if(CollectionUtils.isEmpty(subjectives)){
            log.debug("没有主观题");
            return;
        }

        // 使用虚拟线程池
        var executor = Executors.newVirtualThreadPerTaskExecutor();
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (BigQuestionGroup group : subjectives) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() ->
                    userAnswerListMap.put(group.getBizGroupId(), processSubjectiveQuestionsByAsyncJudge(group, answersMap)), executor);
            futures.add(future);
        }

        // 等待所有判题结果
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        // 10秒超时设置
        if (!allFutures.orTimeout(10, TimeUnit.SECONDS).exceptionally(ex -> {
            log.error("主观题处理超时", ex);
            futures.forEach(f -> f.cancel(true));
            throw new BizException("主观题处理超时");
        }).isDone()) {
            allFutures.join(); // 如果未超时，则正常等待完成
        }
    }

    // 发布主观题异步判分任务并等待结果
    private UserAnswerList processSubjectiveQuestionsByAsyncJudge(BigQuestionGroup group, Map<String, List<UserAnswer>>  answersMap) {
        List<UserAnswer> answers = answersMap.getOrDefault(group.getBizGroupId(), new ArrayList<>());
        UserAnswerList userAnswerList = new UserAnswerList(answers);

        // 主观题处理：启动判题任务并等待结果
        List<JudgeTaskTicket> judgeTaskTickets = userAnswerService.judgeStart(group, userAnswerList);
        if (CollectionUtils.isEmpty(judgeTaskTickets)) {
            log.error("主观题判分任务发布失败, 大题ID:{}", group.getBizGroupId());
            return userAnswerList;
        }else {
            // 取得客观题作答结果
            return fetchJudgeResult(group, userAnswerList);
        }
    }

    // 取得主观题异步判分结果（带超时控制）
    private UserAnswerList fetchJudgeResult(BigQuestionGroup group, UserAnswerList userAnswerList) {
        final int maxRetries = 30;
        final long retryIntervalMillis = 200L;
        long startTime = System.currentTimeMillis();

        for (int retry = 0; retry < maxRetries; retry++) {
            try {
                Thread.sleep(retryIntervalMillis);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("线程中断异常", e);
                return userAnswerList;
            }

            // 获取判分结果
            UserAnswerList result = userAnswerService.fetchJudgeResult(userAnswerList);
            if (isValidResult(result)) {
                log.debug("主观题判分结果获取成功，大题ID:{}", group.getBizGroupId());
                result.getUserAnswers().forEach(ua->
                        log.debug("主观题判分结果, ID:{},Evaluation:{}", ua.getBizQuestionId(), ua.getEvaluation()));
                // 执行评分处理
                return userAnswerService.judgeScore(group, result);
            }
            log.debug("等待主观题返回结果，大题ID:{} 尝试次数:{}, 耗时:{}ms",
                    group.getBizGroupId(), retry + 1, System.currentTimeMillis() - startTime);
        }

        log.warn("主观题判分结果获取超时，大题ID:{}", group.getBizGroupId());
        return userAnswerList;
    }

    // 验证判分结果是否有效
    private boolean isValidResult(UserAnswerList result) {
        return result != null && !CollectionUtils.isEmpty(result.getUserAnswers()) &&
               result.getUserAnswers().stream().allMatch(ua -> StringUtils.hasText(ua.getEvaluation()));
    }

    /**
     * 保存用户作答记录
     * @param batchId 用户作答提交批次ID（试卷实例ID）
     * @param answersMap 用户作答
     */
    public void saveAnswers(String batchId, Map<String, List<UserAnswer>> answersMap) {
        // 取得当前用户作答记录，并设置作答未通过（用于临时保存）
        List<UserAnswer> currentAnswers = answersMap.values().stream()
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .peek(ua -> ua.setPass(Boolean.FALSE))
                .collect(Collectors.toList());

        // 查询用户历史作答记录
        List<UserAnswer> existingAnswers = userAnswerService.getUserAnswersByBatchId(batchId);

        if (!CollectionUtils.isEmpty(existingAnswers)) {
            Set<String> currentQuestionIds = currentAnswers.stream()
                    .map(UserAnswer::getBizQuestionId)
                    .collect(Collectors.toSet());

            // 查询需要删除的作答记录
            List<UserAnswer> answersToDisable = existingAnswers.stream()
                    .filter(existing -> !currentQuestionIds.contains(existing.getBizQuestionId()))
                    .peek(ua -> ua.setEnable(Boolean.FALSE))
                    .toList();

            // 向新的作答记录中添加需要删除的作答记录
            currentAnswers.addAll(answersToDisable);
        }

        userAnswerService.saveUserAnswers(currentAnswers);
    }

    /**
     * 取得试卷实例中所有题目的用户作答
     * @param paperInstance 试卷实例
     * @return 用户作答
     */
    public Map<String, List<UserAnswer>> getAnswers(PaperInstance paperInstance) {

        Map<String, List<UserAnswer>> answersMap = new HashMap<>();
        String batchId = paperInstance.getInstanceId();

        // 1.根据成绩批次ID查询用户作答记录
        List<UserAnswer> userAnswersByBatchId = userAnswerService.getUserAnswersByBatchId(batchId);
        if(CollectionUtils.isEmpty(userAnswersByBatchId)) {
            return answersMap;
        }
        // 2.生成用户作答Map
        Map<String, UserAnswer> userAnswerMapByBizQuestionId = userAnswersByBatchId.stream()
                .collect(Collectors.toMap(UserAnswer::getBizQuestionId, v -> v, (v1, v2) -> v1));

        // 3.取得所有题目
        List<BigQuestionGroup> bigQuestionGroupList = paperInstance.getBigQuestionGroupList();
        if(CollectionUtils.isEmpty(bigQuestionGroupList)) {
            return answersMap;
        }
        // 4.根据题目ID分组
        bigQuestionGroupList.forEach(bigQuestionGroup -> {
            String bigBizQuestionId = bigQuestionGroup.getBizGroupId();
            // 取得标准答案Map（字题业务ID为主键）
            Map<String, List<QuestionAnswer>> standardAnswerMap = bigQuestionGroup.fetchCorrectAnswers();
            // 设置用户作答Map（字题业务ID为主键）
            standardAnswerMap.keySet().forEach(bizQuestionId -> {
                UserAnswer userAnswer = userAnswerMapByBizQuestionId.get(bizQuestionId);
                if(userAnswer != null) {
                    answersMap.computeIfAbsent(bigBizQuestionId, k -> new ArrayList<>()).add(userAnswer);
                }
            });
        });
        return answersMap;
    }
}
