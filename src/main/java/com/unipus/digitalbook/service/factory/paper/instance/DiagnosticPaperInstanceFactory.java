package com.unipus.digitalbook.service.factory.paper.instance;

import com.unipus.digitalbook.dao.PaperInstanceRelationPOMapper;
import com.unipus.digitalbook.dao.PaperQuestionInstancePOMapper;
import com.unipus.digitalbook.dao.PaperQuestionRelationPOMapper;
import com.unipus.digitalbook.dao.PaperRoundPOMapper;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.paper.PaperInstance;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.enums.PaperInstanceRelationTypeEnum;
import com.unipus.digitalbook.model.enums.UnitTestModeEnum;
import com.unipus.digitalbook.model.po.paper.PaperInstanceRelationPO;
import com.unipus.digitalbook.model.po.paper.PaperQuestionInstancePO;
import com.unipus.digitalbook.model.po.paper.PaperQuestionRelationPO;
import com.unipus.digitalbook.model.po.paper.PaperRoundPO;
import com.unipus.digitalbook.service.PaperService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 诊断卷实例工厂
 */
@Service
@Slf4j
public class DiagnosticPaperInstanceFactory extends AbstractPaperInstanceFactory {

    @Resource
    private PaperService paperService;
    @Resource
    private PaperQuestionRelationPOMapper paperQuestionRelationPOMapper;
    @Resource
    private PaperRoundPOMapper paperRoundPOMapper;
    @Resource
    private PaperQuestionInstancePOMapper paperQuestionInstancePOMapper;
    @Resource
    private PaperInstanceRelationPOMapper paperInstanceRelationPOMapper;

    /**
     * 生成试卷预览实例
     * @param paper 试卷对象 (大题组)
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @param testMode 试卷模式
     * @return 预览模式试卷实例
     */
    @Override
    public PaperInstance createPreviewPaperInstance(Paper paper, String openId, Long tenantId, UnitTestModeEnum testMode) {
        String paperId = paper.getPaperId();
        String versionNumber = paper.getVersionNumber();
        // 获取诊断试卷题目组
        List<BigQuestionGroup> finalQuestionList = paperService.getQuestions(paperId, versionNumber);

        // 获取推荐题目ID映射（key：默认题目ID，value：推荐题ID）
        Map<String, String> relationIdMap = getDiagnosticRelationMap(paperId, versionNumber);
        // 根据推荐标签列表返回推荐题目列表
        Set<String> needToRemoveIds;
        if(UnitTestModeEnum.DIAGNOSIS.match(testMode)) {
            // 诊断模式:取得推荐题ID列表
            needToRemoveIds = new HashSet<>(relationIdMap.values());
        }else{
            // 推荐模式:取得诊断题ID列表
            needToRemoveIds = new HashSet<>(relationIdMap.keySet());
        }
        // 剔除指定题目（仅输出诊断题或者推荐提）
        if (!CollectionUtils.isEmpty(finalQuestionList)) {
            finalQuestionList.forEach(q-> rebuildDiagnosticQuestionByRemove(q.getQuestions(), needToRemoveIds));
            finalQuestionList.removeIf(q -> CollectionUtils.isEmpty(q.getQuestions()));
        }
        // 生成预览模式试卷
        return super.createPaperInstance(paper, finalQuestionList, generateScoreBatchId(), generateInstanceId(), openId, tenantId, testMode);
    }

    /**
     * 生成试卷真实实例
     * @param paper 试卷对象
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @param testMode 试卷模式
     * @return 真实挑战卷实例
     */
    @Override
    public PaperInstance createRealPaperInstance(Paper paper, String openId, Long tenantId, UnitTestModeEnum testMode) {
        List<BigQuestionGroup> questions = generateDiagnosticQuestionList(paper, testMode, openId, tenantId);
        return super.createPaperInstance(paper, questions, super.generateScoreBatchId(), null, openId, tenantId, testMode);
    }

    /**
     * 生成题目列表
     * @param paper 试卷参数
     * @param testMode 测试模式（1：诊断模式，2：推荐模式）
     * @param openId 用户OpenId
     * @param tenantId 租户ID
     * @return 试卷题目列表
     */
    private List<BigQuestionGroup> generateDiagnosticQuestionList(Paper paper, UnitTestModeEnum testMode, String openId, Long tenantId) {
        String paperId = paper.getPaperId();
        String versionNumber = paper.getVersionNumber();

        // 1.根据单元测试卷ID获取题目组列表（包含题目组配置信息）
        List<BigQuestionGroup> questionGroupList = paperService.getQuestions(paperId, versionNumber);
        if(CollectionUtils.isEmpty(questionGroupList)){
            log.error("单元测试卷 {} 未配置任何题目或配置获取失败", paperId);
            return List.of();
        }

        // 2. 获取推荐题目ID映射（key：默认题目ID，value：推荐题ID）
        Map<String, String> relationIdMap = getDiagnosticRelationMap(paperId, versionNumber);

        // 3. 根据推荐标签列表返回推荐题目列表
        if(UnitTestModeEnum.DIAGNOSIS.match(testMode)) {
            // 诊断模式
            log.debug("单元测试卷 {} 诊断模式", paperId);
            // 取得推荐题ID列表
            Set<String> needToRemoveIds = new HashSet<>(relationIdMap.values());
            // 剔除推荐题（仅输出诊断默认题）
            questionGroupList.forEach(q-> rebuildDiagnosticQuestionByRemove(q.getQuestions(), needToRemoveIds));
        }else
        if(UnitTestModeEnum.RECOMMENDED.match(testMode)) {
            // 推荐模式
            log.debug("单元测试卷 {} 推荐模式", paperId);
            // 取得错误的默认题ID集合
            Set<String> incorrectDefaultIds = getIncorrectDiagnosticQuestionIds(paperId, versionNumber, openId, tenantId);
            // 构建移除对象ID集合(仅保留需要的推荐题ID)
            Set<String> needToRemoveIds = new HashSet<>();
            relationIdMap.forEach((k,v)->{
                // 总是添加默认题ID到移除集合
                needToRemoveIds.add(k);
                // 如果默认题回答正确且推荐题ID有效，则添加推荐题ID到移除集合
                if(!incorrectDefaultIds.contains(k) && StringUtils.hasText(v)) {
                    needToRemoveIds.add(v);
                }
            });

            // 剔除出诊断默认提和部分推荐提（仅仅保留需要的推荐题）
            questionGroupList.forEach(q-> rebuildDiagnosticQuestionByRemove(q.getQuestions(), needToRemoveIds));
        }else{
            log.error("不支持的测试模式: {}", testMode);
            throw new IllegalArgumentException("不支持的测试模式");
        }

        // 移除空的大题组
        if (!CollectionUtils.isEmpty(questionGroupList)) {
            questionGroupList.removeIf(q->CollectionUtils.isEmpty(q.getQuestions()));
        }

        return questionGroupList;
    }

    /**
     * 获取用户诊断卷作答错误的默认题ID集合
     * - 通过试卷管理的题目答题记录查询
     * @param versionNumber 版本号
     * @param openId 用户OpenId
     * @param tenantId 租户ID
     * @return 错误的默认题ID列表
     */
    private Set<String> getIncorrectDiagnosticQuestionIds(String paperId, String versionNumber, String openId, Long tenantId) {

        // 查询诊断卷最新实例信息
        PaperInstanceRelationPO latestInstanceRelation = paperInstanceRelationPOMapper.selectLatestInstanceRelation(
                paperId, versionNumber, PaperInstanceRelationTypeEnum.RECOMMEND.getCode(), tenantId, openId);
        if (latestInstanceRelation == null) {
            log.debug("未找到推荐卷实例关系，paperId:{}, versionNumber:{}, openId:{}", paperId, versionNumber, openId);
            return Set.of();
        }
        String instanceId = latestInstanceRelation.getBaseInstanceId();

        // 查村询用户作答记录
        List<PaperQuestionInstancePO> paperQuestionInstancePOS = paperQuestionInstancePOMapper.selectByRoundIds(List.of(instanceId));
        if(CollectionUtils.isEmpty(paperQuestionInstancePOS)){
            log.debug("未能从数据库中查询到试卷题目实例记录，instanceId: {}", instanceId);
            return Set.of();
        }

        // 返回错误的默认题ID列表
        return paperQuestionInstancePOS.stream()
                .filter(q->Boolean.FALSE.equals(q.getCorrect()))
                .map(PaperQuestionInstancePO::getQuestionBizId)
                .collect(Collectors.toSet());
    }

    /**
     * 剔除诊断中的指定题目
     * @param questions 题目列表
     * @param needToRemoveIds 需要移除的题目ID列表（任意层级上的对象ID列表）
     */
    private void rebuildDiagnosticQuestionByRemove(List<Question> questions, Set<String> needToRemoveIds) {
        if (CollectionUtils.isEmpty(questions) || CollectionUtils.isEmpty(needToRemoveIds)) {
            // 题组列表为空或者移除对象ID集合为空，则停止处理
            return;
        }
        // 过滤题目列表
        if(questions.stream().anyMatch(q -> needToRemoveIds.contains(q.getBizQuestionId()))){
            // 包含指定的题组ID，则从这一层移除指定题目
            questions.removeIf(q -> needToRemoveIds.contains(q.getBizQuestionId()));
        }
        // 递归处理下级题目列表
        questions.forEach(q -> rebuildDiagnosticQuestionByRemove(q.getQuestions(), needToRemoveIds));
    }

    /**
     * 获取推荐题目ID列表
     * @param paperId 试卷ID
     * @param versionNumber 版本号
     * @return 推荐题目ID列表
     */
    private Map<String, String> getDiagnosticRelationMap(String paperId, String versionNumber){
        // 取得推荐题目ID列表
        List<PaperQuestionRelationPO> paperQuestionRelationPOS = paperQuestionRelationPOMapper
                .selectByCondition(paperId, null, null, versionNumber);
        if(CollectionUtils.isEmpty(paperQuestionRelationPOS)){
            // 没有推荐题
            return Map.of();
        }
        // 返回推荐题ID列表
        return paperQuestionRelationPOS.stream().collect(Collectors.toMap(
                PaperQuestionRelationPO::getBizQuestionIdBase,PaperQuestionRelationPO::getBizQuestionIdTarget));
    }

    /**
     * 获取最近一次诊断试卷实例信息
     * @param paperId 试卷ID
     * @param paperVersion 版本号
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @param submitStatus 提交状态
     * @return 最近一次诊断试卷实例信息
     */
    @Override
    protected PaperRoundPO getLatestInstanceInfo(String paperId, String paperVersion, String openId, Long tenantId,
                                                 UnitTestModeEnum testMode, Integer submitStatus){
        // 查询诊断卷实例关系
        PaperInstanceRelationPO instanceRelationPO = paperInstanceRelationPOMapper
                .selectLatestInstanceRelation(paperId, paperVersion, PaperInstanceRelationTypeEnum.RECOMMEND.getCode(), tenantId, openId);
        if(instanceRelationPO == null){
            log.debug("未找到诊断卷实例关系，paperId:{}, paperVersion:{}, openId:{}", paperId, paperVersion, openId);
            return null;
        }
        String instanceId;
        if(UnitTestModeEnum.DIAGNOSIS.match(testMode)){
            instanceId= instanceRelationPO.getBaseInstanceId();
        }else{
            instanceId = instanceRelationPO.getTargetInstanceId();
        }
        log.debug("诊断卷实例ID, instanceId:{}, paperId:{}, paperVersion:{}, testMode:{}, openId:{}",
                instanceId, paperId, paperVersion, testMode, openId);
        if(StringUtils.hasText(instanceId)) {
            // 查询最新一次试卷提交记录(可能时诊断卷实例，也可能时推荐卷实例)
            return paperRoundPOMapper.getPaperInstanceById(instanceId);
        }else{
            log.debug("未能从数据库中查询到诊断卷最新提交记录: {}, {}, {}, {}", paperId, paperVersion, openId, tenantId);
            return null;
        }
    }

    /**
     * 根据试卷实例，获取试卷实例信息
     * @param instanceId 试卷实例ID
     * @param openId 用户ID
     * @param tenantId 租户ID
     * @return 试卷实例信息
     */
    @Override
    protected UnitTestModeEnum getTestMode(String instanceId, String openId, Long tenantId) {
        PaperInstanceRelationPO relation = paperInstanceRelationPOMapper.getRelationByInstanceId(
                instanceId,tenantId,openId, PaperInstanceRelationTypeEnum.RECOMMEND.getCode());
        if (relation == null) {
            log.debug("未找到关联关系,{}", instanceId);
            return null;
        }
        if(instanceId.equals(relation.getBaseInstanceId())){
            log.debug("诊断卷: 诊断模式,{}", instanceId);
            return UnitTestModeEnum.DIAGNOSIS;
        }else{
            log.debug("推荐卷: 推荐模式,{}", instanceId);
            return UnitTestModeEnum.RECOMMENDED;
        }
    }
}