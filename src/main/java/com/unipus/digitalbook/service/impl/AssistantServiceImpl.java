package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.assistant.AssistantException;
import com.unipus.digitalbook.common.utils.SHA256Util;
import com.unipus.digitalbook.service.AssistantService;
import com.unipus.digitalbook.service.remote.restful.assistant.AssistantApiService;
import com.unipus.digitalbook.service.remote.restful.ucontent.AssistantTokenRequest;
import com.unipus.digitalbook.service.remote.restful.ucontent.BaseResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@Slf4j
public class AssistantServiceImpl implements AssistantService {

    @Value("${remote.assistant.appKey}")
    private String appKey;

    @Value("${remote.assistant.appSecret}")
    private String appSecret;

    @Resource
    private AssistantApiService assistantApiService;

    @Override
    public String assistantToken(String openId) {
        long timestamp = System.currentTimeMillis();
        String nonce = UUID.randomUUID().toString();
        String origin = appKey + "-" + appSecret+ "-" + timestamp + "-" + nonce + "-" + openId;
        String signature = SHA256Util.hash(origin).toUpperCase();
        AssistantTokenRequest request = new AssistantTokenRequest
                (appKey, openId, timestamp, nonce, signature);
        try {
            BaseResponse<String> response = assistantApiService.assistantToken(request);
            int code = response.getCode();
            if (code == 0) {
                return response.getData();
            }
            log.error("openId {} get assistantToken errorCode {}", openId, code);
            throw new AssistantException("获取数字人token异常");
        } catch (Exception e) {
            log.error("openId {} get assistantToken error", openId, e);
            throw new AssistantException("获取数字人token异常", e);
        }
    }

}
