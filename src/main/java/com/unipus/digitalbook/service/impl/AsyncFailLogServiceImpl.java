package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.AsyncFailLogPOMapper;
import com.unipus.digitalbook.model.entity.AsyncFailLog;
import com.unipus.digitalbook.model.po.AsyncFailLogPO;
import com.unipus.digitalbook.service.AsyncFailLogService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 异步处理异常日志服务实现类
 */
@Service
@Slf4j
public class AsyncFailLogServiceImpl implements AsyncFailLogService {

    @Resource
    private AsyncFailLogPOMapper asyncFailLogPOMapper;

    /**
     * 添加异步处理异常日志
     *
     * @param asyncFailLog 异常日志对象
     */
    @Override
    public void addAsyncFailLog(AsyncFailLog asyncFailLog) {
        AsyncFailLogPO po = AsyncFailLogPO.builder(asyncFailLog);
        asyncFailLogPOMapper.insertAsyncFailLog(po);
        log.debug("异步处理异常日志添加成功，id={}", po.getId());
    }

    /**
     * 查询异常日志
     *
     * @param asyncFailLog 异常日志对象
     */
    @Override
    public List<AsyncFailLog> getAsyncFailLog(AsyncFailLog asyncFailLog) {
        List<AsyncFailLogPO> pos = asyncFailLogPOMapper.selectAsyncFailLog(AsyncFailLogPO.builder(asyncFailLog));
        return pos.stream().map(AsyncFailLogPO::toEntity).toList();
    }

    @Override
    public int selectFailCountByOperation(String operation) {
        return asyncFailLogPOMapper.selectFailCountByOperation(operation);
    }

    @Override
    public List<AsyncFailLog> getByOperation(String operation) {
        return asyncFailLogPOMapper.getByOperation(operation)
                .stream().map(AsyncFailLogPO::toEntity).toList();
    }

}
