package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.BookSettingLogPOMapper;
import com.unipus.digitalbook.dao.BookSettingPOMapper;
import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.entity.book.BookSettingLog;
import com.unipus.digitalbook.model.entity.book.BookSettingLogList;
import com.unipus.digitalbook.model.params.book.BookSettingLogParam;
import com.unipus.digitalbook.model.po.book.BookSettingLogPO;
import com.unipus.digitalbook.model.po.book.BookSettingPO;
import com.unipus.digitalbook.service.BookSettingService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class BookSettingServiceImpl implements BookSettingService {

    @Resource
    private BookSettingPOMapper bookSettingPOMapper;

    @Resource
    private BookSettingLogPOMapper bookSettingLogPOMapper;

    @Override
    public void upsertAssistantOpened(String bookId, boolean assistantSetting, Boolean assistantOpened, Boolean assistantUpdated, boolean knowledgeUpdated, Long currentUserId) {
        Date now = Calendar.getInstance().getTime();
        if (assistantSetting) {
            BookSettingPO bookSettingPO = new BookSettingPO();
            bookSettingPO.setBookId(bookId);
            bookSettingPO.setAssistantOpened(assistantOpened);
            bookSettingPO.setCreateTime(now);
            bookSettingPO.setUpdateTime(now);
            bookSettingPO.setCreateBy(currentUserId);
            bookSettingPO.setUpdateBy(currentUserId);
            bookSettingPO.setEnable(true);
            bookSettingPOMapper.upsertAssistantOpened(bookSettingPO);
        }
        BookSettingLogPO bookSettingLogPO = new BookSettingLogPO();
        bookSettingLogPO.setBookId(bookId);
        if (assistantSetting) {
            bookSettingLogPO.setAssistantOpened(assistantOpened);
            bookSettingLogPO.setAssistantUpdated(assistantUpdated);
        }
        bookSettingLogPO.setKnowledgeUpdated(knowledgeUpdated);
        bookSettingLogPO.setCreateTime(now);
        bookSettingLogPO.setUpdateTime(now);
        bookSettingLogPO.setCreateBy(currentUserId);
        bookSettingLogPO.setUpdateBy(currentUserId);
        bookSettingLogPO.setEnable(true);
        bookSettingLogPOMapper.saveLog(bookSettingLogPO);
    }

    @Override
    public void upsertGreeting(String bookId, String greeting, Long currentUserId) {
        Date now = Calendar.getInstance().getTime();
        BookSettingPO bookSettingPO = new BookSettingPO();
        bookSettingPO.setBookId(bookId);
        bookSettingPO.setGreeting(greeting);
        bookSettingPO.setCreateTime(now);
        bookSettingPO.setUpdateTime(now);
        bookSettingPO.setCreateBy(currentUserId);
        bookSettingPO.setUpdateBy(currentUserId);
        bookSettingPO.setEnable(true);
        bookSettingPOMapper.upsertGreeting(bookSettingPO);
    }

    @Override
    public BookSettingPO selectByBookId(String bookId) {
        return bookSettingPOMapper.selectByBookId(bookId);
    }

    @Override
    public BookSettingLogList searchLog(BookSettingLogParam param) {
        String bookId = param.getBookId();
        String name = param.getName();
        Date beginTime = param.getBeginTime();
        Date endTime = param.getEndTime();
        PageParams pageParams = param.getPageParams();
        List<BookSettingLog> list = bookSettingLogPOMapper.searchLog(bookId, name, beginTime, endTime, pageParams);
        Integer total = bookSettingLogPOMapper.searchLogCount(bookId, name, beginTime, endTime);
        return new BookSettingLogList(list, total);
    }

}
