package com.unipus.digitalbook.service.impl;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.common.utils.COSUtil;
import com.unipus.digitalbook.dao.BookTemporarySnapshotPOMapper;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.entity.book.BookTemporarySnapshot;
import com.unipus.digitalbook.model.po.book.BookTemporarySnapshotPO;
import com.unipus.digitalbook.service.BookTemporarySnapshotService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 教材临时快照服务实现类
 */
@Service
@Slf4j
public class BookTemporarySnapshotServiceImpl implements BookTemporarySnapshotService {

    @Resource
    private BookTemporarySnapshotPOMapper bookTemporarySnapshotPOMapper;

    @Resource
    private COSUtil cosUtil;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 保存快照
     *
     * @param bookTemporarySnapshot 教材临时快照实体
     * @param userId                用户ID
     * @return 保存结果
     */
    @Override
    public Boolean saveSnapshot(BookTemporarySnapshot bookTemporarySnapshot, Long userId) {
        BookTemporarySnapshotPO saveBookTemporarySnapshotPO = new BookTemporarySnapshotPO(bookTemporarySnapshot);
        saveBookTemporarySnapshotPO.setCreateBy(userId);
        // 上传快照内容到COS
        addSnapshotContentUploadUrl(saveBookTemporarySnapshotPO);
        return bookTemporarySnapshotPOMapper.insertSelective(saveBookTemporarySnapshotPO) > 0;
    }

    /**
     * 根据主键ID查询快照
     *
     * @param id 主键ID
     * @return 快照结果
     */
    @Override
    public BookTemporarySnapshot getSnapshotById(Long id) {
        BookTemporarySnapshotPO bookTemporarySnapshotPO = bookTemporarySnapshotPOMapper.selectByPrimaryKey(id);
        if (bookTemporarySnapshotPO == null) {
            throw new IllegalArgumentException("快照信息不存在");
        }
        // todo 暂时不存放文件url
        // 获取快照内容
        //getSnapshotContentByUrl(bookTemporarySnapshotPO);
        return bookTemporarySnapshotPO.toEntity();
    }

    /**
     * 根据教材ID和章节ID查询最近一次快照
     *
     * @param bookTemporarySnapshot 教材临时快照实体
     * @return 快照结果
     */
    @Override
    public BookTemporarySnapshot getLatestSnapshotByBookIdAndChapterId(BookTemporarySnapshot bookTemporarySnapshot) {
        BookTemporarySnapshotPO bookTemporarySnapshotPO = bookTemporarySnapshotPOMapper.selectLatestByBookIdAndChapterId(
                bookTemporarySnapshot.getBookId(),
                bookTemporarySnapshot.getChapterId());
        if (bookTemporarySnapshotPO == null) {
            throw new IllegalArgumentException("快照信息不存在");
        }
        // todo 暂时不存放文件url
        // 获取快照内容
        //getSnapshotContentByUrl(bookTemporarySnapshotPO);
        return bookTemporarySnapshotPO.toEntity();
    }

    /**
     * 上传快照内容
     *
     * @param bookTemporarySnapshotPO 快照PO
     */
    private void addSnapshotContentUploadUrl(BookTemporarySnapshotPO bookTemporarySnapshotPO) {
        if (bookTemporarySnapshotPO == null) {
            return;
        }
        String content = bookTemporarySnapshotPO.getContent();
        if (StringUtils.isBlank(content)) {
            return;
        }
        if (JSON.isValid(content)) {
            // 生成带时间戳的文件名
            Date date = new Date();
            String fileName = String.format("/bookTemporarySnapshot/%s/%s/%s/content.json",
                    bookTemporarySnapshotPO.getBookId(),
                    bookTemporarySnapshotPO.getChapterId(),
                    new SimpleDateFormat("yyyyMMddHHmmss").format(date));
            String contentUrl = cosUtil.getPrivateUploadContentUrl(fileName, content);
            // todo 暂时不存放文件url
            //bookTemporarySnapshotPO.setContent(contentUrl);
            bookTemporarySnapshotPO.setCreateTime(date);
            log.info("上传快照内容到COS,bookId={},chapterId={},url={}",
                    bookTemporarySnapshotPO.getBookId(), bookTemporarySnapshotPO.getChapterId(), contentUrl);
        }
    }

    /**
     * 获取快照内容
     *
     * @param bookTemporarySnapshotPO 快照PO
     */
    public void getSnapshotContentByUrl(BookTemporarySnapshotPO bookTemporarySnapshotPO) {
        if (bookTemporarySnapshotPO == null) {
            return;
        }
        String contentUrl = bookTemporarySnapshotPO.getContent();
        if (StringUtils.isBlank(contentUrl)) {
            return;
        }
        // 构建缓存键：使用前缀 + URL的MD5哈希值作为唯一标识
        String cacheKey = CacheConstant.REDIS_COS_BOOK_TEMPORARY_SNAPSHOT_CONTENT_PREFIX + DigestUtils.md5Hex(contentUrl);
        // 先从缓存中获取快照内容
        String content = stringRedisTemplate.opsForValue().get(cacheKey);
        if (content != null) {
            log.debug("从缓存中获取快照内容: {} , cacheKey: {}", contentUrl, cacheKey);
        } else {
            // 缓存中没有，调用COSUtil下载快照内容
            content = cosUtil.getDownloadContentByUrl(contentUrl);
            // 如果成功获取到内容，将其存入缓存
            if (content != null) {
                stringRedisTemplate.opsForValue().set(cacheKey, content,
                        CacheConstant.REDIS_COS_BOOK_TEMPORARY_SNAPSHOT_CONTENT_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                log.debug("下载并缓存快照内容: {} , cacheKey: {}", contentUrl, cacheKey);
            }
        }
        bookTemporarySnapshotPO.setContent(content);
    }
}
