package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.CourseKnowledgeInfoMapper;
import com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeInfoPO;
import com.unipus.digitalbook.service.CourseKnowledgeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 教材图谱相关接口
 */
@Service
public class CourseKnowledgeServiceImpl implements CourseKnowledgeService {

    @Resource
    CourseKnowledgeInfoMapper courseKnowledgeInfoMapper;


    /**
     * 根据主键删除记录
     *
     * @param id 记录的主键
     * @return 删除的记录数
     */
    public int deleteByPrimaryKey(Long id) {
        return courseKnowledgeInfoMapper.deleteByPrimaryKey(id);
    }

    /**
     * 插入一条记录
     *
     * @param record 要插入的记录
     * @return 插入的记录数
     */
    public int insert(CourseKnowledgeInfoPO record) {
        return courseKnowledgeInfoMapper.insert(record);
    }

    /**
     * 插入一条记录（选择性字段）
     *
     * @param record 要插入的记录
     * @return 插入的记录数
     */
    public int insertSelective(CourseKnowledgeInfoPO record) {
        return courseKnowledgeInfoMapper.insertSelective(record);
    }

    /**
     * 根据主键查询记录
     *
     * @param id 记录的主键
     * @return 查询到的记录
     */
    public CourseKnowledgeInfoPO selectByPrimaryKey(Long id) {
        return courseKnowledgeInfoMapper.selectByPrimaryKey(id);
    }

    /**
     * 根据主键更新记录（选择性字段）
     *
     * @param record 要更新的记录
     * @return 更新的记录数
     */
    public int updateByPrimaryKeySelective(CourseKnowledgeInfoPO record) {
        return courseKnowledgeInfoMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 根据主键更新记录
     *
     * @param record 要更新的记录
     * @return 更新的记录数
     */
    public int updateByPrimaryKey(CourseKnowledgeInfoPO record) {
        return courseKnowledgeInfoMapper.updateByPrimaryKey(record);
    }


}
