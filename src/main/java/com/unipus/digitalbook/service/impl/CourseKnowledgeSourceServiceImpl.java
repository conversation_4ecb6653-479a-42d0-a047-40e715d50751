package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.CourseKnowledgeSourceInfoMapper;
import com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO;
import com.unipus.digitalbook.service.CourseKnowledgeSourceService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class CourseKnowledgeSourceServiceImpl implements CourseKnowledgeSourceService {

    @Resource
    private CourseKnowledgeSourceInfoMapper courseKnowledgeSourceInfoMapper;

    /**
     * 根据主键删除记录
     *
     * @param id 记录的主键
     * @return 删除的记录数
     */
    public int deleteByPrimaryKey(Long id) {
        return courseKnowledgeSourceInfoMapper.deleteByPrimaryKey(id);
    }

    /**
     * 插入一条记录
     *
     * @param record 要插入的记录
     * @return 插入的记录数
     */
    public int insert(CourseKnowledgeSourceInfoPO record) {
        return courseKnowledgeSourceInfoMapper.insert(record);
    }

    /**
     * 插入一条记录（选择性字段）
     *
     * @param record 要插入的记录
     * @return 插入的记录数
     */
    public int insertSelective(CourseKnowledgeSourceInfoPO record) {
        return courseKnowledgeSourceInfoMapper.insertSelective(record);
    }

    /**
     * 根据主键查询记录
     *
     * @param id 记录的主键
     * @return 查询到的记录
     */
    public CourseKnowledgeSourceInfoPO selectByPrimaryKey(Long id) {
        return courseKnowledgeSourceInfoMapper.selectByPrimaryKey(id);
    }

    /**
     * 根据主键更新记录（选择性字段）
     *
     * @param record 要更新的记录
     * @return 更新的记录数
     */
    public int updateByPrimaryKeySelective(CourseKnowledgeSourceInfoPO record) {
        return courseKnowledgeSourceInfoMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 根据主键更新记录
     *
     * @param record 要更新的记录
     * @return 更新的记录数
     */
    public int updateByPrimaryKey(CourseKnowledgeSourceInfoPO record) {
        return courseKnowledgeSourceInfoMapper.updateByPrimaryKey(record);
    }
}
