package com.unipus.digitalbook.service.impl;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.common.utils.COSUtil;
import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.dao.CustomContentPOMapper;
import com.unipus.digitalbook.dao.CustomContentQuestionGroupRelationPOMapper;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.entity.content.CustomContent;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.CustomContentStatusEnum;
import com.unipus.digitalbook.model.enums.EventTypeEnum;
import com.unipus.digitalbook.model.po.content.CustomContentPO;
import com.unipus.digitalbook.model.po.content.CustomContentQuestionGroupRelationPO;
import com.unipus.digitalbook.publisher.standalone.CustomContentEventPublisher;
import com.unipus.digitalbook.service.CustomContentService;
import com.unipus.digitalbook.service.QuestionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class CustomContentServiceImpl implements CustomContentService {

    @Resource
    private CustomContentPOMapper customContentPOMapper;
    @Resource
    private CustomContentQuestionGroupRelationPOMapper customContentQuestionGroupRelationPOMapper;
    @Resource
    private COSUtil cosUtil;
    @Resource
    private QuestionService questionService;
    @Resource
    private CustomContentEventPublisher customContentEventPublisher;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 保存自建内容
     *
     * @param customContent 自建内容实体
     * @return 保存结果
     */
    @Override
    public Boolean saveCustomContent(CustomContent customContent) {
        String bizId = customContent.getBizId();
        Long tenantId = customContent.getTenantId();
        CustomContentPO customContentPO = null;
        if (StringUtils.isBlank(bizId)) {
            bizId = IdentifierUtil.getShortUUID();
        } else {
            customContentPO = customContentPOMapper.selectEditingByBizId(bizId, tenantId);
        }
        // 新增或更新
        if (customContentPO == null) {
            CustomContentPO insertCustomContentPO = new CustomContentPO(customContent);
            insertCustomContentPO.setBizId(bizId);
            insertCustomContentPO.setStatus(CustomContentStatusEnum.EDITING.getCode());
            insertCustomContentPO.setCreateTime(new Date());
            insertCustomContentPO.setUpdateBy(null);
            // 上传自建内容到COS
            addCustomContentUploadUrl(insertCustomContentPO);
            // 插入记录
            int row = customContentPOMapper.insertSelective(insertCustomContentPO);
            // 保存题目关联关系
            addCustomContentQuestions(customContent.getQuestionList(), insertCustomContentPO.getId(), tenantId, insertCustomContentPO.getCreateBy());
            if (row > 0) {
                // 发布自建内容新增版本消息
                customContentEventPublisher.customContentEventPublisher(bizId, EventTypeEnum.ADD_VERSION, null);
                return true;
            } else {
                return false;
            }
        } else {
            CustomContentPO updateCustomContentPO = new CustomContentPO(customContent);
            updateCustomContentPO.setId(customContentPO.getId());
            updateCustomContentPO.setUpdateTime(new Date());
            // 上传自建内容到COS
            addCustomContentUploadUrl(updateCustomContentPO);
            // 更新记录
            int row = customContentPOMapper.updateByPrimaryKeySelective(updateCustomContentPO);
            // 更新题目关联关系：先删除旧的关联关系，再添加新的关联关系
            addCustomContentQuestions(customContent.getQuestionList(), updateCustomContentPO.getId(), tenantId, updateCustomContentPO.getUpdateBy());
            if (row > 0) {
                // 发布自建内容编辑消息
                customContentEventPublisher.customContentEventPublisher(bizId, EventTypeEnum.EDIT, null);
                return true;
            } else {
                return false;
            }
        }
    }

    /**
     * 批量保存自建内容
     *
     * @param customContentList 自建内容列表
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveBatchCustomContent(List<CustomContent> customContentList) {
        if (CollectionUtils.isEmpty(customContentList)) {
            return true;
        }
        for (CustomContent customContent : customContentList) {
            saveCustomContent(customContent);
        }
        return true;
    }

    /**
     * 添加自建内容题目关联关系
     * 区分null和空集合：null时不进行更新操作，非null时进行关联关系的删除和重建
     *
     * @param questionList 题目列表
     * @param contentId    自建内容ID
     * @param tenantId     租户ID
     * @param userId       用户ID
     */
    private void addCustomContentQuestions(List<BigQuestionGroup> questionList, Long contentId, Long tenantId, Long userId) {
        if (questionList == null) {
            return;
        }
        // 当questionList不为null时（包括空集合），先删除旧的关联关系（逻辑删除）
        customContentQuestionGroupRelationPOMapper.deleteByContentId(contentId, tenantId, userId);

        if (CollectionUtils.isEmpty(questionList)) {
            return;
        }
        List<Long> newGroupIds = questionService.batchSaveBigQuestions(questionList);
        if (newGroupIds.isEmpty()) {
            return;
        }
        // 保存自建内容ID和题的关系
        List<CustomContentQuestionGroupRelationPO> relationPOList = newGroupIds.stream().map(
                        groupId -> new CustomContentQuestionGroupRelationPO(groupId, contentId, tenantId, userId))
                .toList();
        customContentQuestionGroupRelationPOMapper.batchInsert(relationPOList);
    }

    /**
     * 上传自建内容到COS
     *
     * @param customContentPO 自建内容PO
     */
    private void addCustomContentUploadUrl(CustomContentPO customContentPO) {
        if (customContentPO == null) {
            return;
        }
        if (StringUtils.isBlank(customContentPO.getContent())
                && StringUtils.isBlank(customContentPO.getStudentContent())) {
            return;
        }
        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            Date date = new Date();

            String content = customContentPO.getContent();
            if (StringUtils.isNotBlank(content) && JSON.isValid(content)) {
                futures.add(CompletableFuture.runAsync(() -> {
                    String fileName = String.format("/customContent/editing/%s/%s/content.json",
                            customContentPO.getBizId(),
                            new SimpleDateFormat("yyyyMMddHHmmss").format(date));
                    String contentUrl = cosUtil.getPrivateUploadContentUrl(fileName, content);
                    customContentPO.setContent(contentUrl);
                    log.info("上传自建内容到COS,bizId={},url={}", customContentPO.getBizId(), customContentPO.getContent());
                }, executor));
            }

            String studentContent = customContentPO.getStudentContent();
            if (StringUtils.isNotBlank(studentContent) && JSON.isValid(studentContent)) {
                futures.add(CompletableFuture.runAsync(() -> {
                    String fileName = String.format("/customContent/editing/%s/%s/studentContent.json",
                            customContentPO.getBizId(),
                            new SimpleDateFormat("yyyyMMddHHmmss").format(date));
                    String studentContentUrl = cosUtil.getPrivateUploadContentUrl(fileName, studentContent);
                    customContentPO.setStudentContent(studentContentUrl);
                    log.info("上传自建学生内容到COS,bizId={},url={}", customContentPO.getBizId(), customContentPO.getStudentContent());
                }, executor));
            }

            // 等待所有上传任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }
    }

    /**
     * 为自建内容设置预签名URL
     *
     * @param customContentPOList 自建内容PO列表
     */
    public void getCustomContentPresignedUrl(List<CustomContentPO> customContentPOList) {
        if (CollectionUtils.isEmpty(customContentPOList)) {
            return;
        }
        customContentPOList.forEach(this::getCustomContentPresignedUrl);
    }

    /**
     * 为自建内容设置预签名URL
     *
     * @param customContentPO 自建内容PO
     */
    public void getCustomContentPresignedUrl(CustomContentPO customContentPO) {
        if (customContentPO == null) {
            return;
        }
        if (StringUtils.isBlank(customContentPO.getContent())
                && StringUtils.isBlank(customContentPO.getStudentContent())) {
            return;
        }
        // 使用虚拟线程
        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            String content = customContentPO.getContent();
            if (StringUtils.isNotBlank(content) && !JSON.isValid(content)) {
                futures.add(CompletableFuture.runAsync(() -> {
                    customContentPO.setContent(getPresignedUrlWithCache(content));
                }, executor));
            }

            String studentContent = customContentPO.getStudentContent();
            if (StringUtils.isNotBlank(studentContent) && !JSON.isValid(studentContent)) {
                futures.add(CompletableFuture.runAsync(() -> {
                    customContentPO.setStudentContent(getPresignedUrlWithCache(studentContent));
                }, executor));
            }
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }
    }

    /**
     * 带缓存的预签名URL获取方法
     *
     * @param url 原始URL
     * @return 预签名URL
     */
    private String getPresignedUrlWithCache(String url) {
        // 参数校验：如果URL为空或空白，直接返回null
        if (StringUtils.isBlank(url)) {
            return null;
        }

        // 构建Redis缓存键：使用前缀 + URL的MD5哈希值作为唯一标识,避免URL中特殊字符对Redis键的影响，同时保证键的唯一性
        String cacheKey = CacheConstant.REDIS_COS_PRESIGNED_URL_PREFIX + DigestUtils.md5Hex(url);

        // 先从缓存中获取预签名URL
        String cachedPresignedUrl = stringRedisTemplate.opsForValue().get(cacheKey);
        if (cachedPresignedUrl != null) {
            log.debug("从缓存中获取预签名URL: {} , cacheKey: {}", url, cacheKey);
            return cachedPresignedUrl;
        }

        // 缓存中没有，调用COSUtil生成新的预签名URL
        String presignedUrl = cosUtil.getPresignedUrlByUrl(url, new Date(System.currentTimeMillis() + 60 * 1000));

        // 将预签名URL存入缓存，过期时间比实际URL过期时间短10秒
        stringRedisTemplate.opsForValue().set(cacheKey, presignedUrl,
                CacheConstant.REDIS_COS_PRESIGNED_URL_TIMEOUT_SECONDS, TimeUnit.SECONDS);

        log.debug("生成并缓存预签名URL: {} , cacheKey: {}", url, cacheKey);
        return presignedUrl;
    }

    /**
     * 根据URL获取自建内容
     *
     * @param contentUrl 内容URL地址
     * @return 自建内容字符串，如果URL为空则返回null
     */
    public String getCustomContentByUrl(String contentUrl) {
        if (StringUtils.isBlank(contentUrl)) {
            return null;
        }

        // 构建缓存键：使用前缀 + URL的MD5哈希值作为唯一标识
        String cacheKey = CacheConstant.REDIS_COS_CHAPTER_CONTENT_PREFIX + DigestUtils.md5Hex(contentUrl);

        // 先从缓存中获取自建内容
        String cachedContent = stringRedisTemplate.opsForValue().get(cacheKey);
        if (cachedContent != null) {
            log.debug("从缓存中获取自建内容: {} , cacheKey: {}", contentUrl, cacheKey);
            return cachedContent;
        }

        // 缓存中没有，调用COSUtil下载自建内容
        String content = cosUtil.getDownloadContentByUrl(contentUrl);

        // 如果成功获取到内容，将其存入缓存
        if (content != null) {
            stringRedisTemplate.opsForValue().set(cacheKey, content,
                    CacheConstant.REDIS_CHAPTER_CONTENT_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            log.debug("下载并缓存自建内容: {} , cacheKey: {}", contentUrl, cacheKey);
        }

        return content;
    }

    /**
     * 更新自建内容名称
     *
     * @param customContent 自建内容实体
     * @return 更新结果
     */
    @Override
    public Boolean updateCustomContentName(CustomContent customContent) {
        if (customContent.getBizId() == null) {
            throw new IllegalArgumentException("bizId不能为空");
        }
        // 根据bizId查询现有记录
        CustomContentPO existingPO = customContentPOMapper.selectEditingByBizId(customContent.getBizId(), customContent.getTenantId());
        if (existingPO == null) {
            throw new IllegalArgumentException("自建内容不存在");
        }
        // 更新名称和更新时间
        existingPO.setName(customContent.getName());
        existingPO.setUpdateTime(new Date());
        existingPO.setUpdateBy(customContent.getUpdateBy());
        return customContentPOMapper.updateByPrimaryKeySelective(existingPO) > 0;
    }

    /**
     * 根据业务ID获取编写中的自建内容
     *
     * @param bizId    业务ID
     * @param tenantId 租户ID
     * @return 自建内容实体
     */
    @Override
    public CustomContent getEditingCustomContentByBizId(String bizId, Long tenantId) {
        CustomContentPO po = customContentPOMapper.selectEditingByBizId(bizId, tenantId);
        if (po == null) {
            return null;
        }
        // 为内容设置预签名URL
        getCustomContentPresignedUrl(po);
        return po.toEntity();
    }

    /**
     * 根据业务ID列表批量获取编写中的自建内容
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容列表
     */
    @Override
    public List<CustomContent> getEditingCustomContentByBizIds(List<String> bizIds, Long tenantId) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return List.of();
        }
        List<CustomContentPO> customContentPOList = customContentPOMapper.selectEditingByBizIds(bizIds, tenantId);
        if (CollectionUtils.isEmpty(customContentPOList)) {
            return List.of();
        }
        // 为内容设置预签名URL
        getCustomContentPresignedUrl(customContentPOList);
        return customContentPOList.stream()
                .map(CustomContentPO::toEntity)
                .toList();
    }

    /**
     * 根据业务ID获取已发布的自建内容
     *
     * @param bizId    业务ID
     * @param tenantId 租户ID
     * @return 自建内容实体
     */
    @Override
    public CustomContent getPublishedCustomContentByBizId(String bizId, Long tenantId) {
        CustomContentPO po = customContentPOMapper.selectPublishedByBizId(bizId, tenantId);
        if (po == null) {
            return null;
        }
        // 为内容设置预签名URL
        getCustomContentPresignedUrl(po);
        return po.toEntity();
    }

    /**
     * 根据业务ID列表批量获取已发布的自建内容
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 自建内容列表
     */
    @Override
    public List<CustomContent> getPublishedCustomContentByBizIds(List<String> bizIds, Long tenantId) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return List.of();
        }
        List<CustomContentPO> customContentPOList = customContentPOMapper.selectPublishedByBizIds(bizIds, tenantId);
        if (CollectionUtils.isEmpty(customContentPOList)) {
            return List.of();
        }
        // 为内容设置预签名URL
        getCustomContentPresignedUrl(customContentPOList);
        return customContentPOList.stream()
                .map(CustomContentPO::toEntity)
                .toList();
    }

    /**
     * 根据业务ID列表批量删除编写中的自建内容
     *
     * @param bizIds   业务ID列表
     * @param tenantId 租户ID
     * @return 删除结果
     */
    @Override
    public Boolean deleteCustomContentByBizIds(List<String> bizIds, Long tenantId, Long userId) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return false;
        }
        int count = customContentPOMapper.deleteEditingByBizIds(bizIds, tenantId, userId);
        return count > 0;
    }

    /**
     * 发布自建内容
     *
     * @param publishContentIds 要发布的内容ID列表
     * @param deleteContentIds  要删除的内容ID列表
     * @param tenantId          租户ID
     * @return 发布结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean publishCustomContent(List<String> publishContentIds, List<String> deleteContentIds, Long tenantId, Long userId) {
        // 发布内容：复制编写中的数据，新增为已发布状态
        if (!CollectionUtils.isEmpty(publishContentIds)) {
            List<CustomContentPO> customContentPOList = customContentPOMapper.selectEditingByBizIds(publishContentIds, tenantId);

            // 使用虚拟线程池并行处理COS文件复制
            try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
                List<CompletableFuture<Void>> futures = new ArrayList<>();

                for (CustomContentPO po : customContentPOList) {
                    futures.add(CompletableFuture.runAsync(() -> {
                        // 复制COS文件并更新URL
                        copyContentFilesForPublish(po);
                        // 设置为已发布状态
                        po.setStatus(CustomContentStatusEnum.PUBLISHED.getCode());
                        po.setCreateTime(new Date());
                        po.setId(null);

                        // 插入新的已发布记录
                        customContentPOMapper.insertSelective(po);
                        log.info("发布自建内容成功，bizId: {}", po.getBizId());
                    }, executor));
                }
                // 等待所有发布任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            }
        }
        // 删除已发布的内容
        if (!CollectionUtils.isEmpty(deleteContentIds)) {
            customContentPOMapper.deletePublishedByBizIds(deleteContentIds, tenantId, userId);
        }
        return true;
    }

    /**
     * 为发布复制内容文件到COS
     *
     * @param customContentPO 自建内容PO
     */
    private void copyContentFilesForPublish(CustomContentPO customContentPO) {
        String bizId = customContentPO.getBizId();
        // 复制content文件
        if (StringUtils.isNotBlank(customContentPO.getContent()) && !JSON.isValid(customContentPO.getContent())) {
            String publishedContentPath = generatePublishedContentPath(bizId, "content.json");
            String publishedContentUrl = cosUtil.copyObject(customContentPO.getContent(), publishedContentPath);
            customContentPO.setContent(publishedContentUrl);
            log.debug("复制content文件成功，bizId: {}, 新URL: {}", bizId, publishedContentUrl);
        }

        // 复制studentContent文件
        if (StringUtils.isNotBlank(customContentPO.getStudentContent()) && !JSON.isValid(customContentPO.getStudentContent())) {
            String publishedStudentContentPath = generatePublishedContentPath(bizId, "studentContent.json");
            String publishedStudentContentUrl = cosUtil.copyObject(customContentPO.getStudentContent(), publishedStudentContentPath);
            customContentPO.setStudentContent(publishedStudentContentUrl);
            log.debug("复制studentContent文件成功，bizId: {}, 新URL: {}", bizId, publishedStudentContentUrl);
        }
    }

    /**
     * 为已发布内容生成COS文件路径
     *
     * @param bizId    业务ID
     * @param fileName 文件名
     * @return 已发布内容的COS路径
     */
    public String generatePublishedContentPath(String bizId, String fileName) {
        Date now = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        return String.format("customContent/published/%s/%s/%s", bizId, sdf.format(now), fileName);
    }

    /**
     * 复制已发布的自建内容为编写中状态
     *
     * @param bizIds   已发布内容的bizId列表
     * @param userId   当前用户ID
     * @param tenantId 租户ID
     * @return 复制前后bizId的映射关系 Map<原bizId, 新bizId>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, String> copyPublishedCustomContentByBizIds(List<String> bizIds, Long tenantId, Long userId) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return new HashMap<>();
        }
        log.info("开始复制已发布内容为编写中状态，数量: {}, 租户ID: {}, 用户ID: {}", bizIds.size(), userId, tenantId);
        // 查询已发布的内容
        List<CustomContentPO> publishedContentList = customContentPOMapper.selectPublishedByBizIds(bizIds, tenantId);
        if (CollectionUtils.isEmpty(publishedContentList)) {
            log.warn("未找到已发布的内容，bizIds: {}", bizIds);
            return new HashMap<>();
        }
        Map<String, String> bizIdMapping = new HashMap<>();
        // 使用虚拟线程池并行处理复制操作
        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (CustomContentPO publishedPO : publishedContentList) {
                futures.add(CompletableFuture.runAsync(() -> {
                    String originalBizId = publishedPO.getBizId();
                    String newBizId = IdentifierUtil.getShortUUID();

                    // 复制COS文件到编写中路径
                    copyNewCustomContent(publishedPO, newBizId);
                    // 设置为编写中状态并生成新的bizId
                    publishedPO.setId(null);
                    publishedPO.setBizId(newBizId);
                    publishedPO.setStatus(CustomContentStatusEnum.EDITING.getCode());
                    publishedPO.setCreateTime(new Date());
                    publishedPO.setUpdateTime(null);
                    publishedPO.setCreateBy(userId);
                    publishedPO.setUpdateBy(null);
                    // 插入新的编写中记录
                    int insertResult = customContentPOMapper.insertSelective(publishedPO);
                    if (insertResult > 0) {
                        synchronized (bizIdMapping) {
                            bizIdMapping.put(originalBizId, newBizId);
                        }
                        log.info("复制已发布内容成功，原bizId: {}, 新bizId: {}", originalBizId, newBizId);
                    } else {
                        log.error("插入复制的内容失败，原bizId: {}, 新bizId: {}", originalBizId, newBizId);
                    }
                }, executor));
            }

            // 等待所有复制任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        }
        log.info("复制已发布内容完成，成功数量: {}, 映射关系: {}", bizIdMapping.size(), bizIdMapping);
        return bizIdMapping;
    }

    /**
     * 为编写中状态复制内容文件到COS
     *
     * @param publishedPO 已发布内容PO
     * @param newBizId 新的业务ID
     */
    private void copyNewCustomContent(CustomContentPO publishedPO, String newBizId) {
        String originalBizId = publishedPO.getBizId();
        Date now = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(now);

        // 复制content文件
        if (StringUtils.isNotBlank(publishedPO.getContent()) && !JSON.isValid(publishedPO.getContent())) {
            String editingContentPath = String.format("customContent/editing/%s/%s/content.json", newBizId, timestamp);
            String editingContentUrl = cosUtil.copyObject(publishedPO.getContent(), editingContentPath);
            publishedPO.setContent(editingContentUrl);
            log.debug("复制content文件到编写中路径成功，原bizId: {}, 新bizId: {}, 新URL: {}", originalBizId, newBizId, editingContentUrl);
        }

        // 复制studentContent文件
        if (StringUtils.isNotBlank(publishedPO.getStudentContent()) && !JSON.isValid(publishedPO.getStudentContent())) {
            String editingStudentContentPath = String.format("customContent/editing/%s/%s/studentContent.json", newBizId, timestamp);
            String editingStudentContentUrl = cosUtil.copyObject(publishedPO.getStudentContent(), editingStudentContentPath);
            publishedPO.setStudentContent(editingStudentContentUrl);
            log.debug("复制studentContent文件到编写中路径成功，原bizId: {}, 新bizId: {}, 新URL: {}", originalBizId, newBizId, editingStudentContentUrl);
        }
    }
}