package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.CourseKnowledgeInfoMapper;
import com.unipus.digitalbook.dao.CourseKnowledgeSourceInfoMapper;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.service.KnowledgeGraphService;
import com.unipus.digitalbook.service.remote.restful.knowledge.KnowledgeApiService;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgeNode;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeNodeBaseAddRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeNodeDeleteRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeNodeMoveRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeNodeUpdateRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.BaseKnowledgeResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.GraphNodeInfoResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeNodeAddResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeNodeDetailResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 图谱创建相关接口
 */
@Service
@Slf4j
public class KnowledgeGraphServiceImpl implements KnowledgeGraphService {

    @Resource
    KnowledgeApiService knowledgeApiService;

    @Resource
    CourseKnowledgeInfoMapper courseKnowledgeInfoMapper;

    @Resource
    CourseKnowledgeSourceInfoMapper courseKnowledgeSourceInfoMapper;

    @Override
    public Response<KnowledgeNodeAddResponse> knowledgeBaseNodeAdd(KnowledgeNodeBaseAddRequest params) {
        BaseKnowledgeResponse<KnowledgeNodeAddResponse> remoteRes = knowledgeApiService.knowledgeNodeAdd(params);
        return Response.success(remoteRes.getResult());
    }

    @Override
    public Response<GraphNodeInfoResponse> knowledgeGraphNodeList(String graphId) {
        BaseKnowledgeResponse<GraphNodeInfoResponse> remoteRes = knowledgeApiService.knowledgeGraphNodeList(graphId);

        return Response.success(remoteRes.getResult());
    }

    @Override
    public Response knowledgeBaseNodeUpdate(KnowledgeNodeUpdateRequest params) {
        BaseKnowledgeResponse remoteRes = knowledgeApiService.knowledgeNodeSimpleUpdate(params);
        return Response.success();
    }

    @Override
    public Response<String> knowledgeNodeSameLevelAdd(KnowledgeNode params) {
        BaseKnowledgeResponse<String> remoteRes = knowledgeApiService.knowledgeNodeSameLevelAdd(params);
        return Response.success(remoteRes.getResult());
    }

    @Override
    public Response<String> knowledgeSubNodeAdd(KnowledgeNode params) {
        BaseKnowledgeResponse<String> remoteRes = knowledgeApiService.knowledgeNodeSubLevelAdd(params);
        return Response.success(remoteRes.getResult());
    }

    @Override
    public Response knowledgeNodeUpdate(KnowledgeNode params) {
        knowledgeApiService.knowledgeNodeUpdate(params);
        return Response.success();
    }

    @Override
    public Response<String> knowledgeNodeMove(KnowledgeNodeMoveRequest params) {
        BaseKnowledgeResponse<String> remoteRes = knowledgeApiService.knowledgeNodeMove(params);
        return Response.success(remoteRes.getResult());

    }

    @Override
    public Response knowledgeNodeDelete(KnowledgeNodeDeleteRequest params) {

        BaseKnowledgeResponse remoteRes = knowledgeApiService.knowledgeNodeDelete(params);
        return Response.success();
    }

    @Override
    public Response<KnowledgeNodeDetailResponse> knowledgeNodeDetail(String nodeId) {
        BaseKnowledgeResponse<KnowledgeNodeDetailResponse> remoteRes = knowledgeApiService.knowledgeNodeDetail(nodeId);
        return Response.success(remoteRes.getResult());
    }
}
