package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.CourseKnowledgeInfoMapper;
import com.unipus.digitalbook.dao.CourseKnowledgeSourceInfoMapper;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.service.KnowledgeRelationService;
import com.unipus.digitalbook.service.remote.restful.knowledge.KnowledgeApiService;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeRelationAddRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeRelationUpdateRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.BaseKnowledgeResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeRelationQueryResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 图谱创建相关接口
 */
@Service
@Slf4j
public class KnowledgeRelationServiceImpl implements KnowledgeRelationService {

    @Resource
    KnowledgeApiService knowledgeApiService;

    @Resource
    CourseKnowledgeInfoMapper courseKnowledgeInfoMapper;

    @Resource
    CourseKnowledgeSourceInfoMapper courseKnowledgeSourceInfoMapper;

    @Override
    public Response relationAdd(KnowledgeRelationAddRequest params) {
        BaseKnowledgeResponse<String> remoteRes = knowledgeApiService.relationAdd(params);
        return Response.success(remoteRes.getResult());
    }

    @Override
    public Response relationUpdate(KnowledgeRelationUpdateRequest params) {
        knowledgeApiService.relationUpdate(params);
        return Response.success();
    }

    @Override
    public Response relationDelete(String relationId) {
        knowledgeApiService.relationDelete(relationId);
        return Response.success();
    }

    @Override
    public Response<KnowledgeRelationQueryResponse> relationQuery(String query, String graphId) {
        BaseKnowledgeResponse<KnowledgeRelationQueryResponse> remoteRes = knowledgeApiService.relationQuery(query, graphId);
        return Response.success(remoteRes.getResult());
    }

}
