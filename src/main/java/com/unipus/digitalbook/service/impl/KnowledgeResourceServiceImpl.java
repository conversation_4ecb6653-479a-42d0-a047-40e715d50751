package com.unipus.digitalbook.service.impl;

import com.alibaba.fastjson.JSON;
import com.unipus.digitalbook.common.exception.knowledge.KnowledgeResourceExistException;
import com.unipus.digitalbook.common.exception.knowledge.KnowledgeResourceUrlNullException;
import com.unipus.digitalbook.dao.CourseKnowledgeSourceInfoMapper;
import com.unipus.digitalbook.model.dto.knowledge.CourseKnowledgeSourceInfoDTO;
import com.unipus.digitalbook.model.dto.knowledge.CourseKnowledgeSourceInfoGroupDTO;
import com.unipus.digitalbook.model.dto.knowledge.KnowledgeSourceCheckDTO;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.enums.KnowledgeSourceDeleteEnum;
import com.unipus.digitalbook.model.enums.KnowledgeSourceStatusEnum;
import com.unipus.digitalbook.model.params.knowledge.*;
import com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoExistPO;
import com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO;
import com.unipus.digitalbook.service.KnowledgeResourceService;
import com.unipus.digitalbook.service.UserService;
import com.unipus.digitalbook.service.remote.restful.knowledge.KnowledgeApiService;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeResourceAddRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeResourceListDetailRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeResourceUpdateRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.BaseKnowledgeResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeResourceDetailResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 图谱资源创建相关接口
 */
@Service
@Slf4j
public class KnowledgeResourceServiceImpl implements KnowledgeResourceService {

    @Resource
    KnowledgeApiService knowledgeApiService;

    @Resource
    CourseKnowledgeSourceInfoMapper courseKnowledgeSourceInfoMapper;

    @Resource
    UserService userService;

    @Value("${remote.knowledge.resourceLinkUrl}")
    private String sourceLinkBaseUrl;

    private String sourceLinkUrlStr = "%s?path=%s&primaryKey=%s&source=%s";

    @Override
    public String knowledgeSourceAdd(KnowledgeSourceAddParam params, Long opUserId) {
        //校验待处理的url是否已经标注，如果标注则抛出存在异常～
        List<String> pendingUrls = new ArrayList<>();
        pendingUrls.add(params.getSourceUrl());
        if (!CollectionUtils.isEmpty(params.getSubSourceUrls())) {
            pendingUrls.addAll(params.getSubSourceUrls());
        }
        checkUrlsExistThenThrow(pendingUrls, params.getCourseIdStr(),
                params.getUnitId(), params.getTaskId(), params.getType(), params.getCourseKnowledgeId(), params.getKnowledgeId(), null);

        /**
         * 1、调用三方接口，上传资源，生成资源Id
         * 2、添加知识图谱节点数据
         */
        String sourceUrl = String.format(sourceLinkUrlStr, sourceLinkBaseUrl, params.getSourceUrl(), params.getCourseIdStr(),params.getSource());
        KnowledgeResourceAddRequest knowledgeResourceAddRequest = params.toThirdKnowledgeAddRequest(sourceUrl);
        log.info("third request : {}", JSON.toJSONString(knowledgeResourceAddRequest));
        //图谱返回的资源Id
        BaseKnowledgeResponse<String> knowledgeResourceId = knowledgeApiService.addResource(knowledgeResourceAddRequest);
        CourseKnowledgeSourceInfoPO mainSourceInfoPO = params.toPo(knowledgeResourceId.getResult(), opUserId);

        courseKnowledgeSourceInfoMapper.insertSelective(mainSourceInfoPO);

        //如果打标是组，则生成子数据
        if (params.groupFlag()) {
            List<CourseKnowledgeSourceInfoPO> subPoList = params.toSubPo(mainSourceInfoPO);
            courseKnowledgeSourceInfoMapper.batchInsertSelective(subPoList);
        }
        return mainSourceInfoPO.getId() + "";
    }

    @Override
    public void knowledgeSourceUpdate(KnowledgeSourceUpdateParam params, Long opUserId) {

        KnowledgeResourceUpdateRequest updateRequest = params.toThirdKnowledgeResourceUpdateRequest();
        log.info("third request : {}", JSON.toJSONString(updateRequest));
        knowledgeApiService.updateResource(updateRequest);

        //更新本地数据
        List<CourseKnowledgeSourceInfoPO> pendingUpdateList = params.toPo(opUserId);
        for (CourseKnowledgeSourceInfoPO updatePO : pendingUpdateList) {
            courseKnowledgeSourceInfoMapper.updateByPrimaryKeySelective(updatePO);
        }
    }

    @Override
    public void knowledgeSourceDisable(KnowledgeSourceIdParam param, Long opUserId) {
        CourseKnowledgeSourceInfoPO sourceInfoPO = new CourseKnowledgeSourceInfoPO();
        sourceInfoPO.setId(param.getId());
        sourceInfoPO.setEnableStatus(KnowledgeSourceStatusEnum.DISABLE.getCode());
        sourceInfoPO.setUpdateBy(opUserId);
        sourceInfoPO.setUpdateTime(new Date());
        courseKnowledgeSourceInfoMapper.updateEnableStatusAllSourceInfoById(sourceInfoPO);
    }

    @Override
    public void knowledgeSourceDirUpdate(KnowledgeSourceDirUpdateParam param, Long opUserId) {
        KnowledgeResourceUpdateRequest updateRequest = param.toThirdKnowledgeResourceUpdateRequest();
        log.info("third request : {}", JSON.toJSONString(updateRequest));
        knowledgeApiService.updateResource(updateRequest);

        CourseKnowledgeSourceInfoPO mainPo = param.toPo(opUserId);
        courseKnowledgeSourceInfoMapper.updateDirAllSourceInfoById(mainPo);
    }

    @Override
    public void knowledgeSourceDelete(KnowledgeSourceDeleteParam params, Long opUserId) {
        //删除三方资源Id
        knowledgeApiService.deleteResource(params.getThirdResourceId(), params.getKnowledgeId());

        CourseKnowledgeSourceInfoPO sourceInfoPO = new CourseKnowledgeSourceInfoPO();
        sourceInfoPO.setId(params.getCourseResourceId());
        sourceInfoPO.setDeleteStatus(KnowledgeSourceDeleteEnum.DELETE.getCode());
        sourceInfoPO.setUpdateBy(opUserId);
        sourceInfoPO.setUpdateTime(new Date());
        //删除跟主键Id的本地所有资源
        courseKnowledgeSourceInfoMapper.deleteAllSourceInfoById(sourceInfoPO);
    }

    @Override
    public void knowledgeSourceBatchDelete(KnowledgeSourceBatchDeleteParam param, Long opUserId) {
        List<KnowledgeSourceBatchDeleteParam.BatchIds> pendingDeleteIdInfos = param.getDeleteIds();
        for (KnowledgeSourceBatchDeleteParam.BatchIds pendingDeleteIdInfo : pendingDeleteIdInfos) {
            if (null == pendingDeleteIdInfo.getCourseResourceId() || StringUtils.isEmpty(pendingDeleteIdInfo.getThirdResourceId())) {
                continue;
            }
            KnowledgeSourceDeleteParam deleteParam = new KnowledgeSourceDeleteParam();
            deleteParam.setKnowledgeId(param.getKnowledgeId());
            deleteParam.setCourseResourceId(pendingDeleteIdInfo.getCourseResourceId());
            deleteParam.setThirdResourceId(pendingDeleteIdInfo.getThirdResourceId());
            this.knowledgeSourceDelete(deleteParam, opUserId);
        }
    }

    @Override
    public void knowledgeSourceDeleteByThirdIds(KnowledgeSourceDeleteByThirdIdsParam param, Long opUserId) {
        log.warn("知识点被删除，级联下方的所有资源被删除～");
        courseKnowledgeSourceInfoMapper.deleteAllSourceInfoByThirdIds(param.getThirdResourceIds(), opUserId, param.getKnowledgeId());
    }

    @Override
    public List<CourseKnowledgeSourceInfoDTO> knowledgeSourceQuery(KnowledgeSourceQueryParam param, Long opUserId) {
        List<CourseKnowledgeSourceInfoDTO> result = new ArrayList<>();

        CourseKnowledgeSourceInfoPO queryPO = param.toQueryPO();
        List<CourseKnowledgeSourceInfoPO> infoPOList = courseKnowledgeSourceInfoMapper.selectSelective(queryPO);
        if (CollectionUtils.isEmpty(infoPOList)) {
            //根据条件没有查到资源数据,直接返回
            return result;
        }

        List<CourseKnowledgeSourceInfoPO> mainInfoList = new ArrayList<>();
        List<CourseKnowledgeSourceInfoPO> subInfoList = new ArrayList<>();
        Set<String> knowledgeSourceIdSet = new HashSet<>();

        for (CourseKnowledgeSourceInfoPO sourceInfoPO : infoPOList) {
            if (null == sourceInfoPO.getParentId()) {
                mainInfoList.add(sourceInfoPO);
                knowledgeSourceIdSet.add(sourceInfoPO.getKnowledgeSourceId());
            } else {
                subInfoList.add(sourceInfoPO);
            }
        }

        if (knowledgeSourceIdSet.isEmpty()) {
            log.warn("三方资源Id为空，数据不正常，请排查数据");
            //暂时没有资源生成,直接返回
            return result;
        }
        //组内子资源map
        Map<Long, List<CourseKnowledgeSourceInfoPO>> subInfoMap = new HashMap<>();
        if (!subInfoList.isEmpty()) {
            //子资源map数据
            subInfoMap =
                    subInfoList.stream().collect(Collectors.groupingBy(CourseKnowledgeSourceInfoPO::getParentId));
        }

        //三方获取知识资源信息
        Map<String, KnowledgeResourceDetailResponse> resourceListDetailMap = getKnowledgeResourceDetailResponseMap(knowledgeSourceIdSet);

        //组装返回值
        for (CourseKnowledgeSourceInfoPO sourceInfoPO : mainInfoList) {
            Long lastUpdateUserId = null;
            //云知声返回的图谱资源Id
            String knowledgeSourceId = sourceInfoPO.getKnowledgeSourceId();
            if (!resourceListDetailMap.containsKey(knowledgeSourceId)) {
                log.warn("knowledgeSourceId is not exist in third platform:{},courseKnowledgeId:{}", knowledgeSourceId, sourceInfoPO.getId());
                continue;
            }
            KnowledgeResourceDetailResponse resourceDetailResponse = resourceListDetailMap.get(knowledgeSourceId);

            CourseKnowledgeSourceInfoDTO sourceInfoDTO = new CourseKnowledgeSourceInfoDTO();
            sourceInfoDTO.setCourseKnowledgeId(sourceInfoPO.getCourseKnowledgeId());
            sourceInfoDTO.setCourseResourceId(sourceInfoPO.getId());
            sourceInfoDTO.setThirdResourceId(resourceDetailResponse.getId());
            sourceInfoDTO.setName(resourceDetailResponse.getName());
            sourceInfoDTO.setType(sourceInfoPO.getType()+"");
            sourceInfoDTO.setUrl(resourceDetailResponse.getUrl());
            sourceInfoDTO.setDir(resourceDetailResponse.getDir());
            sourceInfoDTO.setDescription(resourceDetailResponse.getDescription());
            sourceInfoDTO.setNodes(resourceDetailResponse.getNodes());
            sourceInfoDTO.setLabels(resourceDetailResponse.getLabels());
            sourceInfoDTO.setMainSourceUrl(sourceInfoPO.getSourceUrl());
            sourceInfoDTO.setEnableStatus(sourceInfoPO.getEnableStatus());
            sourceInfoDTO.setStartTime(sourceInfoPO.getStartTime());
            sourceInfoDTO.setStartPictureUrl(sourceInfoPO.getStartPictureUrl());
            sourceInfoDTO.setCreateTime(sourceInfoPO.getCreateTime().getTime());
            sourceInfoDTO.setMultimediaKey(sourceInfoPO.getMultimediaKey());
            sourceInfoDTO.setMultimediaIndex(sourceInfoPO.getMultimediaIndex());
            sourceInfoDTO.setMultimediaName(sourceInfoPO.getMultimediaName());
            sourceInfoDTO.setLocation(sourceInfoPO.getLocation());
            lastUpdateUserId = sourceInfoPO.getUpdateBy();

            //如果是组的话，组内资源也需要组装
            if (subInfoMap.containsKey(sourceInfoPO.getId())) {
                List<CourseKnowledgeSourceInfoPO> groupInnerInfo = subInfoMap.get(sourceInfoPO.getId());
                List<CourseKnowledgeSourceInfoDTO.SubSourceInfo> subSourceInfos = new ArrayList<>();
                sourceInfoDTO.setSubSourceList(subSourceInfos);
                for (CourseKnowledgeSourceInfoPO innerInfo : groupInnerInfo) {
                    CourseKnowledgeSourceInfoDTO.SubSourceInfo subSourceInfo = new CourseKnowledgeSourceInfoDTO.SubSourceInfo();
                    subSourceInfo.setId(innerInfo.getId());
                    subSourceInfo.setSourceUrl(innerInfo.getSourceUrl());
                    subSourceInfo.setEnableStatus(innerInfo.getEnableStatus());
                    if (innerInfo.getUpdateTime().getTime() > sourceInfoPO.getUpdateTime().getTime()) {
                        lastUpdateUserId = innerInfo.getUpdateBy();
                    }
                    subSourceInfos.add(subSourceInfo);
                }
            }

            //设置最后更新人
            UserInfo userInfo = userService.getUserInfo(lastUpdateUserId);
            sourceInfoDTO.setUserName(userInfo.getName());
            sourceInfoDTO.setAvatarUrl(userInfo.getAvatarUrl());
            sourceInfoDTO.setSsoId(userInfo.getSsoId());
            //设置数据
            result.add(sourceInfoDTO);
        }

        return result;
    }

    @Override
    public List<CourseKnowledgeSourceInfoGroupDTO> knowledgeSourceQueryGroup(KnowledgeSourceQueryGroupParam param, Long opUserId) {
        List<CourseKnowledgeSourceInfoGroupDTO> result = new ArrayList<>();

        KnowledgeSourceQueryParam queryParam = new KnowledgeSourceQueryParam();
        BeanUtils.copyProperties(param, queryParam);
        List<CourseKnowledgeSourceInfoDTO> courseKnowledgeSourceInfoDTOS = this.knowledgeSourceQuery(queryParam, opUserId);
        Map<String, List<CourseKnowledgeSourceInfoDTO>> groupInfoByTypeMap = courseKnowledgeSourceInfoDTOS.stream().collect(Collectors.groupingBy(CourseKnowledgeSourceInfoDTO::getMultimediaKey));
        if (CollectionUtils.isEmpty(groupInfoByTypeMap)) {
            return result;
        }

        groupInfoByTypeMap.forEach((multimediaKey, groupInfoList) -> {
            List<CourseKnowledgeSourceInfoDTO> sortedList = groupInfoList.stream().sorted(Comparator.comparing(CourseKnowledgeSourceInfoDTO::getMultimediaName)).collect(Collectors.toList());
            CourseKnowledgeSourceInfoDTO lastInfo = sortedList.getLast();
            CourseKnowledgeSourceInfoGroupDTO groupDTO = new CourseKnowledgeSourceInfoGroupDTO();
            groupDTO.setUserName(lastInfo.getUserName());
            groupDTO.setAvatarUrl(lastInfo.getAvatarUrl());
            groupDTO.setCreateTime(lastInfo.getCreateTime());
            groupDTO.setDir(lastInfo.getDir());
            groupDTO.setMultimediaKey(multimediaKey);
            groupDTO.setType(lastInfo.getType());
            groupDTO.setCourseKnowledgeSourceInfoDTOs(sortedList);
            groupDTO.setLocation(lastInfo.getLocation());
            result.add(groupDTO);
        });

        //根据文章的绝对的位置，做最后的排序
        result.sort(Comparator.comparing(CourseKnowledgeSourceInfoGroupDTO::getLocation));

        return result;
    }

    @Override
    public KnowledgeSourceCheckDTO check(KnowledgeSourceCheckParam param, Long opUserId) {

        List<CourseKnowledgeSourceInfoPO> existSourceUrls = checkUrlsExistThenReturnInfos(param.getPendingUrls(), param.getCourseIdStr(),
                param.getUnitId(), param.getTaskId(), param.getType(), param.getCourseKnowledgeId(), param.getKnowledgeId(), param.getIgnoreIds());

        KnowledgeSourceCheckDTO result = new KnowledgeSourceCheckDTO();
        if (CollectionUtils.isEmpty(existSourceUrls)) {
            return result;
        }
        //有存在的url，不可再次添加
        result.setEffectiveStatus(false);
        result.setInfoList(existSourceUrls);
        return result;
    }

    private Map<String, KnowledgeResourceDetailResponse> getKnowledgeResourceDetailResponseMap(Set<String> knowledgeSourceIdSet) {
        Map<String, KnowledgeResourceDetailResponse> resourceListDetailMap = new HashMap<>();
        try {
            KnowledgeResourceListDetailRequest resourceListDetailRequest = new KnowledgeResourceListDetailRequest();
            resourceListDetailRequest.setIds(new ArrayList<>(knowledgeSourceIdSet));
            log.info("third request : {}", JSON.toJSONString(resourceListDetailRequest));
            BaseKnowledgeResponse<Map<String, KnowledgeResourceDetailResponse>> resourceListDetailResponse = knowledgeApiService.getResourceListDetail(resourceListDetailRequest);
            if (null != resourceListDetailResponse) {
                return resourceListDetailResponse.getResult();
            }
        } catch (Exception e) {
            log.error("getKnowledgeResourceDetailResponseMap error", e);
        }

        return resourceListDetailMap;
    }

    /**
     * @param pendingUrls
     * @param courseIdStr
     * @param unitId
     * @param taskId
     * @param type
     * @param courseKnowledgeId
     * @param knowledgeId
     */
    private void checkUrlsExistThenThrow(List<String> pendingUrls,
                                         String courseIdStr,
                                         String unitId,
                                         String taskId,
                                         Integer type,
                                         Long courseKnowledgeId,
                                         String knowledgeId,
                                         List<Long> ignoreIds) {
        if (CollectionUtils.isEmpty(pendingUrls)) {
            throw new KnowledgeResourceUrlNullException();
        }

        List<String> pendingUrlHashes = pendingUrls
                .stream().map(url -> DigestUtils.md5Hex(url)).collect(Collectors.toList());
        CourseKnowledgeSourceInfoExistPO selectExistParams = new CourseKnowledgeSourceInfoExistPO();
        selectExistParams.setCourseIdStr(courseIdStr);
        selectExistParams.setUnitId(unitId);
        selectExistParams.setTaskId(taskId);
        selectExistParams.setType(type);
        selectExistParams.setCourseKnowledgeId(courseKnowledgeId);
        selectExistParams.setKnowledgeId(knowledgeId);
        selectExistParams.setSourceHashUrls(pendingUrlHashes);
        if (!CollectionUtils.isEmpty(ignoreIds)) {
            selectExistParams.setIgnoreIds(ignoreIds);
        }
        List<CourseKnowledgeSourceInfoPO> existSourceUrls = courseKnowledgeSourceInfoMapper.selectExistSourceUrls(selectExistParams);
        if (!CollectionUtils.isEmpty(existSourceUrls)) {
            throw new KnowledgeResourceExistException(existSourceUrls.getFirst().getMultimediaKey());
        }
    }

    /**
     * @param pendingUrls
     * @param courseIdStr
     * @param unitId
     * @param taskId
     * @param type
     * @param courseKnowledgeId
     * @param knowledgeId
     */
    private List<CourseKnowledgeSourceInfoPO> checkUrlsExistThenReturnInfos(List<String> pendingUrls,
                                                                            String courseIdStr,
                                                                            String unitId,
                                                                            String taskId,
                                                                            Integer type,
                                                                            Long courseKnowledgeId,
                                                                            String knowledgeId,
                                                                            List<Long> ignoreIds) {
        if (CollectionUtils.isEmpty(pendingUrls)) {
            throw new KnowledgeResourceUrlNullException();
        }


        List<String> pendingUrlHashes = pendingUrls
                .stream().map(url -> DigestUtils.md5Hex(url)).collect(Collectors.toList());
        CourseKnowledgeSourceInfoExistPO selectExistParams = new CourseKnowledgeSourceInfoExistPO();
        selectExistParams.setCourseIdStr(courseIdStr);
        selectExistParams.setUnitId(unitId);
        selectExistParams.setTaskId(taskId);
        selectExistParams.setType(type);
        selectExistParams.setCourseKnowledgeId(courseKnowledgeId);
        selectExistParams.setKnowledgeId(knowledgeId);
        selectExistParams.setSourceHashUrls(pendingUrlHashes);
        if (!CollectionUtils.isEmpty(ignoreIds)) {
            selectExistParams.setIgnoreIds(ignoreIds);
        }
        List<CourseKnowledgeSourceInfoPO> existSourceUrls = courseKnowledgeSourceInfoMapper.selectExistSourceUrls(selectExistParams);
        return CollectionUtils.isEmpty(existSourceUrls) ? new ArrayList<>() : existSourceUrls;
    }

    public static void main(String[] args) {
        String sourceUrl = String.format("%s?path=%s&cmsCourseId=%s", "sourceLinkBaseUrl", "params.getSourceUrl()", "params.getCourseIdStr()");
        System.out.println(sourceUrl);

    }
}
