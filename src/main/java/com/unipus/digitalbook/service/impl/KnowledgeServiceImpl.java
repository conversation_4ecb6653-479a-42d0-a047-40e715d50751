package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.knowledge.KnowledgeCreateException;
import com.unipus.digitalbook.common.exception.knowledge.KnowledgeNotExistException;
import com.unipus.digitalbook.dao.CourseKnowledgeInfoMapper;
import com.unipus.digitalbook.model.dto.DataListDTO;
import com.unipus.digitalbook.model.dto.knowledge.CourseKnowledgeInfoGetDTO;
import com.unipus.digitalbook.model.enums.DeleteEnum;
import com.unipus.digitalbook.model.enums.KnowledgeStatusEnum;
import com.unipus.digitalbook.model.params.knowledge.KnowledgeAddParam;
import com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeInfoPO;
import com.unipus.digitalbook.service.KnowledgeService;
import com.unipus.digitalbook.service.remote.restful.knowledge.KnowledgeApiService;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.Knowledge;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgePrimaryIdRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgeTag;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeAddRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgePublishRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeUpdateRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.BaseKnowledgeResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeResourceNodeResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.ResourceLabelResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 图谱创建相关接口
 */
@Service
@Slf4j
public class KnowledgeServiceImpl implements KnowledgeService {

    @Resource
    KnowledgeApiService knowledgeApiService;

    @Resource
    CourseKnowledgeInfoMapper courseKnowledgeInfoMapper;

    @Override
    public CourseKnowledgeInfoGetDTO courseKnowledgeInfoGet(String courseIdStr, Integer publishedStatus) {
        CourseKnowledgeInfoPO courseKnowledgeInfoPO = courseKnowledgeInfoMapper.selectByCourseIdStr(courseIdStr);
        if (null == courseKnowledgeInfoPO) {
            throw new KnowledgeNotExistException();
        }

        String knowledgeId = courseKnowledgeInfoPO.getKnowledgeId();
        BaseKnowledgeResponse<Knowledge> remoteRes = knowledgeApiService.knowledgeGet(knowledgeId);
        Knowledge knowledge = remoteRes.getResult();
        CourseKnowledgeInfoGetDTO response = new CourseKnowledgeInfoGetDTO();
        knowledge.setCourseKnowledgeId(courseKnowledgeInfoPO.getId());
        response.setKnowledge(knowledge);
        return response;

    }

    @Override
    public CourseKnowledgeInfoPO knowledgeAdd(KnowledgeAddParam params, Long opUserId) {
        //如果数据已经存在，则直接返回
        CourseKnowledgeInfoPO courseKnowledgeInfoPO = courseKnowledgeInfoMapper.selectByCourseIdStr(params.getCourseIdStr());
        if (null != courseKnowledgeInfoPO) {
            log.warn("数据已存在，不可多次添加");
            return courseKnowledgeInfoPO;
        }

        KnowledgeAddRequest knowledgeAddRequest = params.toKnowledgeAddRequest();
        BaseKnowledgeResponse<String> result = knowledgeApiService.knowledgeAdd(knowledgeAddRequest);
        //三方接口返回的知识图谱Id
        String knowledgeId = result.getResult();
        if (StringUtils.isBlank(knowledgeId)) {
            throw new KnowledgeCreateException();
        }

        CourseKnowledgeInfoPO insertKnowledgeInfo = new CourseKnowledgeInfoPO();
        insertKnowledgeInfo.setCourseIdStr(params.getCourseIdStr());
        insertKnowledgeInfo.setName(params.getName());
        insertKnowledgeInfo.setDescription(params.getBackground());
        insertKnowledgeInfo.setKnowledgeId(knowledgeId);
        insertKnowledgeInfo.setCreateBy(opUserId);
        insertKnowledgeInfo.setUpdateBy(opUserId);
        insertKnowledgeInfo.setCreateTime(new Date());
        insertKnowledgeInfo.setUpdateTime(new Date());
        courseKnowledgeInfoMapper.insertSelective(insertKnowledgeInfo);
        return insertKnowledgeInfo;
    }

    @Override
    public void knowledgeUpdate(KnowledgeUpdateRequest params, Long opUserId) {
        knowledgeApiService.knowledgeUpdate(params);

        CourseKnowledgeInfoPO updateKnowledgeInfo = new CourseKnowledgeInfoPO();
        updateKnowledgeInfo.setId(params.getId());
        updateKnowledgeInfo.setName(params.getName());
        updateKnowledgeInfo.setDescription(params.getDescription());
        updateKnowledgeInfo.setKnowledgeId(params.getKnowledgeId());
        updateKnowledgeInfo.setUpdateBy(opUserId);
        courseKnowledgeInfoMapper.updateByPrimaryKeySelective(updateKnowledgeInfo);
    }

    @Override
    public void knowledgeDelete(KnowledgePrimaryIdRequest params, Long opUserId) {
        knowledgeApiService.knowledgeDelete(params.getKnowledgeId());

        CourseKnowledgeInfoPO updateInfo = new CourseKnowledgeInfoPO();
        updateInfo.setDeleteStatus(DeleteEnum.DELETE.getCode());
        updateInfo.setUpdateTime(new Date());
        updateInfo.setUpdateBy(opUserId);
        courseKnowledgeInfoMapper.updateByPrimaryKeySelective(updateInfo);
    }

    @Override
    public Knowledge knowledgeGet(String knowledgeId) {
        CourseKnowledgeInfoPO knowledgeGetInfo = new CourseKnowledgeInfoPO();
        knowledgeGetInfo.setKnowledgeId(knowledgeId);
        CourseKnowledgeInfoPO courseKnowledgeInfoPO = courseKnowledgeInfoMapper.selectBySelective(knowledgeGetInfo);
        if (null == courseKnowledgeInfoPO) {
            throw new KnowledgeNotExistException();
        }

        BaseKnowledgeResponse<Knowledge> remoteRes = knowledgeApiService.knowledgeGet(knowledgeId);
        if (null == remoteRes || null == remoteRes.getResult()) {
            log.warn("ipub库存在知识图谱节点，但三方图谱数据已找不到，可能删除！");
            throw new KnowledgeNotExistException();
        }

        Knowledge remoteResResult = remoteRes.getResult();
        remoteResResult.setCourseKnowledgeId(courseKnowledgeInfoPO.getId());
        return remoteResResult;
    }

    @Override
    public void knowledgePublish(KnowledgePrimaryIdRequest params, Long opUserId) {
        KnowledgePublishRequest request = new KnowledgePublishRequest();
        request.setKnowledgeId(params.getKnowledgeId());
        request.setDescription(params.getDescription());
        knowledgeApiService.knowledgePublish(request);

        CourseKnowledgeInfoPO updateInfo = new CourseKnowledgeInfoPO();
        updateInfo.setUpdateTime(new Date());
        updateInfo.setUpdateBy(opUserId);
        updateInfo.setDescription(KnowledgeStatusEnum.PUBLISHED.getDesc());
        courseKnowledgeInfoMapper.updateByPrimaryKey(updateInfo);
    }

    @Override
    public String knowledgeFileUpload(MultipartFile file) {
        BaseKnowledgeResponse<String> remoteRes = knowledgeApiService.knowledgeFileUpload(file);
        return remoteRes.getResult();
    }

    @Override
    public DataListDTO<Knowledge> knowledgeList(String keyword) {
        BaseKnowledgeResponse<List<Knowledge>> remoteRes = knowledgeApiService.knowledgeList(keyword);
        return new DataListDTO<>(remoteRes.getResult());
    }

    @Override
    public DataListDTO<Knowledge> knowledgeGraphSearch(String keyword) {
        BaseKnowledgeResponse<List<Knowledge>> remoteRes = knowledgeApiService.knowledgeGraphList(keyword);
        return new DataListDTO<>(remoteRes.getResult());
    }

    @Override
    public List<KnowledgeResourceNodeResponse.ResourceNode> getKnowledgeResourceNodeDetail(String knowledgeId) {
        List<KnowledgeResourceNodeResponse.ResourceNode>  result = new ArrayList<>();

        BaseKnowledgeResponse<List<KnowledgeResourceNodeResponse>> remoteRes = knowledgeApiService.getKnowledgeResourceNodeDetail(knowledgeId);
        if (null == remoteRes || CollectionUtils.isEmpty(remoteRes.getResult())) {
            return result;
        }

        List<KnowledgeResourceNodeResponse> remoteResResult = remoteRes.getResult();
        for (KnowledgeResourceNodeResponse knowledgeResourceNodeResponse : remoteResResult) {
            KnowledgeResourceNodeResponse.ResourceNode dto = new KnowledgeResourceNodeResponse.ResourceNode();
            dto.setId(knowledgeResourceNodeResponse.getGraphId());
            dto.setName(knowledgeResourceNodeResponse.getName());
            dto.setChildren(knowledgeResourceNodeResponse.getNodes());
            result.add(dto);
        }
        return result;
    }

    @Override
    public List<KnowledgeTag> getResourceLabelDetail() {
        List<KnowledgeTag> knowledgeTags = new ArrayList<>();

        BaseKnowledgeResponse<List<ResourceLabelResponse>> remoteRes = knowledgeApiService.getResourceLabelDetail();
        List<ResourceLabelResponse> resourcelabelList = remoteRes.getResult();
        if (CollectionUtils.isEmpty(resourcelabelList)) {
            return knowledgeTags;
        }
        for (ResourceLabelResponse labelResponse : resourcelabelList) {
            KnowledgeTag knowledgeTag = new KnowledgeTag();
            knowledgeTag.setId(labelResponse.getId());
            knowledgeTag.setContent(labelResponse.getType());
            knowledgeTag.setChildren(labelResponse.getLabelSet());
            knowledgeTags.add(knowledgeTag);
        }

        return knowledgeTags;
    }
}
