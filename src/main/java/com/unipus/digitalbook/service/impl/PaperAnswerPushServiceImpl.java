package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.paper.UserPaperSyncInfo;
import com.unipus.digitalbook.model.entity.question.UserAnswerData;
import com.unipus.digitalbook.model.entity.question.UserAnswerNodeData;
import com.unipus.digitalbook.model.enums.LearnTypeEnum;
import com.unipus.digitalbook.service.PaperAnswerPushService;
import com.unipus.digitalbook.service.remote.restful.ucontent.BaseResponse;
import com.unipus.digitalbook.service.remote.restful.ucontent.PushResponseData;
import com.unipus.digitalbook.service.remote.restful.ucontent.UcontentApiService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;

@Service
@Slf4j
public class PaperAnswerPushServiceImpl implements PaperAnswerPushService {
    @Resource
    private UcontentApiService ucontentApiService;

    @Override
    public BaseResponse<PushResponseData> checkUserPaperAnswerByThird(UserPaperSyncInfo userPaperSyncInfo) {
        log.debug("过关率检查入参:{}", JsonUtil.toJsonString(userPaperSyncInfo));
        return doSubmit(toNode(userPaperSyncInfo));
    }

    @Override
    public BaseResponse<PushResponseData> pushUserPaperAnswerToThird(UserPaperSyncInfo userPaperSyncInfo) {
        log.debug("用户试卷作答同步入参:{}", JsonUtil.toJsonString(userPaperSyncInfo));
        return doSubmit(toNode(userPaperSyncInfo));
    }

    public UserAnswerNodeData toNode(UserPaperSyncInfo syncInfo) {
        UserAnswerNodeData node = new UserAnswerNodeData();
        node.setOpenId(syncInfo.getOpenId());
        node.setBookId(syncInfo.getBookId());
        node.setIp(syncInfo.getClientIp());
        node.setLearnType(LearnTypeEnum.PAPER.getCode());
        node.setBookVersionNumber(syncInfo.getBookVersionNumber());
        node.setData(UserAnswerData.buildPaperAnswerData(syncInfo));
        return node;
    }

    // 提交用户作答
    private BaseResponse<PushResponseData> doSubmit(UserAnswerNodeData node) {

        // 预览模式返回Mock数据
        if(isPreviewMode(node)){
            return mockDataForPreviewPaperScoreSubmit();
        }

        BaseResponse<PushResponseData> response;
        try {
            response = ucontentApiService.submit(node);
        } catch (Exception e) {
            log.error("调用第三方服务异常，使用mock数据替代", e);
            throw new IllegalStateException("同步用户作答结果异常");
        }

        if (response == null || !response.isSuccess() || response.getData() == null) {
            // 记录详细的错误信息以便调试
            log.error("同步用户作答结果失败，响应为空或状态不成功，response: {}，node: {}",
                    JsonUtil.toJsonString(response),  JsonUtil.toJsonString(node));
            throw new IllegalStateException("同步用户作答结果失败");
        }
        // 避免打印完整响应对象，防止敏感信息泄露
        log.debug("[同步响应结果]: 状态码={}, 是否通过={}", response.getCode(), response.getData().isPass());
        return response;
    }

    /**
     * 判断用户作答是否为预览模式
     */
    private boolean isPreviewMode(UserAnswerNodeData node) {
        return node == null ||
            node.getOpenId() == null ||
            node.getBookId() == null ||
            node.getBookVersionNumber() == null ||
            IdentifierUtil.DEFAULT_VERSION_NUMBER.equals(node.getBookVersionNumber());
    }

    /**
     * 模拟预览试卷提交
     */
    private BaseResponse<PushResponseData> mockDataForPreviewPaperScoreSubmit() {
        PushResponseData pushResponseData = new PushResponseData();
        // 设置Pass状态: 1(通过)
        pushResponseData.setState(1);
        pushResponseData.setMessage("Mock数据");
        // 设置策略信息
        pushResponseData.setStrategy(JsonUtil.toJsonString(createDefaultStrategyNode()));

        BaseResponse<PushResponseData> response = new BaseResponse<>();
        response.setData(pushResponseData);
        response.setCode(0);
        response.setMessage("Mock成功");

        log.debug("用户作答为预览模式，使用mock数据替代。\n{}", JsonUtil.toJsonString(pushResponseData));
        return response;
    }

    private PushResponseData.StrategyNode createDefaultStrategyNode() {
        PushResponseData.StrategyNode strategyNode = new PushResponseData.StrategyNode();
        long currentEpoch = Instant.now().getEpochSecond();
        strategyNode.setRequired(true);
        // 设置学习开始时间: 现在
        strategyNode.setStartTime(currentEpoch);
        // 设置学习结束时间: 1天后
        strategyNode.setEndTime(currentEpoch + 86400);
        // 设置过关率: 50%
        strategyNode.setTask_mini_score_pct(0.5);
        return strategyNode;
    }

}
