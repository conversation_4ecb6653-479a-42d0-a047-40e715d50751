package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.dao.PaperScoreBatchPOMapper;
import com.unipus.digitalbook.model.entity.UserAccessInfo;
import com.unipus.digitalbook.model.entity.paper.*;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplate;
import com.unipus.digitalbook.model.enums.PaperScoreTemplateTypeEnum;
import com.unipus.digitalbook.model.enums.PaperSubmitStatusEnum;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.enums.UnitTestModeEnum;
import com.unipus.digitalbook.model.params.paper.UserPaperAnswerParam;
import com.unipus.digitalbook.model.po.paper.PaperScoreBatchPO;
import com.unipus.digitalbook.service.PaperAnswerService;
import com.unipus.digitalbook.service.PaperInstanceService;
import com.unipus.digitalbook.service.PaperScoreTemplateRelationService;
import com.unipus.digitalbook.service.PaperService;
import com.unipus.digitalbook.service.factory.paper.answer.PaperAnswerFactory;
import com.unipus.digitalbook.service.factory.paper.answer.PaperAnswerStrategy;
import com.unipus.digitalbook.service.factory.paper.answer.UserAnswerProcessor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 试卷作答记录服务实现类
 */
@Service
@Slf4j
public class PaperAnswerServiceImpl implements PaperAnswerService {
    @Resource
    private PaperService paperService;
    @Resource
    private PaperAnswerFactory paperAnswerFactory;
    @Resource
    private UserAnswerProcessor userAnswerProcessor;
    @Resource
    private PaperInstanceService paperInstanceService;
    @Resource
    private PaperScoreBatchPOMapper paperScoreBatchPOMapper;
    @Resource
    private PaperScoreTemplateRelationService paperScoreTemplateRelationService;

    /**
     * 获取预览作答记录
     *
     * @param param  用户试卷作答记录参数
     * @param userAccessInfo 用户访问信息
     * @return List<UserAnswer> 用户试卷作答记录
     */
    @Override
    public UserPaperAnswer getPreviewPaperAnswer(UserPaperAnswerParam param, UserAccessInfo userAccessInfo) {
        String openId = userAccessInfo.getOpenId();
        Long tenantId = userAccessInfo.getTenantId();
        String envPartition = userAccessInfo.getEnvPartition();

        String instanceId = param.getInstanceId();
        PaperTypeEnum paperType = PaperTypeEnum.getByCode(param.getPaperType());
        // 适用默认版本号
        String versionNumber = IdentifierUtil.DEFAULT_VERSION_NUMBER;
        // 0.用户答题记录
        Map<String, List<UserAnswer>> userAnswersMap = param.toUserAnswerMap(openId, tenantId, envPartition, versionNumber);

        // 1.从缓存中获取试卷实例
        PaperInstance paperInstance = getPaperInstance(instanceId, paperType);
        // 获取大题组列表
        List<BigQuestionGroup> bigQuestionGroups = paperInstance.getBigQuestionGroupList();

        // 2.获取用户答题结果（小题单位分数列表）
        List<UserQuestionScore> userPaperJudgeResult = userAnswerProcessor
                .processAnswers(bigQuestionGroups, userAnswersMap, null);

        // 3.获取用户总分
        BigDecimal userTotalScore = userPaperJudgeResult.stream()
                .map(UserQuestionScore::getUserScore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 4.返回用户答题结果
        return new UserPaperAnswer(instanceId, userTotalScore, bigQuestionGroups);
    }

    /**
     * 保存用户作答记录
     * （仅用于常规卷和诊断卷保存作答记录）
     * @param param  用户试卷作答记录参数
     * @param userAccessInfo 用户访问信息
     * @return UserPaperInfo 用户试卷得分信息
     */
    @Override
    public UserPaperInfo saveUserPaperAnswer(UserPaperAnswerParam param, UserAccessInfo userAccessInfo) {
        String openId = userAccessInfo.getOpenId();
        Long tenantId = userAccessInfo.getTenantId();
        String envPartition = userAccessInfo.getEnvPartition();
        String instanceId = param.getInstanceId();
        PaperTypeEnum paperType = PaperTypeEnum.getByCode(param.getPaperType());
        if(!PaperTypeEnum.REGULAR.match(paperType) && !PaperTypeEnum.DIAGNOSTIC.match(paperType)){
            log.debug("挑战卷不支持保存作答记录");
            throw new IllegalArgumentException("仅常规卷和诊断卷支持保存作答记录");
        }

        // 1.从缓存中获取试卷实例
        PaperInstance paperInstance = getPaperInstance(instanceId, paperType);

        // 2.保存用户题目作答记录（不判分，直接存储用户试卷题目作答记录）
        Map<String, List<UserAnswer>> userAnswersMap = param.toUserAnswerMap(openId, tenantId, envPartition, paperInstance.getVersionNumber());
        userAnswerProcessor.saveAnswers(instanceId, userAnswersMap);

        // 3.取得试卷作答工厂策略
        PaperAnswerStrategy strategy = paperAnswerFactory.createStrategy(paperType);

        // 4.保存用户试卷作答记录
        strategy.saveUserPaperRecord(paperInstance, null);

        // 5.设置返回值
        return new UserPaperInfo(paperInstance);
    }

    /**
     * 获取用户试卷作答记录
     * （仅用于常规卷和诊断卷保存作答记录）
     * @param param  试卷成绩查询参数
     */
    @Override
    public UserPaperAnswer getUserPaperAnswer(QueryPaperAnswer param) {
        String instanceId = param.getInstanceId();
        PaperTypeEnum paperType = param.getPaperType();

        // 1.获取试卷实例
        PaperInstance paperInstance = getPaperInstance(instanceId, paperType);

        // 2.取得试卷作答工厂策略
        PaperAnswerStrategy strategy = paperAnswerFactory.createStrategy(paperType);

        // 2.获取用户作答记录
        return strategy.getUserPaperAnswer(paperInstance);
    }

   /**
     * 提交试卷作答记录
     * @param param 用户试卷作答记录参数
     * @param userAccessInfo 用户访问信息
     * @return UserPaperInfo 用户试卷得分信息
     */
    @Transactional
    @Override
    public UserPaperInfo submitUserPaperAnswer(UserPaperAnswerParam param, UserAccessInfo userAccessInfo) {
        String openId = userAccessInfo.getOpenId();
        Long tenantId = userAccessInfo.getTenantId();
        String envPartition = userAccessInfo.getEnvPartition();
        String instanceId = param.getInstanceId();
        PaperTypeEnum paperType = PaperTypeEnum.getByCode(param.getPaperType());
        UnitTestModeEnum testMode = UnitTestModeEnum.of(param.getTestMode());

        // 1.获取已有的试卷实例
        PaperInstance paperInstance = getPaperInstance(instanceId, paperType);
        String versionNumber = paperInstance.getVersionNumber();
        paperInstance.setRelatedInstanceId(param.getBaseInstanceId());
        paperInstance.setTestMode(testMode);

        // 2.获取试卷答题策略
        PaperAnswerStrategy strategy = paperAnswerFactory.createStrategy(paperType);

        // 3.获取用户试卷成绩
        Map<String, List<UserAnswer>> userAnswersMap = param.toUserAnswerMap(openId, tenantId, envPartition, versionNumber);

        // 4.处理用户作答记录（评分，保存用户作答记录，同时推送除挑战卷以外的成绩到UAI）
        return strategy.submit(paperInstance, userAnswersMap, userAccessInfo);
    }

    /**
     * 提交用户挑战卷成绩
     *
     * @param userPaperInfo  用户试卷成绩提交参数
     * @param userAccessInfo 用户访问信息
     * @return UserPaperInfo 用户试卷得分信息
     */
    @Override
    public UserPaperInfo submitUserChallengeScore(UserPaperInfo userPaperInfo, UserAccessInfo userAccessInfo) {
        String paperId = userPaperInfo.getPaperId();
        String versionNumber = userPaperInfo.getVersionNumber();

        // 1.获取试卷答题策略
        PaperAnswerStrategy strategy = paperAnswerFactory.createStrategy(PaperTypeEnum.CHALLENGE);

        // 2.提交挑战卷最佳成绩（同时同步UAI）
        return strategy.submitBestScore(paperId, versionNumber, userAccessInfo);
    }

    /**
     * 获取用户试卷成绩
     * 查询试卷的用户成绩，可用于判断试卷是否已完成
     * @param userPaperInfo  用户试卷成绩提交参数
     * @return UserPaperInfo 用户试卷得分信息
     */
    @Override
    public UserPaperScore getUserPaperScore(UserPaperInfo userPaperInfo, UserAccessInfo userAccessInfo) {
        String paperId = userPaperInfo.getPaperId();
        String versionNumber = userPaperInfo.getVersionNumber();
        UnitTestModeEnum testMode = userPaperInfo.getTestMode();

        // 1.获取试卷信息
        Paper paper = paperService.getPaperDetail(paperId, versionNumber);

        // 2.获取试卷答题策略
        PaperAnswerStrategy strategy = paperAnswerFactory.createStrategy(paper.getPaperType());

        // 3.返回用户试卷成绩信息
        UserPaperScore userPaperScore = strategy.getUserPaperScore(paperId, versionNumber, userAccessInfo, testMode);

        // 4.设置试卷得分模板详情
        if(userPaperScore!=null) {
            userPaperScore.setMatchedPaperScoreTemplateDetail(getPaperScoreTemplateDetail(paperId, versionNumber));
        }
        log.debug("用户试卷成绩:{}", JsonUtil.toJsonString(userPaperScore));
        return userPaperScore;
    }

    /**
     * 获取试卷得分模板详情
     *
     * @param paperId 试卷ID
     * @return 试卷得分模板详情列表
     */
    private PaperScoreTemplate getPaperScoreTemplateDetail(String paperId, String versionNumber) {
        // 取得试卷关联的教材ID
        Paper paper = paperService.getPaperDetail(paperId, versionNumber);
        PaperScoreTemplateTypeEnum scoreTemplateType = PaperScoreTemplateTypeEnum.getByPaperType(paper.getPaperType());
        if(scoreTemplateType==null){
            log.debug("无对应模板信息:试卷ID={},试卷版本ID={}，试卷类型={}", paperId, versionNumber, paper.getPaperType());
            return null;
        }
        return paperScoreTemplateRelationService.getPaperScoreTemplate(paper.getBookId(), scoreTemplateType);
    }

    /**
     * 获取已有的试卷实例
     * @param instanceId 试卷实例ID/轮次ID
     * @param paperType 试卷类型
     * @return PaperInstance 试卷实例
     */
    private PaperInstance getPaperInstance(String instanceId, PaperTypeEnum paperType) {
        // 获取已有的试卷实例
        PaperInstance paperInstance = paperInstanceService.loadPaperInstance(instanceId, paperType);
        if(paperInstance == null || CollectionUtils.isEmpty(paperInstance.getBigQuestionGroupList())) {
            throw new IllegalArgumentException("试卷实例不存在，实例ID:" + instanceId);
        }
        log.debug("已获取试卷实例:\n{}", JsonUtil.toJsonString(paperInstance));
        return paperInstance;
    }

    /**
     * 获取用户试卷成绩提交记录
     * @param userPaperInfo 用户试卷成绩提交参数
     *   - paperId       试卷ID
     *   - versionNumber 试卷版本
     *   - openId        用户ID
     *   - tenantId      租户ID
     * @return 用户试卷成绩提交列表
     */
    @Override
    public List<PaperScoreHistory> getUserPaperSubmitHistory(UserPaperInfo userPaperInfo) {
        String paperId = userPaperInfo.getPaperId();
        String versionNumber = userPaperInfo.getVersionNumber();
        String openId = userPaperInfo.getOpenId();
        Long tenantId = userPaperInfo.getTenantId();

        // 检索试卷成绩提交记录
        PaperScoreBatchPO condition = new PaperScoreBatchPO(paperId, versionNumber, openId, tenantId,
                PaperSubmitStatusEnum.SUBMITTED.getCode());
        List<PaperScoreBatchPO> paperScoreBatchPOs = paperScoreBatchPOMapper.selectByCondition(condition);
        if(CollectionUtils.isEmpty(paperScoreBatchPOs)){
            log.debug("试卷成绩提交记录不存在，试卷ID:{},试卷版本:{}", paperId, versionNumber);
            return null;
        }
        // 构建用户试卷成绩提交记录
        return paperScoreBatchPOs.stream().map(PaperScoreHistory::new).toList();
    }

}
