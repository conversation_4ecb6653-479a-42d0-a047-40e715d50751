package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.dao.PaperBookRelationPOMapper;
import com.unipus.digitalbook.dao.PaperChapterReferencePOMapper;
import com.unipus.digitalbook.dao.PaperChapterReferenceVersionPOMapper;
import com.unipus.digitalbook.model.entity.paper.PaperReference;
import com.unipus.digitalbook.model.po.paper.PaperBookRelationPO;
import com.unipus.digitalbook.model.po.paper.PaperChapterReferencePO;
import com.unipus.digitalbook.model.po.paper.PaperChapterReferenceVersionPO;
import com.unipus.digitalbook.service.PaperReferenceService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 试卷引用服务实现类
 */
@Service
@Slf4j
public class PaperReferenceServiceImpl implements PaperReferenceService {

    @Resource
    private PaperBookRelationPOMapper paperBookRelationPOMapper;
    @Resource
    private PaperChapterReferencePOMapper paperChapterReferencePOMapper;
    @Resource
    private PaperChapterReferenceVersionPOMapper paperChapterReferenceVersionPOMapper;

    /**
     * 批量保存教材中试卷引用
     * -- 检测引用关系是否存在，存在则更新引用关系，不存在则添加引用关系
     * -- 检查试卷是否存在，不存在则不允许添加引用，抛出异常
     * @param paperReferences 试卷引用对象列表(paperId,bookId,chapterId,position)
     * @param userId 当前用户ID
     */
    @Override
    public void savePaperReference(List<PaperReference> paperReferences, Long userId){
        if (CollectionUtils.isEmpty(paperReferences)) {
            log.debug("试卷引用列表为空");
            return;
        }

        String bookId = paperReferences.getFirst().getBookId();
        String chapterId = paperReferences.getFirst().getChapterId();
        Set<String> paperIds = paperReferences.stream().map(PaperReference::getPaperId).collect(Collectors.toSet());

        // 1. 检查试卷是否存在于教材中
        List<PaperBookRelationPO> validPapers = paperBookRelationPOMapper.selectList(bookId);
        if (CollectionUtils.isEmpty(validPapers)) {
            throw new BizException("教材中未找到任何试卷引用关系");
        }
        Set<String> validPaperIds = validPapers.stream()
                .map(PaperBookRelationPO::getPaperId)
                .collect(Collectors.toSet());

        if (!validPaperIds.containsAll(paperIds)) {
            Set<String> invalidPaperIds = paperIds.stream()
                    .filter(id -> !validPaperIds.contains(id))
                    .collect(Collectors.toSet());
            throw new BizException("存在无效的试卷ID：" + invalidPaperIds + "，无法添加引用");
        }

        // 2. 查询章节中已保存的引用关系
        PaperChapterReferencePO queryPO = new PaperChapterReferencePO();
        queryPO.setBookId(bookId);
        queryPO.setChapterId(chapterId);
        List<PaperChapterReferencePO> existList = paperChapterReferencePOMapper.selectList(queryPO);

        // 3. 构建现有引用关系的map，key为paperId
        Map<String, PaperChapterReferencePO> existMap = existList.stream()
                .collect(Collectors.toMap(PaperChapterReferencePO::getPaperId, po -> po));

        // 4. 构建新增/更新的引用关系map，key为paperId
        Map<String, PaperReference> newMap = paperReferences.stream()
                .collect(Collectors.toMap(PaperReference::getPaperId, pr -> pr));

        // 5. 生成批量保存列表
        List<PaperChapterReferencePO> referenceList = new ArrayList<>();

        // 5.1 处理已存在但不在新列表中的引用（需要禁用）
        existList.forEach(existPO -> {
            if (!newMap.containsKey(existPO.getPaperId())) {
                existPO.setEnable(false);
                existPO.setUpdateBy(userId);
                referenceList.add(existPO);
            }
        });

        // 5.2 处理新增或更新的引用（enable=true）
        paperReferences.forEach(pr -> {
            PaperChapterReferencePO po;
            if (existMap.containsKey(pr.getPaperId())) {
                // 已存在，更新
                po = existMap.get(pr.getPaperId());
                po.setPosition(pr.getPosition());
                po.setUpdateBy(userId);
            } else {
                // 新增
                po = new PaperChapterReferencePO(pr, userId);
            }
            referenceList.add(po);
        });

        // 6. 批量保存
        if (!referenceList.isEmpty()) {
            paperChapterReferencePOMapper.batchSave(referenceList);
        }
    }

    /**
     * 删除指定教材章节中的所有编辑态试卷引用
     * @param bookId 教材ID
     * @param chapterId 章节ID
     * @param userId 当前用户ID
     */
    @Override
    public void removePaperReferenceInChapter(String bookId, String chapterId, Long userId){
        paperChapterReferencePOMapper.removeReferenceInChapter(bookId, chapterId, userId);
    }

    /**
     * 添加教材中试卷引用
     * @param paperReference 试卷引用对象
     * @param userId 当前用户ID
     * @return 试卷引用ID
     */
    @Override
    public Boolean insertPaperReference(PaperReference paperReference, Long userId){
        PaperChapterReferencePO po = new PaperChapterReferencePO(paperReference, userId);
        return paperChapterReferencePOMapper.insert(po)>0;
    }

    /**
     * 删除教材中试卷引用
     * 仅仅删除编辑中的引用关系(引用关系的版本为默认版本)
     * @param paperReference 试卷引用对象
     * @param userId 当前用户ID
     * @return 试卷引用ID
     */
    @Override
    public Boolean deletePaperReference(PaperReference paperReference, Long userId){
        String paperId = paperReference.getPaperId();
        // 检查试卷是否存在发布中的版本
        PaperChapterReferencePO paperChapterReferencePO = new PaperChapterReferencePO(paperReference, userId);
        List<PaperChapterReferencePO> paperChapterReferencePOs = paperChapterReferencePOMapper.selectList(paperChapterReferencePO);
        if(CollectionUtils.isEmpty(paperChapterReferencePOs)){
            log.debug("试卷引用关系不存在，paperId：{}", paperId);
            throw new BizException("试卷引用关系不存在");
        }

        // 删除试卷与章节的引用关系
        return paperChapterReferencePOMapper.delete(List.of(paperChapterReferencePOs.getFirst().getId()), userId)>0;
    }

    /**
     * 保存试卷与特定章节版本的关系
     *
     * @param chapterId        章节ID
     * @param versionChapterId 版本章节ID
     * @param currentUserId    当前用户ID
     */
    @Override
    public void savePaperReferenceWithChapterVersion(String chapterId, Long versionChapterId, Long currentUserId) {
        // 查询章节引用的试卷
        PaperChapterReferencePO paperChapterReferencePO = new PaperChapterReferencePO();
        paperChapterReferencePO.setChapterId(chapterId);
        List<PaperChapterReferencePO> referenceList = paperChapterReferencePOMapper.selectList(paperChapterReferencePO);
        if(CollectionUtils.isEmpty(referenceList)){
            log.debug("章节不存在引用关系，chapterId：{}", chapterId);
            return;
        }

        // 基于章节试卷引用列表，生成章节版本与试卷的引用关系
        List<PaperChapterReferenceVersionPO> referenceVersionList = referenceList.stream().map(rpo ->
            new PaperChapterReferenceVersionPO(
                    rpo.getPaperId(),
                    rpo.getBookId(),
                    rpo.getChapterId(),
                    versionChapterId,
                    rpo.getPosition(),
                    currentUserId)).toList();
        paperChapterReferenceVersionPOMapper.batchInsert(referenceVersionList);
    }

    /**
     * 检查试卷引用是否存在
     *
     * @param paperIds 试卷ID列表
     * @return 被引用的试卷IDj集合
     */
    @Override
    public Set<String> checkPaperReferenceExist(List<String> paperIds) {
        if(CollectionUtils.isEmpty(paperIds)){
            return Set.of();
        }
        List<PaperChapterReferencePO> refList = paperChapterReferencePOMapper.selectReferenceByPaperIds(paperIds);
        if(CollectionUtils.isEmpty(refList)){
            log.debug("测试被教材章节使用:{}", paperIds);
            return Set.of();
        }
        return refList.stream().map(PaperChapterReferencePO::getPaperId).collect(Collectors.toSet());
    }

    /**
     * 检查试卷引用是否已发布
     *
     * @param paperIds 试卷ID列表
     * @return 被引用的试卷IDj集合
     */
    @Override
    public Set<String> getVersionedPaperReference(List<String> paperIds) {
        if(CollectionUtils.isEmpty(paperIds)){
            return Set.of();
        }
        List<PaperChapterReferenceVersionPO> refList = paperChapterReferenceVersionPOMapper.selectByPaperId(paperIds);
        if(CollectionUtils.isEmpty(refList)){
            return Set.of();
        }
        return refList.stream().map(PaperChapterReferenceVersionPO::getPaperId).collect(Collectors.toSet());
    }

    /**
     * 根据教材版本查询所有在章节中引用的试卷
     *
     * @param bookId 教材ID
     * @return 试卷引用列表
     */
    @Override
    public List<PaperReference> getPaperReferenceList(String bookId) {
        if (!StringUtils.hasText(bookId)) {
            throw new IllegalArgumentException("教材ID不能为空");
        }
        // 查询引用中的试卷，按所属章节序号升序排列
        List<PaperChapterReferencePO> referenceList = paperChapterReferencePOMapper.selectLatestReferenceByBookId(bookId);
        if(CollectionUtils.isEmpty(referenceList)){
            log.debug("教材没有引用中的试卷，bookId：{}", bookId);
            return List.of();
        }

        return referenceList.stream().map(PaperChapterReferencePO::toEntity).toList();
    }
}
