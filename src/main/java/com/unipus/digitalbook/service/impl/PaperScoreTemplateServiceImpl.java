package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.PaperScoreTemplateDetailPOMapper;
import com.unipus.digitalbook.dao.PaperScoreTemplatePOMapper;
import com.unipus.digitalbook.model.common.PageParams;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplate;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplateDetail;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplateList;
import com.unipus.digitalbook.model.enums.EventTypeEnum;
import com.unipus.digitalbook.model.enums.PaperScoreTemplateATEnum;
import com.unipus.digitalbook.model.enums.PaperScoreTemplateStatusEnum;
import com.unipus.digitalbook.model.po.template.PaperScoreTemplateDetailPO;
import com.unipus.digitalbook.model.po.template.PaperScoreTemplatePO;
import com.unipus.digitalbook.service.PaperScoreTemplateService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class PaperScoreTemplateServiceImpl implements PaperScoreTemplateService {

    private static final int TOTAL_SCORE = 100;

    @Resource
    private PaperScoreTemplatePOMapper paperScoreTemplatePOMapper;

    @Resource
    private PaperScoreTemplateDetailPOMapper paperScoreTemplateDetailPOMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addTemplate(PaperScoreTemplate paperScoreTemplate, Long createBy) {
        Long count = paperScoreTemplatePOMapper.countByName(paperScoreTemplate.getName(), null);
        if (ObjectUtils.isNotEmpty(count) && count > 0) {
            throw new IllegalArgumentException("模板名称重复");
        }

        List<PaperScoreTemplateDetail> templateDetailList = checkAndSupplyTemplateDetailList(paperScoreTemplate.getTemplateDetailList());

        PaperScoreTemplatePO paperScoreTemplatePO = new PaperScoreTemplatePO();
        paperScoreTemplatePO.fromEntity(paperScoreTemplate);
        paperScoreTemplatePO.setCreateBy(createBy);
        paperScoreTemplatePO.setUpdateBy(createBy);
        paperScoreTemplatePO.setStatus(PaperScoreTemplateStatusEnum.UN_PUBLISH.getCode());
        paperScoreTemplatePO.setAttributeType(PaperScoreTemplateATEnum.PROTOTYPE.getCode());
        paperScoreTemplatePO.setVersion(1);
        paperScoreTemplatePOMapper.insertSelective(paperScoreTemplatePO);

        List<PaperScoreTemplateDetailPO> templateDetailPOList = templateDetailList.stream().map(p -> {
            PaperScoreTemplateDetailPO paperScoreTemplateDetailPO = new PaperScoreTemplateDetailPO();
            paperScoreTemplateDetailPO.fromEntity(p);
            paperScoreTemplateDetailPO.setPaperScoreTemplateId(paperScoreTemplatePO.getId());
            paperScoreTemplateDetailPO.setCreateBy(createBy);
            paperScoreTemplateDetailPO.setUpdateBy(createBy);
            return paperScoreTemplateDetailPO;
        }).collect(Collectors.toList());
        paperScoreTemplateDetailPOMapper.batchInsert(templateDetailPOList);

        return paperScoreTemplatePO.getId();
    }

    /**
     * 校验并补充模板详情
     *
     * @param paperScoreTemplateDetailList
     * @return
     */
    private List<PaperScoreTemplateDetail> checkAndSupplyTemplateDetailList(List<PaperScoreTemplateDetail> paperScoreTemplateDetailList) {
        List<PaperScoreTemplateDetail> templateDetailList = new ArrayList<>();

        //下一个最低分
        int nextMinScore = 0;
        LinkedHashMap<Integer, List<PaperScoreTemplateDetail>> linkedHashMap = paperScoreTemplateDetailList.stream().sorted(Comparator.comparing(PaperScoreTemplateDetail::getMinScore)).collect(Collectors.groupingBy(PaperScoreTemplateDetail::getMinScore, LinkedHashMap::new, Collectors.toList()));
        for (Map.Entry<Integer, List<PaperScoreTemplateDetail>> entry : linkedHashMap.entrySet()) {
            if (entry.getValue().size() > 1) {
                throw new IllegalArgumentException("您设置的分数范围值有重叠，请重新设置");
            }

            PaperScoreTemplateDetail paperScoreTemplateDetail = entry.getValue().getFirst();
            if (nextMinScore != paperScoreTemplateDetail.getMinScore()) {
                throw new IllegalArgumentException("您设置的分数范围有缺失，未覆盖(0,100]的范围");
            }
            nextMinScore = paperScoreTemplateDetail.getMaxScore();
            templateDetailList.add(paperScoreTemplateDetail);
        }

        if (nextMinScore != TOTAL_SCORE) {
            throw new IllegalArgumentException("您设置的分数范围有缺失，未覆盖(0,100]的范围");
        }

        templateDetailList.addFirst(supplyZeroScoreTemplateDetail(templateDetailList.getFirst()));

        return templateDetailList;
    }

    /**
     * 补充0分的区间
     *
     * @param paperScoreTemplateDetail
     * @return
     */
    private PaperScoreTemplateDetail supplyZeroScoreTemplateDetail(PaperScoreTemplateDetail paperScoreTemplateDetail) {
        PaperScoreTemplateDetail zeroScoreTemplateDetail = new PaperScoreTemplateDetail();
        zeroScoreTemplateDetail.setMinScore(0);
        zeroScoreTemplateDetail.setMaxScore(0);
        zeroScoreTemplateDetail.setEvaluatePhrases(paperScoreTemplateDetail.getEvaluatePhrases());
        zeroScoreTemplateDetail.setEvaluateText(paperScoreTemplateDetail.getEvaluateText());
        return zeroScoreTemplateDetail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editTemplate(PaperScoreTemplate paperScoreTemplate, Long createBy) {
        PaperScoreTemplatePO paperScoreTemplatePO = paperScoreTemplatePOMapper.selectByPrimaryKey(paperScoreTemplate.getId());
        //校验模板类型是否被修改
        if (!paperScoreTemplate.getType().equals(paperScoreTemplatePO.getType())) {
            throw new IllegalArgumentException("模板类型不可以修改");
        }

        Long count = paperScoreTemplatePOMapper.countByName(paperScoreTemplate.getName(), paperScoreTemplate.getId());
        if (ObjectUtils.isNotEmpty(count) && count > 0) {
            throw new IllegalArgumentException("模板名称重复");
        }

        //校验并补充模板详情
        List<PaperScoreTemplateDetail> templateDetailList = checkAndSupplyTemplateDetailList(paperScoreTemplate.getTemplateDetailList());

        //获取待新增\更新\删除的模板明细
        Map<EventTypeEnum, List<PaperScoreTemplateDetailPO>> eventTemplateDetailList = getEventTemplateDetailList(paperScoreTemplate.getId(), templateDetailList, createBy);

        //更新模板信息
        PaperScoreTemplatePO editPaperScoreTemplatePO = new PaperScoreTemplatePO();
        editPaperScoreTemplatePO.setId(paperScoreTemplate.getId());
        editPaperScoreTemplatePO.setName(paperScoreTemplate.getName());
        editPaperScoreTemplatePO.setUpdateBy(createBy);
        editPaperScoreTemplatePO.setStatus(PaperScoreTemplateStatusEnum.UN_PUBLISH.getCode());
        editPaperScoreTemplatePO.setVersion(paperScoreTemplatePO.getVersion() + 1);
        paperScoreTemplatePOMapper.updateByPrimaryKeySelective(editPaperScoreTemplatePO);

        //新增\更新\删除模板明细
        for (Map.Entry<EventTypeEnum, List<PaperScoreTemplateDetailPO>> entry : eventTemplateDetailList.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            switch (entry.getKey()) {
                case ADD -> paperScoreTemplateDetailPOMapper.batchInsert(entry.getValue());
                case EDIT -> {
                    for (PaperScoreTemplateDetailPO detailPO : entry.getValue()) {
                        int update = paperScoreTemplateDetailPOMapper.updateByPrimaryKeySelective(detailPO);
                        if (update != 1) {
                            throw new IllegalArgumentException("模板详情信息错误");
                        }
                    }
                }
                case DELETE ->
                        paperScoreTemplateDetailPOMapper.deleteByIdList(entry.getValue().stream().map(PaperScoreTemplateDetailPO::getId).collect(Collectors.toList()), createBy);
            }
        }

        return true;
    }

    /**
     * 获取待新增\更新\删除的模板明细
     *
     * @param paperScoreTemplateId
     * @param templateDetailList
     * @param createBy
     * @return
     */
    private Map<EventTypeEnum, List<PaperScoreTemplateDetailPO>> getEventTemplateDetailList(Long paperScoreTemplateId, List<PaperScoreTemplateDetail> templateDetailList, Long createBy) {
        //待新增\更新\删除的模板明细
        Map<EventTypeEnum, List<PaperScoreTemplateDetailPO>> eventTemplateDetailListMap = Stream.of(EventTypeEnum.ADD, EventTypeEnum.EDIT, EventTypeEnum.DELETE).collect(Collectors.toMap(k -> k, v -> new ArrayList<>(), (v1, v2) -> v2));

        List<PaperScoreTemplateDetailPO> paperScoreTemplateDetailPOList = paperScoreTemplateDetailPOMapper.selectByPaperScoreTemplateId(paperScoreTemplateId);
        Map<Long, PaperScoreTemplateDetailPO> detailPOMap = paperScoreTemplateDetailPOList.stream().collect(Collectors.toMap(PaperScoreTemplateDetailPO::getId, Function.identity()));

        //待更新的模板详情Id
        Set<Long> editIdSet = new HashSet<>();

        for (PaperScoreTemplateDetail templateDetail : templateDetailList) {
            if (ObjectUtils.isNotEmpty(templateDetail.getId())) {
                PaperScoreTemplateDetailPO editPaperScoreTemplateDetailPO = new PaperScoreTemplateDetailPO();
                editPaperScoreTemplateDetailPO.fromEntity(templateDetail);
                editPaperScoreTemplateDetailPO.setId(templateDetail.getId());
                editPaperScoreTemplateDetailPO.setUpdateBy(createBy);
                eventTemplateDetailListMap.get(EventTypeEnum.EDIT).add(editPaperScoreTemplateDetailPO);
                detailPOMap.remove(templateDetail.getId());
                if (editIdSet.contains(editPaperScoreTemplateDetailPO.getId())){
                    throw new IllegalArgumentException("模板详情信息错误");
                }
                editIdSet.add(editPaperScoreTemplateDetailPO.getId());
            } else {
                //0分区间
                if (templateDetail.getMinScore().equals(templateDetail.getMaxScore())) {
                    PaperScoreTemplateDetailPO editPaperScoreTemplateDetailPO = new PaperScoreTemplateDetailPO();
                    editPaperScoreTemplateDetailPO.fromEntity(templateDetail);
                    editPaperScoreTemplateDetailPO.setId(paperScoreTemplateDetailPOList.getFirst().getId());
                    editPaperScoreTemplateDetailPO.setUpdateBy(createBy);
                    eventTemplateDetailListMap.get(EventTypeEnum.EDIT).add(editPaperScoreTemplateDetailPO);
                    detailPOMap.remove(paperScoreTemplateDetailPOList.getFirst().getId());
                } else {
                    PaperScoreTemplateDetailPO addPaperScoreTemplateDetailPO = new PaperScoreTemplateDetailPO();
                    addPaperScoreTemplateDetailPO.fromEntity(templateDetail);
                    addPaperScoreTemplateDetailPO.setPaperScoreTemplateId(paperScoreTemplateId);
                    addPaperScoreTemplateDetailPO.setCreateBy(createBy);
                    addPaperScoreTemplateDetailPO.setUpdateBy(createBy);
                    eventTemplateDetailListMap.get(EventTypeEnum.ADD).add(addPaperScoreTemplateDetailPO);
                }
            }
        }

        if (MapUtils.isNotEmpty(detailPOMap)) {
            eventTemplateDetailListMap.get(EventTypeEnum.DELETE).addAll(detailPOMap.values());
        }

        return eventTemplateDetailListMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean publishTemplate(Long currentUserId, Long id) {
        PaperScoreTemplatePO propertyObj = paperScoreTemplatePOMapper.selectByPrimaryKey(id);
        PaperScoreTemplatePO cloneObj = propertyObj.clone(currentUserId);
        paperScoreTemplatePOMapper.updateStatus(currentUserId, id, PaperScoreTemplateStatusEnum.PUBLISHED.getCode());
        paperScoreTemplatePOMapper.insertSelective(cloneObj);

        List<PaperScoreTemplateDetailPO> protertyTemplateDetailPOList = paperScoreTemplateDetailPOMapper.selectByPaperScoreTemplateId(id);
        List<PaperScoreTemplateDetailPO> cloneTemplateDetailPOList = protertyTemplateDetailPOList.stream().map(p -> p.clone(currentUserId, cloneObj.getId())).toList();
        paperScoreTemplateDetailPOMapper.batchInsert(cloneTemplateDetailPOList);

        return true;
    }

    @Override
    public PaperScoreTemplate getTemplateDetail(Long id, boolean showZeroInterval) {
        PaperScoreTemplatePO paperScoreTemplatePO = paperScoreTemplatePOMapper.selectByPrimaryKey(id);
        if (ObjectUtils.isEmpty(paperScoreTemplatePO)) {
            return null;
        }

        List<PaperScoreTemplateDetailPO> detailPOList = paperScoreTemplateDetailPOMapper.selectByPaperScoreTemplateId(id);
        if (!showZeroInterval){
            //不返回0分区间
            detailPOList.removeFirst();
        }
        List<PaperScoreTemplateDetail> paperScoreTemplateDetailList = detailPOList.stream().map(PaperScoreTemplateDetailPO::toEntity).collect(Collectors.toList());

        PaperScoreTemplate paperScoreTemplate = paperScoreTemplatePO.toEntity();
        paperScoreTemplate.setTemplateDetailList(paperScoreTemplateDetailList);
        return paperScoreTemplate;
    }

    @Override
    public PaperScoreTemplateList getTemplateList(String name, Integer type, Integer status, PageParams pageParams) {
        List<PaperScoreTemplatePO> paperScoreTemplatePOList = paperScoreTemplatePOMapper.selectTemplateList(name, type, status, pageParams);
        Long total = paperScoreTemplatePOMapper.countTemplateList(name, type, status);
        int totalCount = ObjectUtils.isEmpty(total) ? 0 : total.intValue();
        return PaperScoreTemplateList.assemblyPaperScoreTemplateList(paperScoreTemplatePOList, totalCount);
    }
}
