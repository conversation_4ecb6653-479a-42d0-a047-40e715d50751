package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.dao.*;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.paper.PaperSyncInfo;
import com.unipus.digitalbook.model.entity.paper.PaperVersion;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.enums.PaperTypeEnum;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import com.unipus.digitalbook.model.po.paper.*;
import com.unipus.digitalbook.model.po.publish.BookVersionPO;
import com.unipus.digitalbook.model.po.publish.BookVersionPaperVersionRelationPO;
import com.unipus.digitalbook.model.po.question.QuestionGroupPO;
import com.unipus.digitalbook.service.PaperVersionService;
import com.unipus.digitalbook.service.QuestionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 试卷版本服务实现类
 */
@Service
@Slf4j
public class PaperVersionServiceImpl implements PaperVersionService {

    @Resource
    private QuestionService questionService;
    @Resource
    private QuestionBankStrategyMapper questionBankStrategyMapper;
    @Resource
    private PaperChapterReferenceVersionPOMapper paperChapterReferenceVersionPOMapper;
    @Resource
    private QuestionGroupPOMapper questionGroupPOMapper;
    @Resource
    private PaperQuestionRelationPOMapper paperQuestionRelationPOMapper;
    @Resource
    private BookVersionPaperVersionRelationPOMapper bookVersionPaperVersionRelationPOMapper;
    @Resource
    private PaperExtendPOMapper paperExtendPOMapper;

    private static final String ERR_INVALID_PARAMS = "参数不能为空";
    private static final String ERR_PAPER_NOT_FOUND = "试卷不存在";

    /**
     * 根据试卷主键ID查询所有试卷
     *
     * @param paperPrimaryIds 试卷主键ID列表
     * @return 试卷列表
     */
    @Override
    public List<Paper> getPaperList(List<Long> paperPrimaryIds){
        if (CollectionUtils.isEmpty(paperPrimaryIds) ) {
            throw new IllegalArgumentException(ERR_INVALID_PARAMS);
        }
        // 查询试卷列表
        List<PaperPO> paperPOList = paperExtendPOMapper.selectPaperListByPrimaryId(paperPrimaryIds, false);
        if (CollectionUtils.isEmpty(paperPOList)) {
            log.debug("没有找到指定的试卷，paperPrimaryIds：{}", paperPrimaryIds);
            return List.of();
        }
        return paperPOList.stream().map(PaperPO::toEntity).toList();
    }

    /**
     * 根据教材版本ID查询所有试卷
     *
     * @param bookVersionId 教材版本ID
     * @return 试卷列表
     */
    @Override
    public List<Paper> getPaperList(Long bookVersionId) {
        List<BookVersionPaperVersionRelationPO> paperVersionRelations = bookVersionPaperVersionRelationPOMapper.selectByBookVersionId(bookVersionId);
        if (!CollectionUtils.isEmpty(paperVersionRelations)) {
            List<Long> paperPrimaryIds = paperVersionRelations.stream().map(BookVersionPaperVersionRelationPO::getPaperVersionId).toList();
            return getPaperList(paperPrimaryIds);
        }
        return List.of();
    }

    /**
     * 根据章节版本ID列表，获取试卷列表
     *
     * @param chapterVersionIds 章节版本ID列表
     * @param bookId 教材ID
     * @param bookVersionId 教材版本ID，可为null
     * @return 试卷列表
     */
    @Override
    public Map<Long, List<Paper>> getPaperChapterVersionMapping(List<Long> chapterVersionIds, String bookId, Long bookVersionId) {
        if (CollectionUtils.isEmpty(chapterVersionIds)) {
            return Map.of();
        }
        List<PaperChapterReferenceVersionPO> referenceList = paperChapterReferenceVersionPOMapper.selectByChapterVersionId(chapterVersionIds);
        Map<Long, List<String>> idMapping = referenceList.stream()
                .collect(Collectors.groupingBy(
                        PaperChapterReferenceVersionPO::getChapterVersionId,
                        Collectors.mapping(PaperChapterReferenceVersionPO::getPaperId, Collectors.toList())
                ));
        if (CollectionUtils.isEmpty(idMapping)) {
            return Map.of();
        }
        List<Paper> paperList;
        if (bookVersionId != null) {
            paperList = getPaperList(bookVersionId);
        } else {
            List<String> paperIds = referenceList.stream().map(PaperChapterReferenceVersionPO::getPaperId).distinct().toList();
            List<PaperPO> paperPOList = paperExtendPOMapper.selectLatestPaperList(paperIds, null, null, null);
            paperList = paperPOList.stream().map(PaperPO::toEntity).toList();
        }
        if (bookId != null) {
            paperList.forEach(paper -> paper.setBookId(bookId));
        }
        Map<Long, List<Paper>> paperChapterVersionMapping = new HashMap<>();
        idMapping.forEach((chapterVersionId, paperIds) -> {
            List<Paper> chapterPapers = paperList.stream().filter(paper -> paperIds.contains(paper.getPaperId())).toList();
            paperChapterVersionMapping.put(chapterVersionId, chapterPapers);
        });
        return paperChapterVersionMapping;
    }

    /**
     * 复制教材中试卷为指定版本
     * 包含复制试卷、题库、题目、题库策略、诊断卷推荐题关系、教材章节试卷引用
     * 其中题目复制，包含题组和题目
     * @param paperIds 试卷ID列表
     * @param userId 用户ID
     * @return 试卷发布版本号
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<PaperVersion> copyPaperWithVersion(Set<String> paperIds, Long userId) {
        // 试卷发布参照版本号（从参照版本数据复制，生成发布版本数据）
        String baseVersion = IdentifierUtil.DEFAULT_VERSION_NUMBER;
        // 生成试卷发布版本号
        String versionNumber = IdentifierUtil.generateVersion();

        // 并行处理试卷复制
        List<PaperVersion> versionList = paperIds.parallelStream()
                .map(paperId -> copyManager(paperId, baseVersion, versionNumber, userId))
                .toList();

        // 返回试卷发布版本号
        log.debug("复制试卷成功，versionNumber：{}", versionNumber);
        return versionList;
    }

    /**
     * 复制试卷、题库、题目、题库策略、诊断卷推荐题关系、教材章节试卷引用
     * @param paperId 试卷ID
     * @param baseVersion 编辑中的版本
     * @param targetVersion 发布目标版本
     * @param userId 用户ID
     */
    private PaperVersion copyManager(String paperId, String baseVersion, String targetVersion, Long userId) {
        // 复制试卷，返回试卷题组对象
        BigQuestionGroup paper = copyPaper(paperId, baseVersion, targetVersion, userId);
        // 复制题库，返回题库ID映射
        Map<String, Long> parentIdMap = copyQuestionBank(paper, baseVersion, targetVersion, userId);

        // 使用虚拟线程或固定线程池
        try (var executor = Executors.newVirtualThreadPerTaskExecutor()) {
            List<CompletableFuture<Void>> futures = Arrays.asList(
                CompletableFuture.runAsync(
                        // 复制题目
                        () -> copyQuestion(paper, parentIdMap, baseVersion, targetVersion, userId), executor),
                CompletableFuture.runAsync(
                        // 复制题库策略
                        () -> copyQuestionBankStrategy(parentIdMap, baseVersion, targetVersion, userId), executor),
                CompletableFuture.runAsync(
                        // 复制诊断卷推荐题关系
                        () -> copyDiagnosticQuestionRelation(paper, baseVersion, targetVersion, userId), executor)
            );

            // 合并所有任务，任一失败则抛出异常
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }

        // 返回试卷版本信息
        return new PaperVersion(paper.getId(), paperId, targetVersion);
    }

    // 复制试卷
    private BigQuestionGroup copyPaper(String paperId, String baseVersion, String targetVersion, Long userId) {
        // 复制试卷（题组）
        QuestionGroupPO questionGroupPO = Optional
                .ofNullable(questionGroupPOMapper.selectQuestionGroupByBizGroupId(paperId, baseVersion))
                .orElseThrow(() -> new IllegalArgumentException(ERR_PAPER_NOT_FOUND));
        BigQuestionGroup bigQuestionGroup = copyAndSaveQuestionGroup(questionGroupPO, null, targetVersion, userId);
        // 复制试卷扩展信息
        paperExtendPOMapper.insertBySelect(bigQuestionGroup.getId(), paperId, baseVersion, targetVersion, userId);

        // 返回试卷基本信息
        return bigQuestionGroup;
    }

    // 复制题库
    private Map<String, Long> copyQuestionBank(BigQuestionGroup paper, String baseVersion, String targetVersion, Long userId) {
        if (!QuestionGroupTypeEnum.CHALLENGE_PAPER.match(paper.getType())) {
            // 不是挑战卷，跳过
            return Collections.emptyMap();
        }
        // 获取所有题库
        List<QuestionGroupPO> questionBanks = questionGroupPOMapper.selectChildByParentBizGroupId(paper.getBizGroupId(), baseVersion);
        if (CollectionUtils.isEmpty(questionBanks)) {
            return Collections.emptyMap();
        }
        // 并行处理题库复制
        return questionBanks.parallelStream()
                .map(bank -> copyAndSaveQuestionGroup(bank, paper.getId(), targetVersion, userId))
                .collect(Collectors.toMap(BigQuestionGroup::getBizGroupId, BigQuestionGroup::getId));
    }

    private BigQuestionGroup copyAndSaveQuestionGroup(QuestionGroupPO source, Long parentId, String targetVersion, Long userId) {
        QuestionGroupPO copy = new QuestionGroupPO(source, parentId, targetVersion, userId);
        questionGroupPOMapper.insertOrUpdateSelective(copy);
        return copy.toBigQuestion();
    }

    // 复制题目
    private void copyQuestion(BigQuestionGroup paper, Map<String, Long> bankMap, String baseVersion, String targetVersion, Long userId) {
        Map<String, Long> parentMap = new HashMap<>(
                CollectionUtils.isEmpty(bankMap) ? Map.of(paper.getBizGroupId(), paper.getId()) : bankMap
        );

        // 基于题库列表或试卷，进行复制
        parentMap.entrySet().parallelStream().forEach(entry -> {
            String parentBizId = entry.getKey();
            Long parentPrimaryKey = entry.getValue();
            // 获取大题列表
            List<QuestionGroupPO> bigQuestionGroups = questionGroupPOMapper
                    .selectChildByParentBizGroupId(parentBizId, baseVersion);
            if (CollectionUtils.isEmpty(bigQuestionGroups)) {
                // 处理下一条数据
                return;
            }
            // 获取大题ID列表
            List<Long> groupIds = bigQuestionGroups.stream().map(QuestionGroupPO::getId).toList();
            // 批量获取大题（包含下级信息）
            List<BigQuestionGroup> questions = questionService.batchGetBigQuestions(groupIds);
            if (CollectionUtils.isEmpty(questions)) {
                // 处理下一条数据
                return;
            }
            // 批量保存大题
            questions.forEach(group -> {
                group.setId(null);
                group.setParentId(parentPrimaryKey);
                group.setVersionNumber(targetVersion);
                group.setUpdateBy(userId);
            });

            questionService.batchSaveBigQuestions(questions);
        });
    }

    // 复制题库策略
    private void copyQuestionBankStrategy(Map<String, Long> bankIdMap, String baseVersion, String targetVersion, Long userId) {
        if(CollectionUtils.isEmpty(bankIdMap)){
            // 如果不是题库，无需处理
            return;
        }
        List<String> bankIds = bankIdMap.keySet().stream().toList();
        // 查询题组策略
        List<QuestionBankStrategyPO> strategies = questionBankStrategyMapper.batchSelect(bankIds, baseVersion);
        if (CollectionUtils.isEmpty(strategies)) {
            return;
        }

        // 复制题库策略
        strategies.forEach(po -> {
            po.setId(null);
            po.setVersionNumber(targetVersion);
            po.setCreateBy(userId);
        });
        questionBankStrategyMapper.batchInsertOrUpdate(strategies);
    }

    // 复制诊断卷推荐题关系
    private void copyDiagnosticQuestionRelation(BigQuestionGroup paper, String baseVersion, String targetVersion, Long userId) {
        if(!QuestionGroupTypeEnum.DIAGNOSTIC.match(paper.getType())){
            // 如果不是诊断卷，无需处理
            return;
        }
        String paperId = paper.getBizGroupId();
        List<PaperQuestionRelationPO> relations = paperQuestionRelationPOMapper
                .selectByCondition(paperId, null, null, baseVersion);
        if(CollectionUtils.isEmpty(relations)){
            return;
        }
        relations.forEach(po -> {
            po.setId(null);
            po.setPaperVersionNumber(targetVersion);
            po.setCreateBy(userId);
        });
        paperQuestionRelationPOMapper.batchInsertOrUpdate(relations);
    }

    /**
     * 获取试卷是否已上架
     *
     * @param paperIds 试卷ID列表
     * @return 试卷是否已上架Map
     */
    @Override
    public Map<String, Boolean> isPublishedPaper(List<String> paperIds) {
        Map<String, Boolean> publishFlagMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(paperIds)) {
            for (String paperId : paperIds) {
                List<BookVersionPaperVersionRelationPO> paperVersionRelations = bookVersionPaperVersionRelationPOMapper.selectByPaperId(paperId);
                publishFlagMap.put(paperId, !CollectionUtils.isEmpty(paperVersionRelations));
            }
        }
        return publishFlagMap;
    }

    /**
     * 获取试卷的最新发布版本
     *
     * @param paperIds 试卷ID列表
     * @return 试卷列表
     */
    @Override
    public List<Paper> getLastPublishedPaperList(List<String> paperIds) {
        if (!CollectionUtils.isEmpty(paperIds)) {
            List<BookVersionPaperVersionRelationPO> paperVersionRelations = paperIds.stream()
                    .map(paperId -> bookVersionPaperVersionRelationPOMapper.selectLastPublishedByPaperId(paperId))
                    .filter(Objects::nonNull).toList();
            if (!CollectionUtils.isEmpty(paperVersionRelations)) {
                List<Long> paperPrimaryIds = paperVersionRelations.stream().map(BookVersionPaperVersionRelationPO::getPaperVersionId).toList();
                return getPaperList(paperPrimaryIds);
            }
        }
        return List.of();
    }

    /**
     * 根据教材ID/教材版本/试卷ID取得试卷版本
     *
     * @param bookId            教材ID
     * @param bookVersionNumber 教材版本
     * @param paperId           试卷ID
     * @return 试卷版本
     */
    @Override
    public PaperVersion getPaperVersionByBookInfo(String bookId, String bookVersionNumber, String paperId) {
        String paperVersionNumber = bookVersionPaperVersionRelationPOMapper.getPaperVersionByBookVersion(bookId, bookVersionNumber, paperId);
        if(!StringUtils.hasText(paperVersionNumber)){
            log.error("试卷不存在,教材ID:{},教材版本:{},试卷ID:{}", bookId, bookVersionNumber, paperId);
            throw new IllegalArgumentException(ERR_PAPER_NOT_FOUND);
        }

        // 获取试卷名称
        PaperExtendPO paperExtendPO = paperExtendPOMapper.selectByPaperIdAndVersion(paperId, paperVersionNumber);
        if(paperExtendPO == null){
            log.error("试卷信息不存在,试卷ID:{},试卷版本:{}", paperId, paperVersionNumber);
            throw new IllegalArgumentException(ERR_PAPER_NOT_FOUND);
        }

        // 获取试卷版本
        QuestionGroupPO questionGroupPO = questionGroupPOMapper.selectQuestionGroupByBizGroupId(paperId, paperVersionNumber);
        if(questionGroupPO == null){
            log.error("试卷信息不存在,试卷ID:{},试卷版本:{}", paperId, paperVersionNumber);
            throw new IllegalArgumentException(ERR_PAPER_NOT_FOUND);
        }
        PaperTypeEnum paperType = PaperTypeEnum.getTypeByGroupCode(questionGroupPO.getType());

        // 构建试卷版本信息
        PaperVersion paperVersion = new PaperVersion(paperExtendPO.getPaperVersionId(), paperId, paperVersionNumber);
        paperVersion.setPaperName(paperExtendPO.getPaperName());
        paperVersion.setPaperType(paperType.getCode());

        return paperVersion;
    }

    /**
     * 根据试卷ID及版本获取关联的教材ID和版本
     *
     * @param paperId            试卷ID
     * @param paperVersionNumber 试卷版本
     * @return 教材版本信息
     */
    @Override
    public BookVersion getBookInfoByPaperVersion(String paperId, String paperVersionNumber) {

        BookVersionPO bookVersionPO = bookVersionPaperVersionRelationPOMapper.getBookVersionByPaperVersion(paperId, paperVersionNumber);
        if(bookVersionPO == null){
            log.debug("未检索到试卷关联的教材，试卷ID:{},试卷版本:{}", paperId, paperVersionNumber);
            return null;
        }
        return bookVersionPO.toEntity();
    }

    /**
     * 根据教材版本ID取得试卷版本信息
     *
     * @param bookVersionId     教材版本ID
     * @return 试卷版本信息列表
     */
    @Override
    public List<PaperSyncInfo> getPaperSyncInfoByBookVersionId(Long bookVersionId) {
        // 查询教材版本下的试卷版本信息
        List<PaperPO> paperPOList = bookVersionPaperVersionRelationPOMapper.getPaperVersionByBookVersionId(bookVersionId);
        if(CollectionUtils.isEmpty(paperPOList)){
            log.debug("试卷不存在,教材版本ID:{}", bookVersionId);
            return List.of();
        }

        // 构建试卷信息
        return paperPOList.stream().map(this::buildPaperSyncInfo).filter(Objects::nonNull).toList();
    }

    /**
     * 构建试卷同步信息
     * @param po 试卷信息
     * @return 试卷同步信息
     */
    private PaperSyncInfo buildPaperSyncInfo(PaperPO po) {
        String paperId = po.getPaperId();
        String version = po.getVersionNumber();

        // 查询试卷信息（类型）
        QuestionGroupPO questionGroupPO = questionGroupPOMapper.selectQuestionGroupByBizGroupId(paperId, version);
        if(questionGroupPO == null){
            log.error("试卷不存在,试卷ID:{}", paperId);
            return null;
        }
        PaperTypeEnum paperType = PaperTypeEnum.getTypeByGroupCode(questionGroupPO.getType());

        // 查询试卷或者题库下的题组列表(大题基本信息)
        List<QuestionGroupPO> questionGroupPOs = questionGroupPOMapper.selectChildByParentBizGroupId(paperId, version);
        if(CollectionUtils.isEmpty(questionGroupPOs)){
            log.warn("试卷题目不存在,试卷ID:{}", paperId);
            return null;
        }

        if(PaperTypeEnum.CHALLENGE.match(paperType)){
            // 挑战卷不返回题目列表,仅仅返回总分和题目数量（1道大题对应1道小题）
            BigDecimal totalScore = questionGroupPOs.stream().map(QuestionGroupPO::getScore)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            return new PaperSyncInfo(po, paperType, questionGroupPOs.size(), totalScore, null);
        }

        // 查询大题组详情列表（包含下级题目信息）
        List<Long> questionPrimaryIds = questionGroupPOs.stream().map(QuestionGroupPO::getId).toList();
        List<BigQuestionGroup> questionGroups = questionService.batchGetBigQuestions(questionPrimaryIds);
        if(CollectionUtils.isEmpty(questionGroups)){
            log.warn("试卷题目不存在,试卷ID:{}", paperId);
            return null;
        }

        // 剔除诊断卷的推荐题（仅保留默认题）
        if (PaperTypeEnum.DIAGNOSTIC.match(paperType)) {
            // 查询诊断卷题目推荐关系
            List<PaperQuestionRelationPO> relations = paperQuestionRelationPOMapper
                    .selectByCondition(paperId, null, null, version);
            if (!CollectionUtils.isEmpty(relations)) {
                // 获取推荐题ID列表
                Set<String> recommendIds = relations.stream()
                        .map(PaperQuestionRelationPO::getBizQuestionIdTarget)
                        .filter(StringUtils::hasText)
                        .collect(Collectors.toSet());
                // 删除推荐题(推荐题作答结果不会提交到UAI，不需要同步)
                questionGroups.parallelStream().forEach(group -> {
                    if (!CollectionUtils.isEmpty(group.getQuestions())) {
                        group.getQuestions().removeIf(q -> recommendIds.contains(q.getBizQuestionId()));
                    }
                });
            }
        }

        // 取得小题列表
        List<Question> smallQuestions = questionGroups.stream()
                .filter(group-> !CollectionUtils.isEmpty(group.getQuestions()))
                .flatMap(group->group.getQuestions().stream())
                .toList();
        if(CollectionUtils.isEmpty(smallQuestions)){
            log.warn("试卷题目不存在,试卷ID:{}", paperId);
            return null;
        }

        // 计算小题目数量
        Integer questionCount = smallQuestions.size();

        // 合计小题分数作为试卷总分
        BigDecimal totalScore = smallQuestions.stream().map(Question::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 返回试卷同步信息
        return new PaperSyncInfo(po, paperType, questionCount, totalScore, questionGroups);
    }
}
