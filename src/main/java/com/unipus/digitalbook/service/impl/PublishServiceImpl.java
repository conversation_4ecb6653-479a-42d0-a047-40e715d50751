package com.unipus.digitalbook.service.impl;

import com.alibaba.fastjson.JSON;
import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.dao.*;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.book.*;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.chapter.ChapterList;
import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import com.unipus.digitalbook.model.entity.complement.ComplementResource;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.paper.PaperVersion;
import com.unipus.digitalbook.model.entity.publish.BookPublishOrder;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.enums.PublishContentTypeEnum;
import com.unipus.digitalbook.model.enums.PublishStatusEnum;
import com.unipus.digitalbook.model.events.BookPublishEvent;
import com.unipus.digitalbook.model.po.BookPublishPackagePO;
import com.unipus.digitalbook.model.po.BookVersionChapterVersionRelationPO;
import com.unipus.digitalbook.model.po.BookVersionInfoVersionRelationPO;
import com.unipus.digitalbook.model.po.book.BookPublishItem;
import com.unipus.digitalbook.model.po.publish.BookVersionPaperVersionRelationPO;
import com.unipus.digitalbook.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.function.LongFunction;
import java.util.function.ToLongFunction;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

/**
 * 教材上架服务实现类
 */
@Service
@Slf4j
public class PublishServiceImpl implements PublishService {
    @Value("${kafka.topic.bookPublishTopic}")
    String bookPublishTopic;

    @Resource
    private BookService bookService;

    @Resource
    private ChapterService chapterService;

    @Resource
    private UserService userService;

    @Resource
    private ComplementResourceService complementResourceService;

    @Resource
    private BookVersionService bookVersionService;

    @Resource
    private PaperVersionService paperVersionService;


    @Resource
    private BookVersionChapterVersionRelationPOMapper bookVersionChapterVersionRelationPOMapper;
    @Resource
    private BookVersionInfoVersionRelationPOMapper bookVersionInfoVersionRelationPOMapper;
    @Resource
    private BookVersionPaperVersionRelationPOMapper bookVersionPaperVersionRelationPOMapper;

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    @Resource
    private BookPublishPackagePOMapper publishPackagePOMapper;
    @Resource
    private ComplementResourcePOMapper complementResourcePOMapper;

    /**
     * 提交教材上架检测信息
     * todo 1：配套资源 2:检查教材本身状态
     *
     * @param bookPublishOrder 教材上架检测单
     * @param userId           操作用户ID
     * @return 提交结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public HashMap<String, String> publish(BookPublishOrder bookPublishOrder, Long userId) {
        //todo 检查教材本身状态
        Book book = bookService.getBookById(bookPublishOrder.getBookId());
        //获取已上架的节点信息
        List<BookNode> publishedNodeList = getPublishedNodeList(book.getId());
        // 检查提交的所包含的节点信息
        HashMap<String, String> checkResultMap = checkSubmitInfo(bookPublishOrder.getPublishResourceList(), publishedNodeList);
        if (!checkResultMap.isEmpty()) {
            log.debug("教材上架检测信息参数校验失败:{}", checkResultMap);
            return checkResultMap;
        }
        //获取最新的章节信息（章节名称和排序顺序）
        ChapterList chapterList = chapterService.getChaptersByBookId(book.getId());

        // 保存提交信息
        return saveBookVersion(bookPublishOrder, publishedNodeList, chapterList.getChapterList(), userId);
    }

    /**
     * 获取已上架教材内容节点列表
     *
     * @param bookId 教材ID
     * @return 已上架信息
     */
    private List<BookNode> getPublishedNodeList(String bookId) {
        BookVersion bookLastPublishedVersion = bookVersionService.getBookLastPublishedVersion(bookId);
        if (bookLastPublishedVersion == null) {
            log.info("教材版本信息为空,教材ID:{}", bookId);
            return List.of();
        }
        List<BookNode> bookNodes = new ArrayList<>();
        //查询该版本下的信息（基本信息、简介、版权信息、配套资源）
        List<BookVersionInfoVersionRelationPO> infoVersionRelationPOList = bookVersionInfoVersionRelationPOMapper.selectByBookVersionId(bookLastPublishedVersion.getId());
        if (!CollectionUtils.isEmpty(infoVersionRelationPOList)) {
            List<BookNode> nodes = buildBookInfoNode(infoVersionRelationPOList);
            bookNodes.addAll(nodes);
        }

        //查询该版本下的章节信息
        List<BookVersionChapterVersionRelationPO> chapterVersionRelations = bookVersionChapterVersionRelationPOMapper.selectByBookVersionId(bookLastPublishedVersion.getId());
        if (!CollectionUtils.isEmpty(chapterVersionRelations)) {
            Map<Long, BookVersionChapterVersionRelationPO> chapterVersionRelationMap = chapterVersionRelations.stream().collect(Collectors.toMap(BookVersionChapterVersionRelationPO::getChapterVersionId, o -> o));
            List<Long> chapterVersionIds = chapterVersionRelations.stream().map(BookVersionChapterVersionRelationPO::getChapterVersionId).toList();
            List<Chapter> chapters = buildChapterListByVersionIdList(chapterVersionIds);
            for (Chapter chapter : chapters) {
                chapter.setChapterNumber(chapterVersionRelationMap.get(chapter.getChapterVersion().getId()).getChapterNumber());
                chapter.setName(chapterVersionRelationMap.get(chapter.getChapterVersion().getId()).getChapterName());
            }
            bookNodes.addAll(chapters);
        }

        //查询该版本下的试卷信息
        List<BookVersionPaperVersionRelationPO> paperVersionRelations = bookVersionPaperVersionRelationPOMapper.selectByBookVersionId(bookLastPublishedVersion.getId());
        if (!CollectionUtils.isEmpty(paperVersionRelations)) {
            List<Long> paperPrimaryIds = paperVersionRelations.stream().map(BookVersionPaperVersionRelationPO::getPaperVersionId).distinct().toList();
            List<Paper> papers = paperVersionService.getPaperList(paperPrimaryIds);
            papers.forEach(paper -> paper.setBookId(bookId));
            bookNodes.addAll(papers);
        }

        return bookNodes;
    }

    private List<BookNode> buildBookInfoNode(List<BookVersionInfoVersionRelationPO> infoVersionRelationPOList) {
        List<BookNode> bookNodes = new ArrayList<>();
        for (BookVersionInfoVersionRelationPO infoVersionRelationPO : infoVersionRelationPOList) {
            PublishContentTypeEnum contentType = PublishContentTypeEnum.getEnumByCode(infoVersionRelationPO.getContentType());
            switch (contentType) {
                case BASIC_INFO -> {
                    BookBasic bookBasic = bookService.getBookBasicInfoById(infoVersionRelationPO.getInfoId());
                    bookNodes.add(bookBasic);
                }
                case BOOK_INTRO -> {
                    BookIntro bookIntro = bookService.getBookIntroById(infoVersionRelationPO.getInfoId());
                    bookNodes.add(bookIntro);
                }
                case COPYRIGHT_INFO -> {
                    BookCopyright copyrightInfo = bookService.getBookCopyrightById(infoVersionRelationPO.getInfoId());
                    bookNodes.add(copyrightInfo);
                }
                case COMPLEMENT_RESOURCE -> {
                    ComplementResource complementResource = complementResourceService.getResourceById(infoVersionRelationPO.getInfoId());
                    bookNodes.add(complementResource);
                }
                case null, default -> log.error("未知的教材信息类型:{}", contentType);
            }
        }
        return bookNodes;
    }

    private List<Chapter> buildChapterListByVersionIdList(List<Long> chapterVersionIds) {
        List<Chapter> chapters = new ArrayList<>();
        for (Long chapterVersionId : chapterVersionIds) {
            Chapter chapter = chapterService.getChapterWithVersionByVersionId(chapterVersionId);
            if (chapter != null) {
                chapters.add(chapter);
            }
        }
        return chapters;
    }

    private HashMap<String, String> saveBookVersion(BookPublishOrder bookPublishInfos,
                                                    List<BookNode> alreadyPublishedInfos,
                                                    List<Chapter> newestChapterList,
                                                    Long userId) {
        //创建书的version
        BookVersion newBookVersion = createAndSaveNewBookVersion(bookPublishInfos, userId);
        Long bookVersionId = newBookVersion.getId();

        BookPublishPackagePO bookPublishPackagePO = buildBookPublishPackagePO(bookVersionId, userId);

        //开始处理新版本的章节
        processChapterVersions(bookPublishInfos, alreadyPublishedInfos, newestChapterList, bookVersionId, userId, bookPublishPackagePO.getPublishPackage());

        // 处理信息（基本信息、简介、版权信息）版本
        processMetadata(bookPublishInfos, alreadyPublishedInfos, bookVersionId, userId, bookPublishPackagePO.getPublishPackage());

        //处理配套资源 由于配套资源是全量覆盖的 所以直接不用处理已上架的数据
        processSupportingResources(bookPublishInfos, alreadyPublishedInfos, bookVersionId, userId, bookPublishPackagePO.getPublishPackage());

        // 处理试卷及试卷引用
        processPaperVersions(bookPublishInfos, alreadyPublishedInfos, bookVersionId, userId, bookPublishPackagePO.getPublishPackage());

        //保存上架包
        publishPackagePOMapper.insertSelective(bookPublishPackagePO);
        // 发送上架事件
        UserInfo userInfo = userService.getUserInfo(userId);
        sendPublishEvent(bookPublishInfos.getBookId(), newBookVersion.getId(), newBookVersion.getVersionNum(), userId, userInfo.getSsoId());
        return new HashMap<>();
    }

    private BookPublishPackagePO buildBookPublishPackagePO(Long bookVersionId, Long userId) {
        BookPublishPackagePO bookPublishPackagePO = new BookPublishPackagePO();
        bookPublishPackagePO.setBookVersionId(bookVersionId);
        bookPublishPackagePO.setCreateBy(userId);
        bookPublishPackagePO.setEnable(true);
        bookPublishPackagePO.setCreateTime(new Date());
        bookPublishPackagePO.setPublishPackage(new ArrayList<>());
        return bookPublishPackagePO;
    }

    private void publishPackageFillChapters(List<BookPublishItem> publishItemList, List<Chapter> chapters) {
        for (Chapter chapter : chapters) {
            BookPublishItem bookPublishItem = new BookPublishItem();
            bookPublishItem.setTypeCode(PublishContentTypeEnum.CHAPTER_CONTENT.getCode());
            bookPublishItem.setResourceId(chapter.getId());
            bookPublishItem.setVersionId(chapter.getChapterVersion().getId());
            publishItemList.add(bookPublishItem);
        }
    }

    private void publishPackageFillPapers(List<BookPublishItem> publishItemList, List<PaperVersion> paperVersions) {
        for (PaperVersion paperVersion : paperVersions) {
            BookPublishItem bookPublishItem = new BookPublishItem();
            bookPublishItem.setTypeCode(PublishContentTypeEnum.PAPER.getCode());
            bookPublishItem.setResourceId(paperVersion.getPaperId());
            bookPublishItem.setVersionId(paperVersion.getId());
            publishItemList.add(bookPublishItem);
        }
    }

    private void publishPackageFillInfo(List<BookPublishItem> publishItemList, PublishContentTypeEnum contentType, Long versionId) {
        BookPublishItem bookPublishItem = new BookPublishItem();
        bookPublishItem.setTypeCode(contentType.getCode());
        bookPublishItem.setVersionId(versionId);
        publishItemList.add(bookPublishItem);
    }

    private void sendPublishEvent(String bookId, Long versionId, String versionNumber, Long oprUserId, String openId) {
        BookPublishEvent bookPublishEvent = new BookPublishEvent(bookId, versionNumber, versionId, oprUserId, openId);
        kafkaTemplate.send(bookPublishTopic, JSON.toJSONString(bookPublishEvent));
    }


    private BookVersion createAndSaveNewBookVersion(BookPublishOrder bookPublishInfos, Long userId) {
        BookVersion bookVersion = new BookVersion();
        bookVersion.setBookId(bookPublishInfos.getBookId());
        // Consider injecting a Clock for testability instead of new Date()
        bookVersion.setVersionNum(IdentifierUtil.generateVersion());
        bookVersion.setPublishStatus(PublishStatusEnum.FIRST_SUBMISSION.getCode()); // Assuming PublishStatusEnum exists
        bookVersion.setCreateBy(userId);

        Long bookVersionId = bookVersionService.addBookVersion(bookVersion); // Assuming this returns the ID
        bookVersion.setId(bookVersionId); // Set the ID on the object
        log.info("Created new BookVersion Record with ID: {},Number:{}", bookVersionId, bookVersion.getVersionNum());
        return bookVersion;
    }

    /**
     * 处理新书版本中所有与章节相关的逻辑。
     */
    private void processChapterVersions(BookPublishOrder bookPublishInfos,
                                        List<BookNode> alreadyPublishedInfos,
                                        List<Chapter> newestChapterList, // 包含了按照最终期望顺序排列的章节名称/编号
                                        Long bookVersionId,
                                        Long userId, List<BookPublishItem> bookPublishPackagePO) {

        // 从出版单中提取待上架的章节列表
        List<Chapter> willPublishChapters = extractResourceList(bookPublishInfos, Chapter.class);
        if (!CollectionUtils.isEmpty(willPublishChapters)) {
            publishPackageFillChapters(bookPublishPackagePO, willPublishChapters);
        }
        // 将计划上架的章节与已上架的章节合并，以获得该版本的完整章节集合
        Map<String, Chapter> finalChapterMap = mergeChapterLists(willPublishChapters, alreadyPublishedInfos);
        if (CollectionUtils.isEmpty(finalChapterMap.values())) {
            // 如果没有找到有效的章节版本进行关联，则记录警告并返回
            log.warn("对于书籍版本 {} 没有找到有效的章节版本进行关联,完整章节集合:{}", bookVersionId, finalChapterMap.values());
            return;
        }
        // 获取待上架的章节版本id列表
        List<Long> willPublishVersionIdList = bookPublishPackagePO.stream()
                .filter(item -> PublishContentTypeEnum.CHAPTER_CONTENT.match(item.getTypeCode()))
                .map(BookPublishItem::getVersionId).toList();
        // 使用newestChapterList创建章节版本关系，确保正确的顺序和命名
        createChapterVersionRelations(finalChapterMap, newestChapterList, bookVersionId, userId, willPublishVersionIdList);
    }


    /**
     * 创建并批量插入教材版本与章节版本之间的关系记录。
     *
     * @param newestChapterList 来获取正确的显示名称和章节编号。
     */
    private void createChapterVersionRelations(Map<String, Chapter> finalChapterMap,
                                               List<Chapter> newestChapterList, // 用于名称/排序编号的查找
                                               Long bookVersionId, Long userId,
                                               List<Long> willPublishVersionIdList) {

        // 获取特定版本进行关联
        List<ChapterVersion> chapterVersions = finalChapterMap.values().stream()
                .map(Chapter::getChapterVersion)
                .filter(Objects::nonNull) // 确保不会发生空指针异常
                .toList();
        // 创建一个map以便快速从最新的列表中查找章节元数据
        Map<String, Chapter> newestChapterMap = newestChapterList.stream()
                .collect(Collectors.toMap(Chapter::getId, Function.identity(), (c1, c2) -> c1)); // 处理潜在的重复项（如果有）

        List<BookVersionChapterVersionRelationPO> relations = new ArrayList<>();
        for (ChapterVersion cv : chapterVersions) {
            Chapter chapterMetadata = newestChapterMap.get(cv.getChapterId());
            if (chapterMetadata != null) {
                BookVersionChapterVersionRelationPO relation = new BookVersionChapterVersionRelationPO();
                relation.setBookVersionId(bookVersionId);
                relation.setChapterVersionId(cv.getId());
                // 根据版本ID是否在待上架列表中决定使用哪个元数据来源
                if (willPublishVersionIdList.contains(cv.getId())) {
                    relation.setChapterName(chapterMetadata.getName()); // 从最新的列表中获取名称
                    relation.setChapterNumber(chapterMetadata.getChapterNumber()); // 从最新的列表中获取编号
                } else {
                    Chapter chapter = finalChapterMap.get(cv.getChapterId());
                    relation.setChapterName(chapter.getName());
                    relation.setChapterNumber(chapter.getChapterNumber());
                }
                relation.setCreateBy(userId);
                // 如果有必要，设置其他字段
                relations.add(relation);
            } else {
                log.warn("在newestChapterList中找不到chapter ID {} 对应的章节元数据 (ChapterVersion ID: {})", cv.getChapterId(), cv.getId());
                //todo 上架过程中，被删除的情况
            }
        }

        if (!CollectionUtils.isEmpty(relations)) {
            log.info("批量插入{}个章节关系记录到教材版本 {}", relations.size(), bookVersionId);
            bookVersionChapterVersionRelationPOMapper.batchInsert(relations);
        }
    }

    /**
     * 创建并批量插入教材版本与试卷版本之间的关系记录。
     *
     * @param paperVersions 试卷版本列表
     * @param bookVersionId 教材版本ID
     * @param userId        用户ID
     */
    private void createPaperVersionRelations(List<PaperVersion> paperVersions, Long bookVersionId, Long userId) {
        // 创建关系记录
        List<BookVersionPaperVersionRelationPO> relations = paperVersions.stream()
                .map(pv -> new BookVersionPaperVersionRelationPO(bookVersionId, pv.getVersionId(), userId))
                .toList();
        bookVersionPaperVersionRelationPOMapper.batchInsert(relations);
    }

    /**
     * 合并待上架的章节列表与已上架的章节信息
     *
     * @param willPublishChapters   待上架的章节列表
     * @param alreadyPublishedInfos 已上架的章节信息列表
     * @return 包含合并结果的章节映射表
     */
    private Map<String, Chapter> mergeChapterLists(List<Chapter> willPublishChapters, List<BookNode> alreadyPublishedInfos) {
        // 提取之前已上架的章节
        List<Chapter> publishedChapters = alreadyPublishedInfos.stream()
                .filter(Chapter.class::isInstance)
                .map(Chapter.class::cast)
                .toList();

        // 创建一个按章节Id对应已上架章节的关系
        Map<String, Chapter> chapterMap = publishedChapters.stream()
                .collect(Collectors.toMap(Chapter::getId, Function.identity()));

        // 使用待上架的章节覆盖或新增到映射表中
        for (Chapter newChapter : willPublishChapters) {
            chapterMap.put(newChapter.getId(), newChapter);
        }
        return chapterMap;
    }

    /**
     * 合并待上架的试卷列表与已上架的试卷信息
     *
     * @param willPublishPapers     待上架的试卷列表
     * @param alreadyPublishedInfos 已上架的试卷信息列表
     * @return 包含合并结果的章节映射表
     */
    private Map<String, PaperVersion> mergePaperLists(List<PaperVersion> willPublishPapers, List<BookNode> alreadyPublishedInfos) {
        // 提取之前已上架的章节
        List<Paper> publishedPapers = alreadyPublishedInfos.stream()
                .filter(Paper.class::isInstance)
                .map(Paper.class::cast)
                .toList();

        // 创建一个试卷Id对应已上架试卷的关系
        Map<String, PaperVersion> paperVersionMap = publishedPapers.stream()
                .map(p -> new PaperVersion(p.getId(), p.getPaperId(), p.getVersionNumber())).
                collect(Collectors.toMap(PaperVersion::getPaperId, Function.identity()));

        // 使用待上架的章节覆盖或新增到映射表中
        for (PaperVersion newPaperVersion : willPublishPapers) {
            paperVersionMap.put(newPaperVersion.getPaperId(), newPaperVersion);
        }
        return paperVersionMap;
    }

    /**
     * 从BookPublishOrder对象中提取指定类型的资源列表
     *
     * @param bookPublishInfos 包含资源信息的BookPublishOrder对象
     * @param resourceClass    需要提取的资源类型
     * @return 指定类型的资源列表
     */
    private <T> List<T> extractResourceList(BookPublishOrder bookPublishInfos, Class<T> resourceClass) {
        // 如果输入对象为空或资源列表为空，则返回空列表
        if (bookPublishInfos == null || CollectionUtils.isEmpty(bookPublishInfos.getPublishResourceList())) {
            return Collections.emptyList();
        }
        // 过滤并转换资源列表为指定类型，然后收集到新的列表中
        return bookPublishInfos.getPublishResourceList().stream()
                .filter(resourceClass::isInstance) // 过滤出指定类型的资源
                .map(resourceClass::cast)          // 将过滤后的资源转换为指定类型
                .toList();       // 收集结果到列表中
    }

    /**
     * Handles processing of metadata resources (BookBasic, BookIntro, BookCopyright).
     */
    private void processMetadata(BookPublishOrder bookPublishInfos,
                                 List<BookNode> alreadyPublishedInfos,
                                 Long bookVersionId, Long userId,
                                 List<BookPublishItem> publishItemList) {

        // Use the generic helper for each metadata type
        processSingleMetadataResource(bookPublishInfos, alreadyPublishedInfos, bookVersionId,
                BookBasic.class,
                bookService::getBookBasicInfoById, // Function to fetch full object by ID
                BookBasic::deepCopy,             // Function to copy the object
                bookService::saveBookBasic,      // Function to save the copied object
                PublishContentTypeEnum.BASIC_INFO, userId, publishItemList);

        processSingleMetadataResource(bookPublishInfos, alreadyPublishedInfos, bookVersionId,
                BookIntro.class,
                bookService::getBookIntroById,
                BookIntro::deepCopy,
                bookService::saveBookIntro,
                PublishContentTypeEnum.BOOK_INTRO, userId, publishItemList);

        processSingleMetadataResource(bookPublishInfos, alreadyPublishedInfos, bookVersionId,
                BookCopyright.class,
                bookService::getBookCopyrightById,
                BookCopyright::deepCopy,
                bookService::saveBookCopyright,
                PublishContentTypeEnum.COPYRIGHT_INFO, userId, publishItemList);
    }

    /**
     * Handles processing of paper resources.
     */
    private void processPaperVersions(BookPublishOrder bookPublishInfos,
                                      List<BookNode> alreadyPublishedInfos,
                                      Long bookVersionId, Long userId,
                                      List<BookPublishItem> publishItemList) {
        // 从出版单中提取待上架的试卷列表
        List<Paper> willPublishPapers = extractResourceList(bookPublishInfos, Paper.class);

        List<PaperVersion> newPaperVersions = List.of();
        if (!CollectionUtils.isEmpty(willPublishPapers)) {
            Set<String> paperIds = willPublishPapers.stream().map(Paper::getPaperId).collect(Collectors.toSet());
            // 复制试卷，生成上架版本（仅返回版本信息，如需试卷实体，通过paperVersionService.getPaperList取得）
            newPaperVersions = paperVersionService.copyPaperWithVersion(paperIds, userId);
            if (CollectionUtils.isEmpty(newPaperVersions)) {
                log.warn("试卷复制失败，paperIds：{}", paperIds);
                return;
            }
            // 填充出版单中的试卷信息
            publishPackageFillPapers(publishItemList, newPaperVersions);
        }

        // 将计划上架的试卷与已上架的试卷合并，以获得该版本的完整试卷集合
        Map<String, PaperVersion> finalPaperVersionMap = mergePaperLists(newPaperVersions, alreadyPublishedInfos);
        List<PaperVersion> paperVersionsToLink = new ArrayList<>(finalPaperVersionMap.values());

        // 获取特定版本进行关联
        if (CollectionUtils.isEmpty(paperVersionsToLink)) {
            // 如果没有找到有效的试卷版本进行关联，则记录警告并返回
            log.warn("对于书籍版本 {} 没有找到有效的试卷版本进行关联,完整试卷集合:{}", bookVersionId, finalPaperVersionMap.values());
            return;
        }

        // 创建教材版本与试卷版本关系
        createPaperVersionRelations(paperVersionsToLink, bookVersionId, userId);

    }

    /**
     * 通用的helper方法，用于处理单一类型的元数据资源。
     * 检查是否指定了新的资源，如果是，则复制/保存它，或者链接之前已发布的资源。
     * 创建必要的版本关系。
     *
     * @param <T>                   资源的类型（例如，BookBasic, BookIntro），应该包含getId()和deepCopy()方法。
     * @param bookPublishInfos      包含可能的新资源的发布单。
     * @param alreadyPublishedInfos 已经发布的节点列表。
     * @param bookVersionId         当前构建的书籍版本ID。
     * @param resourceClass         资源类型的类（例如，BookBasic.class）。
     * @param fetcher               根据其ID获取完整资源对象的功能（必须返回T）。
     * @param copier                执行资源对象深拷贝的功能（必须返回T）。
     * @param saver                 保存资源对象并返回其新ID的功能（必须返回Long）。
     * @param contentType           表示此资源的枚举类型。
     */
    private <T> void processSingleMetadataResource(
            BookPublishOrder bookPublishInfos, List<BookNode> alreadyPublishedInfos, Long bookVersionId,
            Class<T> resourceClass, LongFunction<T> fetcher, UnaryOperator<T> copier, ToLongFunction<T> saver,
            PublishContentTypeEnum contentType, Long userId, List<BookPublishItem> publishItemList) {

        // 假设资源有一个Long getId()方法。如果ID是String，请调整Function<Long, T>。
        // 查找检查列表中是否指定了这种类型的新资源
        T newResourceStub = extractResourceList(bookPublishInfos, resourceClass).stream().findFirst().orElse(null);
        Long resourceIdToLink = null;

        if (newResourceStub != null) {
            // 指定了新资源：获取、复制、保存、获取新ID
            // 需要首先获取完整的对象。
            // 如果bookPublishInfos中的存根*是*完整对象，请调整此逻辑。
            Long stubId = ((BookInfoInterface) newResourceStub).getId(); // 假设资源实现Identifiable接口以提供getId()
            T fullResource = fetcher.apply(stubId);
            if (fullResource != null) {
                T copiedResource = copier.apply(fullResource);
                if (copiedResource instanceof BookInfoInterface copiedResourceObject) {
                    copiedResourceObject.setCreateBy(userId);
                    resourceIdToLink = saver.applyAsLong(copiedResource);
                    publishPackageFillInfo(publishItemList, contentType, resourceIdToLink);
                    log.info("为书籍版本 {} 保存了新的 {}，ID {}", bookVersionId, resourceClass.getSimpleName(), resourceIdToLink);
                }
            }
        } else {
            // 未指定新资源：查找之前已发布的资源
            T publishedResource = alreadyPublishedInfos.stream()
                    .filter(resourceClass::isInstance)
                    .map(resourceClass::cast)
                    .findFirst()
                    .orElse(null);
            if (publishedResource != null) {
                resourceIdToLink = ((BookInfoInterface) publishedResource).getId();
                log.debug("将现有 {} 的ID {} 链接到书籍版本 {}", resourceClass.getSimpleName(), resourceIdToLink, bookVersionId);
            }
        }

        // 如果确定了一个要链接的ID（无论是新的还是已有的），则创建关联
        if (resourceIdToLink != null) {
            createVersionInfoRelation(bookVersionId, resourceIdToLink, contentType, userId);
        }
    }

    /**
     * Creates the relationship record between a book version and a metadata info item (Basic, Intro, Copyright).
     */
    private void createVersionInfoRelation(Long bookVersionId, Long infoId, PublishContentTypeEnum contentType, Long userId) {
        BookVersionInfoVersionRelationPO relation = new BookVersionInfoVersionRelationPO();
        relation.setBookVersionId(bookVersionId);
        relation.setInfoId(infoId);
        relation.setContentType(contentType.getCode());
        // Assuming insertSelective handles setting createBy/updateBy if needed, or add explicitly using userId if available here
        relation.setCreateBy(userId);
        bookVersionInfoVersionRelationPOMapper.insertSelective(relation);
        log.debug("Created info relation for BookVersionId: {}, InfoId: {}, Type: {}", bookVersionId, infoId, contentType);
    }


    /**
     * 处理配套资源
     */
    private void processSupportingResources(BookPublishOrder bookPublishInfos,
                                            List<BookNode> alreadyPublishedInfos,
                                            Long bookVersionId,
                                            Long userId, List<BookPublishItem> publishItemList) {
        List<ComplementResource> complementResourceList = extractResourceList(bookPublishInfos, ComplementResource.class);
        List<ComplementResource> alreadyPublishedComplementResourceList = alreadyPublishedInfos.stream()
                .filter(ComplementResource.class::isInstance).map(ComplementResource.class::cast).toList();
        if (complementResourceList.isEmpty() && !alreadyPublishedComplementResourceList.isEmpty()) {
            //  如果本次发布的新资源为空，但存在以前的发布的资源列表，则认为已发布的是本次发布的，然后进行覆盖性发布
            for (ComplementResource complementResource : alreadyPublishedComplementResourceList) {
                processSingleMetadataResource(null, Collections.singletonList(complementResource), bookVersionId,
                        ComplementResource.class,
                        complementResourceService::getResourceById,
                        ComplementResource::deepCopy,
                        complementResourceService::saveResource,
                        PublishContentTypeEnum.COMPLEMENT_RESOURCE, userId, publishItemList);
            }
            return;
        }
        if (complementResourceList.isEmpty()) {
            log.info("本次发布没有配套资源");
            return;
        }
        for (ComplementResource complementResource : complementResourceList) {
            BookPublishOrder tmpBookPublishInfos = new BookPublishOrder();
            tmpBookPublishInfos.setPublishResourceList(Collections.singletonList(complementResource));
            List<BookPublishItem> tmpPublishItemList = new ArrayList<>();
            processSingleMetadataResource(tmpBookPublishInfos, null, bookVersionId,
                    ComplementResource.class,
                    complementResourceService::getResourceById,
                    ComplementResource::deepCopy,
                    complementResourceService::saveResource,
                    PublishContentTypeEnum.COMPLEMENT_RESOURCE, userId, tmpPublishItemList);
            tmpPublishItemList.forEach(publishItem -> {
                publishItem.setResourceId(complementResource.getResourceId());
                publishItemList.add(publishItem);
            });
        }
    }


    private String formatMapMessage(HashMap<String, String> messageMap) {
        return messageMap.entrySet().stream()
                .map(entry -> String.format("Key: %s, Value: %s", entry.getKey(), entry.getValue()))
                .collect(Collectors.joining(", ", "{", "}"));
    }

    /**
     * 检查提交信息
     *
     * @param bookPublishInfos  教材发布检测信息参数
     * @param publishedNodeList 已发布的教材节点列表
     * @return 检查结果
     */
    private HashMap<String, String> checkSubmitInfo(List<BookNode> bookPublishInfos, List<BookNode> publishedNodeList) {
        HashMap<String, String> checkResultMap = new HashMap<>();

        //获取已发布的教材的信息,检测是否存在必有节点（版权、基本信息、简介）
        checkRequiredNodesMissing(bookPublishInfos, publishedNodeList, checkResultMap);

        if (!checkResultMap.isEmpty()) {
            String message = "教材的基本信息或教材简介或版权信息还未提交。为确保后续流程顺利进行，请提交这些信息进行检测。";
            log.debug("{}\n{}", message, formatMapMessage(checkResultMap));
            throw new BizException(message);
        }

        return checkResultMap;
    }

    private void checkRequiredNodesMissing(
            List<BookNode> currentNodes,
            List<BookNode> publishedNodes,
            Map<String, String> resultMap) {
        Map<Class<? extends BookNode>, String> requiredNodes = Map.of(
                BookBasic.class, "缺少基本信息节点",
//                BookCopyright.class, "缺少版权信息节点",
                BookIntro.class, "缺少简介信息节点"
        );
        requiredNodes.forEach((nodeClass, errorMsg) -> {
            boolean existsInCurrent = containsNodeOfType(currentNodes, nodeClass);
            boolean existsInPublished = containsNodeOfType(publishedNodes, nodeClass);
            if (!existsInCurrent && !existsInPublished) {
                resultMap.put(nodeClass.getSimpleName(), errorMsg);
            }
        });
    }

    // 辅助方法：检查列表中是否存在指定类型的节点
    private <T extends BookNode> boolean containsNodeOfType(List<BookNode> nodes, Class<T> nodeClass) {
        return nodes.stream()
                .anyMatch(nodeClass::isInstance);
    }
}
