package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.utils.DateUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.UserAnswerNodeData;
import com.unipus.digitalbook.model.enums.LearnTypeEnum;
import com.unipus.digitalbook.service.BookVersionService;
import com.unipus.digitalbook.service.QuestionAnswerPushService;
import com.unipus.digitalbook.service.remote.restful.ucontent.BaseResponse;
import com.unipus.digitalbook.service.remote.restful.ucontent.PushResponseData;
import com.unipus.digitalbook.service.remote.restful.ucontent.UcontentApiService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;

@Service
@Slf4j
public class QuestionAnswerPushServiceImpl implements QuestionAnswerPushService {
    @Resource
    private UcontentApiService ucontentApiService;

    @Resource
    private BookVersionService bookVersionService;
    @Override
    public BaseResponse<PushResponseData> pushUserAnswerToThird(BigQuestionGroup question, List<UserAnswer> userAnswers, Long chapterVersionId, String dataPackage, String clientIp) {
        BookVersion bookVersion = bookVersionService.getBookVersionByChapterVersionId(chapterVersionId);
        String bookId = bookVersion.getBookId();
        String bookVersionNumber = bookVersion.getVersionNum();
        UserAnswerNodeData node = new UserAnswerNodeData(userAnswers.getFirst().getOpenId(), LearnTypeEnum.QUESTION, bookId, bookVersionNumber, clientIp)
                .toQuestionUserAnswerNodeData(dataPackage, question, userAnswers);
        log.info("sync userAnswer node {}", JsonUtil.toJsonString(node));
        BaseResponse<PushResponseData> response = ucontentApiService.submit(node);
        log.info("sync userAnswer response {}", JsonUtil.toJsonString(response));
        if (response == null || !response.isSuccess() || response.getData() == null) {
            throw new IllegalStateException("同步用户作答结果失败");
        }
        PushResponseData.StrategyNode strategyNode = response.getData().getStrategyNode();
        // 不同租户可能有不同的结果解析逻辑
        if (!response.getData().isPass() &&  strategyNode != null) {
            // 没有配置策略，直接返回
            if (strategyNode.getStartTime() != 0 && strategyNode.getEndTime() != 0) {
                long nowEpochSecond = Instant.now().getEpochSecond();
                if (strategyNode.getStartTime() > nowEpochSecond || strategyNode.getEndTime() < nowEpochSecond) {
                    LocalDate startDate = Instant.ofEpochSecond(strategyNode.getStartTime()).atZone(ZoneId.systemDefault()).toLocalDate();
                    LocalDate endDate = Instant.ofEpochSecond(strategyNode.getEndTime()).atZone(ZoneId.systemDefault()).toLocalDate();
                    throw new IllegalStateException(String.format("非单元有效期学习成绩不统计，请在单元有效期%s～%s内学习",
                            startDate.format(DateUtil.FORMATTER_DAY),
                            endDate.format(DateUtil.FORMATTER_DAY)));
                }
            }
            if (strategyNode.getTask_mini_score_pct() > 0) {
                throw new IllegalStateException(String.format("客观题正确率未超过%s%%，请重新作答", strategyNode.getTask_mini_score_pct() * 100d));
            }
        }
        return response;
    }

    @Override
    public BaseResponse<PushResponseData> pushUserContentProgressToThird(String openId, String nodeId, String nodeType, String bookId, String bookVersionNumber, String dataPackage, String clientIp) {
        UserAnswerNodeData nodeProgress = new UserAnswerNodeData(openId, LearnTypeEnum.CONTENT, bookId, bookVersionNumber, clientIp)
                .toNodeProgress(dataPackage, nodeId, nodeType);
        log.info("sync nodeProgress node {}", JsonUtil.toJsonString(nodeProgress));
        BaseResponse<PushResponseData> response = ucontentApiService.submit(nodeProgress);
        log.info("sync nodeProgress response {}", JsonUtil.toJsonString(response));
        if (response == null || !response.isSuccess() || response.getData() == null) {
            throw new IllegalStateException("同步用户作答结果失败");
        }
        if (!response.getData().isPass()) {
            log.debug("sync nodeProgress response is not pass {}, {}", bookVersionNumber, nodeId);
        }
        return response;
    }
}
