package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.dao.QuestionThirdMappingPOMapper;
import com.unipus.digitalbook.grpc.QuesGrpcAddResponse;
import com.unipus.digitalbook.grpc.QuestionClient;
import com.unipus.digitalbook.listener.CustomRetryTemplate;
import com.unipus.digitalbook.model.entity.AsyncFailLog;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.po.question.QuestionThirdMappingPO;
import com.unipus.digitalbook.service.AsyncFailLogService;
import com.unipus.digitalbook.service.QuestionPushService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
public class QuestionPushServiceImpl implements QuestionPushService {
    private static final String SYNC_THIRD_QUESTION = "syncThirdQuestion";
    @Resource
    private QuestionClient questionClient;
    @Resource
    private QuestionThirdMappingPOMapper questionThirdMappingPOMapper;
    @Resource
    private CustomRetryTemplate customRetryTemplate;

    @Resource
    private AsyncFailLogService asyncFailLogService;

    @Override
    public CompletableFuture<String> pushQuestionToThirdAsync(BigQuestionGroup question, Long tenantId) {
        return CompletableFuture.supplyAsync(() -> {
                return customRetryTemplate.handle(new PushQuestionEvent(question, tenantId), this::pushQuestionToThird, SYNC_THIRD_QUESTION);
        });
    }

    private String pushQuestionToThird(PushQuestionEvent event) {
        log.info("sync questionId {}", event.getQuestion().getBizGroupId());
        BigQuestionGroup eventQuestion = event.getQuestion();
        QuesGrpcAddResponse quesGrpcAddResponse = questionClient.addQues(event.getQuestion());
        long id = quesGrpcAddResponse.getId();
        int version = quesGrpcAddResponse.getVersion();
        QuestionThirdMappingPO questionThirdMappingPO = new QuestionThirdMappingPO(eventQuestion);
        questionThirdMappingPO.setThirdId(String.valueOf(id));
        questionThirdMappingPO.setThirdVersion(String.valueOf(version));
        questionThirdMappingPO.setTenantId(event.getTenantId());
        questionThirdMappingPOMapper.insertSelective(questionThirdMappingPO);
        return String.valueOf(id);
    }
    @Override
    public boolean checkQuestionPushStatus() {
        int count = asyncFailLogService.selectFailCountByOperation(SYNC_THIRD_QUESTION);
        log.info( "同步失败数量：{}", count);
        return count == 0;
    }

    @Override
    public void retryPushQuestionToThird() {
        log.info("开始重试同步失败的题");
        List<AsyncFailLog> failLogs = asyncFailLogService.getByOperation(SYNC_THIRD_QUESTION);
        failLogs.forEach(failLog -> {
            log.info("同步失败，开始重试：{}, object: {}", failLog.getId(), failLog.getObject());
            PushQuestionEvent pushQuestionEvent = JsonUtil.parseObject(failLog.getObject(), PushQuestionEvent.class);
            customRetryTemplate.handle(pushQuestionEvent, this::pushQuestionToThird,  SYNC_THIRD_QUESTION);
        });
        log.info("同步失败数量：{}", failLogs.size());
    }

    private static class PushQuestionEvent {
        private BigQuestionGroup question;
        private Long tenantId;
        private PushQuestionEvent() {
        }
        public PushQuestionEvent(BigQuestionGroup question, Long tenantId) {
            this.question = question;
            this.tenantId = tenantId;
        }

        public BigQuestionGroup getQuestion() {
            return question;
        }

        public void setQuestion(BigQuestionGroup question) {
            this.question = question;
        }

        public Long getTenantId() {
            return tenantId;
        }

        public void setTenantId(Long tenantId) {
            this.tenantId = tenantId;
        }
    }
}
