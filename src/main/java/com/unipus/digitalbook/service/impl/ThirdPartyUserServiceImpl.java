package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.common.exception.user.ThirdPartyUserException;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.dao.ThirdPartyUserInfoPOMapper;
import com.unipus.digitalbook.model.constants.CacheConstant;
import com.unipus.digitalbook.model.entity.ThirdPartyUserInfo;
import com.unipus.digitalbook.model.po.ThirdPartyUserInfoPO;
import com.unipus.digitalbook.service.ThirdPartyUserService;
import com.unipus.digitalbook.service.remote.restful.sso.SsoService;
import com.unipus.digitalbook.service.remote.restful.sso.response.SsoUserResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 第三方用户服务实现类
 *
 * <AUTHOR>
 * @date 2025/6/23 18:21
 */
@Slf4j
@Service
public class ThirdPartyUserServiceImpl implements ThirdPartyUserService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private SsoService ssoService;

    @Resource
    private ThirdPartyUserInfoPOMapper thirdPartyUserInfoPOMapper;

    @Override
    public ThirdPartyUserInfo getUserInfoByOpenId(Long tenantId, String openId) {
        if (tenantId == null || !StringUtils.hasText(openId)) {
            return null;
        }
        // 首先从本地数据库查询
        ThirdPartyUserInfoPO thirdPartyUserInfoPO = thirdPartyUserInfoPOMapper.selectByOpenIdAndTenantId(openId, tenantId);
        if (thirdPartyUserInfoPO != null) {
            return thirdPartyUserInfoPO.toEntity();
        }

        // 本地查询不到，调用第三方服务获取用户信息
        log.info("本地数据库未找到用户信息，调用第三方服务获取，OpenId: {}", openId);
        // 根据租户ID调用不同的第三方服务
        ThirdPartyUserInfo thirdPartyUserInfo = null;
        if (1 == tenantId) {
            ResponseEntity<SsoUserResponse> responseEntity = ssoService.getUserByOpenId(openId);
            if (responseEntity == null || !HttpStatus.OK.equals(responseEntity.getStatusCode()) || responseEntity.getBody() == null) {
                log.error("第三方获取用户信息服务调用失败，OpenId: {}, 状态码: {}", openId, responseEntity != null ? responseEntity.getStatusCode() : "null");
                throw new ThirdPartyUserException("第三方获取用户信息服务调用失败");
            }
            SsoUserResponse ssoUserResponse = responseEntity.getBody();
            Boolean status = ssoUserResponse.getStatus();
            if (Boolean.FALSE.equals(status)) {
                log.error("第三方获取用户信息返回错误，OpenId: {}, code: {}, msg: {}", openId, ssoUserResponse.getCode(), ssoUserResponse.getMsg());
                throw new ThirdPartyUserException("第三方获取用户信息返回错误");
            }
            // 创建第三方用户信息并保存到数据库
            SsoUserResponse.Rs rs = ssoUserResponse.getRs();
            thirdPartyUserInfo = new ThirdPartyUserInfo();
            thirdPartyUserInfo.setOpenId(openId);
            thirdPartyUserInfo.setNickName(rs.getNickname());
            thirdPartyUserInfo.setFullName(rs.getFullname());
            thirdPartyUserInfo.setUserName(rs.getUsername());
            thirdPartyUserInfo.setMobile(rs.getMobile());
            thirdPartyUserInfo.setEmail(rs.getEmail());
            thirdPartyUserInfo.setTenantId(tenantId);
        }

        // 保存到本地数据库
        thirdPartyUserInfoPO = ThirdPartyUserInfoPO.fromEntity(thirdPartyUserInfo);
        thirdPartyUserInfoPOMapper.insertSelective(thirdPartyUserInfoPO);

        // 缓存用户信息到redis中
        stringRedisTemplate.opsForValue().set(CacheConstant.REDIS_THIRD_PARTY_USR_PREFIX + thirdPartyUserInfoPO.getId(),
                Objects.requireNonNull(JsonUtil.toJsonString(thirdPartyUserInfoPO)), CacheConstant.REDIS_USER_TIMEOUT_SECONDS, TimeUnit.SECONDS);

        return thirdPartyUserInfo;
    }

    /**
     * 根据第三方用户ID获取用户信息
     *
     * @param userId 第三方用户ID
     * @return 第三方用户信息实体
     */
    @Override
    public ThirdPartyUserInfo getUserInfoByUserId(Long userId) {
        // 先查询redis缓存
        String infojson = stringRedisTemplate.opsForValue().get(CacheConstant.REDIS_THIRD_PARTY_USR_PREFIX + userId);
        if (StringUtils.hasText(infojson)) {
            return JsonUtil.parseObject(infojson, ThirdPartyUserInfo.class);
        }
        ThirdPartyUserInfoPO thirdPartyUserInfoPO = thirdPartyUserInfoPOMapper.selectByPrimaryKey(userId);
        return thirdPartyUserInfoPO.toEntity();
    }

    @Override
    public String saveThirdPartyUser(ThirdPartyUserInfo thirdPartyUserInfo, Long userId) {
        ThirdPartyUserInfoPO thirdPartyUserInfoPO = new ThirdPartyUserInfoPO();
        if (Objects.isNull(thirdPartyUserInfo.getId())) {
            thirdPartyUserInfoPO.setCreateBy(userId);
            thirdPartyUserInfoPOMapper.insertSelective(thirdPartyUserInfoPO);
        } else {
            thirdPartyUserInfoPO.setUpdateBy(userId);
            thirdPartyUserInfoPOMapper.updateByPrimaryKeySelective(thirdPartyUserInfoPO);
        }
        log.info("保存第三方用户信息成功，{}", thirdPartyUserInfoPO);
        return thirdPartyUserInfoPO.getId().toString();
    }
}
