package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.BookPublishPackagePOMapper;
import com.unipus.digitalbook.dao.UserChapterProgressPOMapper;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.entity.action.ReadProgressList;
import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import com.unipus.digitalbook.model.enums.PublishContentTypeEnum;
import com.unipus.digitalbook.model.events.UserActionEvent;
import com.unipus.digitalbook.model.po.BookPublishPackagePO;
import com.unipus.digitalbook.model.po.book.BookPublishItem;
import com.unipus.digitalbook.model.po.question.UserChapterProgressPO;
import com.unipus.digitalbook.publisher.UserActionPublisher;
import com.unipus.digitalbook.service.ChapterService;
import com.unipus.digitalbook.service.UserActionService;
import com.unipus.digitalbook.service.useraction.nodestrategy.NodeCompletionStrategyFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j
public class UserActionServiceImpl implements UserActionService {
    @Resource
    private ChapterService chapterService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private UserChapterProgressPOMapper userChapterProgressPOMapper;
    @Resource
    private NodeCompletionStrategyFactory nodeCompletionStrategyFactory;
    @Resource
    private UserActionPublisher userActionPublisher;

    @Resource
    private BookPublishPackagePOMapper bookPublishPackagePOMapper;

    @Override
    public Response<Boolean> finishNode(UserAction userAction) {
        Long chapterVersionId = userAction.getChapterVersionId();
        ChapterNode chapterNode = userAction.getChapterNode();
        if (chapterNode == null) {
            log.error("节点不存在");
            return Response.fail("节点不存在");
        }
        boolean completed = nodeCompletionStrategyFactory.getStrategy(chapterNode.getType()).isCompleted(chapterNode, userAction);
        if (!completed) {
            log.debug("节点未完成 {}", chapterNode.getId());
            return Response.success(false);
        }
        Boolean finished = finishNode(userAction.getTenantId(), userAction.getOpenId(), chapterVersionId, chapterNode.getOffset());
        userActionPublisher.send(new UserActionEvent(userAction, chapterNode, userAction.getChapterId(), chapterVersionId, chapterNode.getOffset()));
        return Response.success(finished);
    }

    @Override
    public Response<Boolean> directFinishNodeWithOutSendMessage(Long tenantId, String openId, String chapterId, Long chapterVersionId, String nodeId) {
        Map<String, ChapterNode> nodeMap = chapterService.getNodeMapByChapterVersionId(chapterVersionId);
        ChapterNode chapterNode = nodeMap.get(nodeId);
        if (chapterNode == null) {
            log.debug("节点可能已变更 {}", nodeId);
            return Response.fail("节点可能已变更");
        }
        Boolean finished = finishNode(tenantId, openId, chapterVersionId, chapterNode.getOffset());
        // todo 保证写入成功获取数据
        byte[] bit = getProgressFromCache(tenantId, openId, chapterVersionId);
        userChapterProgressPOMapper.saveProgressBit(tenantId, openId, chapterId, chapterVersionId, bit);
        return Response.success(finished);
    }

    @Override
    public Response<ReadProgressList> getChapterProgress(Long tenantId, String openId, Long chapterVersionId) {
        Map<String, ChapterNode> nodeMap = chapterService.getNodeMapByChapterVersionId(chapterVersionId);
        if (nodeMap.isEmpty()) {
            return Response.success();
        }
        byte[] progressBitBit = getProgressFromCache(tenantId, openId, chapterVersionId);
        ReadProgressList readProgressList = new ReadProgressList()
                .computation(progressBitBit, nodeMap);
        return Response.success(readProgressList);
    }

    @Override
    public Response<Boolean> getNodeProgress(Long tenantId, String openId, Long chapterVersionId, String nodeId) {
        Map<String, ChapterNode> nodeMap = chapterService.getNodeMapByChapterVersionId(chapterVersionId);
        if (nodeMap.isEmpty()) {
            return Response.success(false);
        }
        ChapterNode chapterNode = nodeMap.get(nodeId);
        if (chapterNode == null) {
            log.error("节点不存在 {} {}", chapterVersionId, nodeId);
            return Response.fail("节点不存在");
        }
        String progressKey = getProgressKey(tenantId, openId, chapterVersionId);
        Boolean bit = stringRedisTemplate.opsForValue().getBit(progressKey, chapterNode.getOffset());
        return Response.success(bit);
    }

    @Override
    public Response<byte[]> getNodeProgressValueFromCache(Long tenantId, String openId, Long chapterVersionId) {
        return Response.success(getProgressFromCache(tenantId, openId, chapterVersionId));
    }

    @Override
    public Response<Boolean> clearChapterProgress(Long tenantId, String openId, Long chapterVersionId) {
        userChapterProgressPOMapper.clearProgressBit(tenantId, openId, chapterVersionId);
        stringRedisTemplate.delete(getProgressKey(tenantId, openId, chapterVersionId));
        return Response.success(true);
    }

    @Override
    public Response<Boolean> migratedProgress(Long tenantId, String bookId, String bookVersion) {
        log.info("start migrate progress: {}, {}", bookId, bookVersion);
        // 查询当前版本发布的教材的章节列表
        BookPublishPackagePO bookPublishPackagePO = bookPublishPackagePOMapper.selectByBookIdAndVersionNumber(bookId, bookVersion);
        if (bookPublishPackagePO == null) {
            log.error("no book publish package found for bookId: {}, version: {}", bookId, bookVersion);
            return Response.fail("no book publish package");
        }
        List<BookPublishItem> publishPackage = bookPublishPackagePO.getPublishPackage();
        if (publishPackage == null || publishPackage.isEmpty()) {
            log.debug("no publish package found for bookId: {}, version: {}", bookId, bookVersion);
            return Response.success();
        }
        List<BookPublishItem> publishChapterList = publishPackage.stream().filter(item -> PublishContentTypeEnum.CHAPTER_CONTENT.match(item.getTypeCode())).toList();
        if (publishChapterList.isEmpty()) {
            log.debug("no publish chapter found for bookId: {}, version: {}", bookId, bookVersion);
            return Response.success();
        }
        publishChapterList.forEach(item -> {
            Long versionId = item.getVersionId();
            String chapterId = item.getResourceId();
            String latestChapterProgressKey = getLatestChapterVersionKey(tenantId, chapterId);
            stringRedisTemplate.opsForValue().set(latestChapterProgressKey, String.valueOf(versionId), Duration.ofDays(30));
            Map<String, ChapterNode> nodeMap = chapterService.getNodeMapByChapterVersionId(versionId);
            log.debug("migrate chapterId: {}", chapterId);
            // 获取章节下所有用户当前章节的进度数据
            List<UserChapterProgressPO> oldProgressList = userChapterProgressPOMapper.getLatestProgressList(tenantId, chapterId);
            oldProgressList.forEach(progress -> {
                // 开始迁移用户章节数据
                log.debug("migrate openId: {}", progress.getOpenId());
                Long oldChapterVersionId = progress.getChapterVersionId();
                migrateUserProgress(tenantId, progress.getOpenId(), oldChapterVersionId, versionId, chapterId, nodeMap);
            });
        });
        return Response.success();
    }

    @Override
    public Response<Long> getLatestPublishChapterVersionId(Long tenantId, String chapterId) {
        String chapterIdStr = stringRedisTemplate.opsForValue().get(getLatestChapterVersionKey(tenantId, chapterId));
        if (!StringUtils.hasText(chapterIdStr)) {
            return Response.success();
        }
        return Response.success(Long.valueOf(chapterIdStr));
    }

    private String getLatestChapterVersionKey(Long tenantId, String chapterId) {
        return String.format("latest:publish:chapter:%s:%s", tenantId, chapterId);
    }

    private void migrateUserProgress(Long tenantId, String openId, Long oldChapterVersionId, Long chapterVersionId, String chapterId, Map<String, ChapterNode> chapterNodeMap) {
        ReadProgressList data = getChapterProgress(tenantId, openId, oldChapterVersionId).getData();
        if (data == null || data.getCompletedList() == null) {
            return;
        }
        Set<String> completed = new HashSet<>(data.getCompletedList());
        if  (completed.isEmpty()) {
            return;
        }
        String progressKey = getProgressKey(tenantId, openId, chapterVersionId);
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            byte[] rawKey = ((RedisSerializer<String>) stringRedisTemplate.getKeySerializer())
                    .serialize(progressKey);
            RedisStringCommands redisStringCommands = connection.stringCommands();
            completed.stream().parallel().forEach(nodeId -> {
                ChapterNode chapterNode = chapterNodeMap.get(nodeId);
                if (chapterNode != null) {
                    redisStringCommands.setBit(rawKey, chapterNode.getOffset(), true);
                }
            });
            return true;
        });
        Response<byte[]> progress = getNodeProgressValueFromCache(
                tenantId,
                openId,
                chapterVersionId);
        userChapterProgressPOMapper.saveProgressBit(tenantId,
                openId,
                chapterId,
                chapterVersionId,
                progress.getData());

    }


    /**
     * 获取进度
     * @param tenantId 租户ID
     * @param openId 用户ID
     * @param chapterVersionId 章节版本ID
     * @return 进度
     */
    private byte[] getProgressFromCache(Long tenantId, String openId, Long chapterVersionId) {
        String progressKey = getProgressKey(tenantId, openId, chapterVersionId);
        byte[] progressRawKey = ((RedisSerializer<String>)stringRedisTemplate.getKeySerializer())
                .serialize(progressKey);
        return stringRedisTemplate.execute((RedisCallback<byte[]>) connection ->
                connection.stringCommands().get(progressRawKey)
        );
    }

    /**
     * 设置进度
     * @param tenantId 租户ID
     * @param openId 用户ID
     * @param chapterVersionId 章节版本ID
     * @param offset 位置
     * @return 是否成功
     */
    private Boolean finishNode(Long tenantId, String openId, Long chapterVersionId, int offset) {
        String progressKey = getProgressKey(tenantId, openId, chapterVersionId);
        stringRedisTemplate.opsForValue().setBit(progressKey, offset, true);
        return true;
    }

    private String getProgressKey(Long tenantId, String openId, Long chapterVersionId) {
        return String.format("progress:%d:%s:%d", tenantId, openId, chapterVersionId);
    }
}
