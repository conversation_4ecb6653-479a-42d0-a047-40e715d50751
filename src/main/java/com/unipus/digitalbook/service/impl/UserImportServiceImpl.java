package com.unipus.digitalbook.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.util.FileUtils;
import com.alibaba.excel.util.IoUtils;
import com.unipus.digitalbook.common.exception.user.UserImportException;
import com.unipus.digitalbook.common.utils.COSUtil;
import com.unipus.digitalbook.common.utils.ExcelUtil;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.dao.UserImportResultPOMapper;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.constants.CommonConstant;
import com.unipus.digitalbook.model.entity.Organization;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.role.Role;
import com.unipus.digitalbook.model.entity.user.UserImportResult;
import com.unipus.digitalbook.model.entity.user.UserImportTemplate;
import com.unipus.digitalbook.model.enums.UserImportStatusEnum;
import com.unipus.digitalbook.model.po.user.UserImportResultPO;
import com.unipus.digitalbook.service.OrgService;
import com.unipus.digitalbook.service.RoleService;
import com.unipus.digitalbook.service.UserImportService;
import com.unipus.digitalbook.service.UserService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/6 14:10
 */
@Slf4j
@Service
public class UserImportServiceImpl implements UserImportService {

    @Resource
    private UserImportResultPOMapper userImportResultPOMapper;
    @Resource
    private COSUtil cosUtil;
    @Resource
    RoleService roleService;
    @Resource
    UserService userService;
    @Resource
    OrgService orgService;

    @Override
    public Long save(UserImportResult userImportResult) {
        UserImportResultPO userImportResultPO = new UserImportResultPO(userImportResult);
        userImportResultPOMapper.insertSelective(userImportResultPO);
        return userImportResultPO.getId();
    }

    @Override
    public UserImportResult getById(Long id) {
        UserImportResultPO userImportResultPO = userImportResultPOMapper.selectByPrimaryKey(id);
        if (userImportResultPO == null) {
            return null;
        }
        return userImportResultPO.toUserImportResult();
    }

    @Override
    public Boolean updateById(UserImportResult userImportResult) {
        UserImportResultPO userImportResultPO = new UserImportResultPO(userImportResult);
        userImportResultPOMapper.updateByPrimaryKeySelective(userImportResultPO);
        return true;
    }

    /**
     * 下载Excel用户导入模板
     *
     * @param orgId    组织ID
     * @param response HTTP响应对象
     */
    @Override
    public void downloadTemplate(Long orgId, HttpServletResponse response) {
        try {
            if (orgId == null || orgId <= 0) {
                throw new IllegalArgumentException("机构ID不能为空");
            }
            Organization organization = orgService.getOrgDetail(orgId);
            if (organization == null) {
                throw new IllegalArgumentException("所选机构不存在");
            }
            String orgName = organization.getOrgName();
            List<Role> roleList = roleService.getByOrgId(orgId);
            if (CollectionUtils.isEmpty(roleList)) {
                throw new IllegalArgumentException("该机构未设置角色，无法使用批量导入，请联系平台管理员");
            }
            // 设置下拉列表
            List<Map<String, String>> data = roleList.stream().map(role -> {
                Map<String, String> dataMap = new HashMap<>();
                dataMap.put("key", role.getName());
                dataMap.put("value", role.getId().toString());
                return dataMap;
            }).collect(Collectors.toList());
            String templateFilePath = new ApplicationHome(getClass()).getSource().getParent() + "/用户导入模板.xlsx";
            try (Workbook workbook = WorkbookFactory.create(new ByteArrayInputStream(FileUtils.readFileToByteArray(new File(templateFilePath))))) {
                Sheet sheet = workbook.getSheet("用户数据");
                sheet.getRow(0).getCell(0).setCellValue(orgName);
                // 创建一个隐藏的工作表来存储key-value映射
                ExcelUtil.addDropDownList(workbook, sheet, 2, 501, 2, data, "用户角色Code");
                //ExcelUtil.addDropDownList(workbook, sheet, data, "用户角色Code");

                response.setCharacterEncoding("utf-8");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                // 这里URLEncoder.encode可以防止中文乱码
                String fileName = URLEncoder.encode("用户导入模板_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
                response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
                response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
                workbook.write(response.getOutputStream());
            }
        } catch (Exception e) {
            String errorMessage = e instanceof IllegalArgumentException ? e.getMessage() : "下载Excel用户导入模板失败";
            if (!(e instanceof IllegalArgumentException)) {
                log.error(errorMessage, e);
            }
            try {
                Response.writeErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, errorMessage);
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
                throw new UserImportException("写入错误响应失败");
            }
        }
    }

    /**
     * 通过Excel文件导入用户数据
     *
     * @param userId 用户ID
     * @param orgId  机构ID
     * @param file   要导入的Excel文件
     * @return 返回用户导入结果的ID
     */
    @Override
    public Long importByExcel(Long userId, Long orgId, MultipartFile file) {
        // 校验参数
        validateImportTemplateParams(userId, orgId, file);
        String originalFilename = file.getOriginalFilename();
        // 读取文件
        byte[] bytes;
        try (InputStream inputStream = file.getInputStream()) {
            bytes = IoUtils.toByteArray(inputStream);
        } catch (IOException e) {
            throw new IllegalArgumentException("导入的模板文件错误，请检查导入模板文件");
        }

        // 读取并解析Excel文件
        Organization organization = orgService.getOrgDetail(orgId);
        if (organization == null || orgId <= 0) {
            throw new IllegalArgumentException("所选机构不存在");
        }
        String orgName = organization.getOrgName();
        List<UserImportTemplate> rowList = readAndParseRowList(bytes, orgName);
        // 插入用户导入结果表
        Long taskId = createUserImportResult(userId, orgId, originalFilename, bytes);
        // 异步执行
        CompletableFuture.runAsync(() -> {
            List<Role> roleList = roleService.getByOrgId(orgId);
            Set<String> cellPhoneSet = new HashSet<>();
            // 校验数据
            rowList.forEach(userImportTemplate -> validateUserImportTemplate(userImportTemplate, roleList, cellPhoneSet));
            // 虚拟线程处理用户信息
            try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
                CompletableFuture.allOf(rowList.stream().filter(UserImportTemplate::isValid).map(userImportTemplate -> CompletableFuture.runAsync(() -> {
                    try {
                        // 添加用户
                        UserInfo userInfo = new UserInfo();
                        userInfo.setName(userImportTemplate.getNickName());
                        userInfo.setCellPhone(userImportTemplate.getCellPhone());
                        userService.addUser(userInfo, orgId, List.of(Long.valueOf(userImportTemplate.getRoleCode())), userId);
                    } catch (Exception e) {
                        log.error("添加用户失败, params:{}", JsonUtil.toJsonString(userImportTemplate), e);
                        userImportTemplate.setValid(false);
                        userImportTemplate.setErrorMsg(e.getMessage() + ";");
                    }
                }, executor)).toArray(CompletableFuture[]::new)).join();
            }
            updateImportResult(taskId, userId, rowList, orgName);
        });
        return taskId;
    }

    /**
     * 验证导入模板参数的有效性
     *
     * @param userId 用户ID，用于标识用户
     * @param orgId  机构ID，用于标识机构
     * @param file   上传的文件，应为Excel格式
     * @throws IllegalArgumentException 如果用户ID或机构ID为空或无效，或文件为空或非Excel格式，则抛出此异常
     */
    private void validateImportTemplateParams(Long userId, Long orgId, MultipartFile file) {
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户id不能为空");
        }
        if (orgId == null || orgId <= 0) {
            throw new IllegalArgumentException("机构id不能为空");
        }
        // 验证文件是否为空
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }
        // 验证文件是否为Excel文件
        String originalFilename = file.getOriginalFilename();
        // 检查文件名是否为 null
        if (originalFilename == null) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        // 将文件名转换为小写
        String lowercaseFilename = originalFilename.toLowerCase();
        // 检查文件扩展名
        if (!lowercaseFilename.endsWith(CommonConstant.XLS_FILE_SUFFIX) && !lowercaseFilename.endsWith(CommonConstant.XLSX_FILE_SUFFIX)) {
            throw new IllegalArgumentException("文件必须是Excel格式（.xls 或 .xlsx）");
        }
    }

    /**
     * 读取并解析用户导入模板的Excel文件
     * 该方法使用了EasyExcel库来解析Excel文件，并将解析后的数据存储在List中
     *
     * @param bytes   Excel文件的字节数组
     * @param orgName 组织名称，用于验证Excel文件是否属于该组织
     * @return 解析后的用户导入模板列表
     */
    private List<UserImportTemplate> readAndParseRowList(byte[] bytes, String orgName) {
        try (Workbook workbook = WorkbookFactory.create(new ByteArrayInputStream(bytes))) {
            Sheet sheet = workbook.getSheet("用户数据");
            if (sheet == null) {
                throw new IllegalArgumentException("导入的模板文件有误，请检查导入模板文件");
            }
        } catch (IOException e) {
            throw new IllegalArgumentException("导入的模板文件有误，请检查导入模板文件");
        }
        List<UserImportTemplate> rowList = new ArrayList<>();
        try (ExcelReader excelReader = EasyExcel.read(new ByteArrayInputStream(bytes)).build()) {
            ReadSheet readSheet = EasyExcel.readSheet("用户数据").head(UserImportTemplate.class).headRowNumber(2)
                    .registerReadListener(new AnalysisEventListener<UserImportTemplate>() {
                        private boolean matchTemplate = false;

                        @Override
                        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                            context.readWorkbookHolder().setIgnoreEmptyRow(false);
                            // 判断第一行的标题是否使用当前机构的模版导入
                            if (context.readRowHolder().getRowIndex().equals(context.readSheetHolder().getHeadRowNumber() - 2) && !orgName.equals(headMap.get(0))) {
                                throw new IllegalArgumentException("导入文件的所属机构与所选机构不一致");
                            } else if (!context.readRowHolder().getRowIndex().equals(context.readSheetHolder().getHeadRowNumber() - 1)) {
                                return;
                            }
                            if (CollectionUtils.isEmpty(headMap)) {
                                throw new IllegalArgumentException("导入的模板文件表标题错误，请检查导入模板文件");
                            }
                            // 读取实体类头信息
                            Map<Integer, String> baseHeadMap = new HashMap<>();
                            context.currentReadHolder().excelReadHeadProperty().getHeadMap()
                                    .forEach((headIndex, head) -> baseHeadMap.put(headIndex, head.getHeadNameList().getLast()));
                            // 表头检测
                            baseHeadMap.forEach((headIndex, headName) -> {
                                if (!headName.equals(headMap.get(headIndex))) {
                                    throw new IllegalArgumentException("导入的模板文件表标题与定义标题不符合，请检查导入模板文件");
                                }
                            });
                            matchTemplate = true;
                        }

                        @Override
                        public void invoke(UserImportTemplate data, AnalysisContext context) {
                            if (rowList.size() >= 500) {
                                throw new IllegalArgumentException("上传文件处理件数超过上限500条，请重新上传");
                            }
                            if (!StringUtils.hasText(data.getNickName()) && !StringUtils.hasText(data.getCellPhone())
                                    && !StringUtils.hasText(data.getRoleName()) && !StringUtils.hasText(data.getRoleCode())) {
                                return;
                            }
                            data.setNum(context.readRowHolder().getRowIndex() - 1);
                            rowList.add(data);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext context) {
                            if (!matchTemplate) {
                                throw new IllegalArgumentException("导入的模板文件有误，请检查导入模板文件");
                            }
                            if (CollectionUtils.isEmpty(rowList)) {
                                throw new IllegalArgumentException("上传文件处理件数不能为空，请重新上传");
                            }
                        }
                    }).build();
            excelReader.read(readSheet);
        }
        return rowList;
    }

    /**
     * 创建用户导入结果
     *
     * @param userId           用户ID，表示执行导入操作的用户
     * @param orgId            组织ID，表示用户所属的组织
     * @param originalFilename 原始文件名，用于上传文件时保留文件的原始名称
     * @param templateBytes    文件的字节流，包含用户导入的数据
     * @return 返回保存后的用户导入结果对象
     */
    private Long createUserImportResult(Long userId, Long orgId, String originalFilename, byte[] templateBytes) {
        //String fileName = UUID.randomUUID() + originalFilename.substring(originalFilename.lastIndexOf("."));
        String excelUrl = cosUtil.getUploadUrl(UUID.randomUUID() + "_" + originalFilename, templateBytes);
        UserImportResult userImportResult = new UserImportResult();
        userImportResult.setUserId(userId);
        userImportResult.setOrgId(orgId);
        userImportResult.setExcelUrl(excelUrl);
        userImportResult.setTotal(0);
        userImportResult.setSuccessNum(0);
        userImportResult.setFailNum(0);
        userImportResult.setCreateBy(userId);
        return this.save(userImportResult);
    }

    /**
     * 验证用户导入模板的信息是否合法
     * 此方法主要检查用户昵称、手机号和角色信息是否符合规范，以及是否存在重复的手机号
     *
     * @param userImportTemplate 用户导入模板对象，包含用户信息
     * @param roleList           角色列表，用于验证角色信息是否有效
     * @param cellPhoneSet       已处理的手机号集合，用于检查手机号是否重复
     */
    private void validateUserImportTemplate(UserImportTemplate userImportTemplate, List<Role> roleList, Set<String> cellPhoneSet) {
        boolean isValid = true;
        StringBuilder errorMsg = new StringBuilder();

        String nickName = userImportTemplate.getNickName();
        if (!StringUtils.hasText(nickName)) {
            errorMsg.append("用户昵称必输有值;");
            isValid = false;
        } else if (nickName.length() < 2 || nickName.length() > 16) {
            errorMsg.append("用户昵称请输入长度2-16位;");
            isValid = false;
        } else if (!nickName.matches("^(?! )[\\u4e00-\\u9fa5a-zA-Z0-9-_ ]*(?<! )$")) {
            errorMsg.append("用户昵称请输入汉字、字母、数字、\"-\"、\"_\"、空格（首尾除外）;");
            isValid = false;
        } else if (nickName.matches("^1[3-9]\\d{9}$")) {
            errorMsg.append("用户昵称不可输入手机号;");
            isValid = false;
        }

        String cellPhone = userImportTemplate.getCellPhone();
        if (!StringUtils.hasText(cellPhone)) {
            errorMsg.append("请输入注册手机号;");
            isValid = false;
        } else if (!cellPhone.matches("^1[3-9]\\d{9}$")) {
            errorMsg.append("注册手机号必须输入11位手机号;");
            isValid = false;
        } else if (cellPhoneSet.contains(cellPhone)) {
            errorMsg.append("导入手机号重复;");
            isValid = false;
        }
        cellPhoneSet.add(cellPhone);

        String roleName = userImportTemplate.getRoleName();
        String roleCode = userImportTemplate.getRoleCode();
        if (!StringUtils.hasText(roleName) && !StringUtils.hasText(roleCode)) {
            errorMsg.append("角色名称必须选择一项;");
            isValid = false;
        } else if (roleList.stream().noneMatch(role -> role.getName().equals(roleName) && role.getId().toString().equals(roleCode))) {
            errorMsg.append("无效的角色名称;");
            isValid = false;
        }
        if (!isValid) {
            userImportTemplate.setValid(false);
            userImportTemplate.setErrorMsg(errorMsg.toString());
        }
    }

    /**
     * 更新导入结果
     *
     * @param taskId  任务ID
     * @param userId  用户ID
     * @param rowList 行列表
     * @param orgName 组织名称
     */
    private void updateImportResult(Long taskId, Long userId, List<UserImportTemplate> rowList, String orgName) {
        List<UserImportTemplate> errorList = rowList.stream().filter(userImportTemplate -> !userImportTemplate.isValid()).toList();
        String failExcelUrl = addExcelError(orgName, errorList);
        UserImportResult updateUserImportResult = new UserImportResult();
        updateUserImportResult.setId(taskId);
        updateUserImportResult.setTaskStatus(UserImportStatusEnum.COMPLETED.getCode());
        updateUserImportResult.setTaskEndTime(new Date());
        updateUserImportResult.setTotal(rowList.size());
        updateUserImportResult.setSuccessNum(rowList.size() - errorList.size());
        updateUserImportResult.setFailNum(errorList.size());
        updateUserImportResult.setFailExcelUrl(failExcelUrl);
        updateUserImportResult.setUpdateTime(new Date());
        updateUserImportResult.setUpdateBy(userId);
        // 更新数据库中的用户导入结果记录
        this.updateById(updateUserImportResult);
    }

    /**
     * 生成错误信息excel
     *
     * @param orgName   组织名称，用于填写在excel的第一行
     * @param errorList 包含错误信息的用户导入模板列表，用于生成错误信息列
     * @return 生成错误信息excel后的文件路径
     */
    private String addExcelError(String orgName, List<UserImportTemplate> errorList) {
        if (CollectionUtils.isEmpty(errorList)) {
            return null;
        }
        String templateFilePath = new ApplicationHome(getClass()).getSource().getParent() + "/用户导入模板.xlsx";
        try (Workbook workbook = WorkbookFactory.create(new ByteArrayInputStream(FileUtils.readFileToByteArray(new File(templateFilePath))));
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            Sheet sheet = workbook.getSheet("用户数据");
            sheet.getRow(0).getCell(0).setCellValue(orgName);
            // 取消隐藏列
            sheet.setColumnHidden(3, false);
            //sheet.setColumnHidden(4, false);
            // 遍历行和列来设置生成错误信息指定列的值
            int rowIndex = 2;
            for (UserImportTemplate template : errorList) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    row = sheet.createRow(rowIndex);
                }
                setCellValue(row, 0, template.getNickName());
                setCellValue(row, 1, template.getCellPhone());
                setCellValue(row, 2, template.getRoleName());
                setCellValue(row, 3, template.getErrorMsg());
//                Cell numCell = row.getCell(4);
//                if (numCell == null) {
//                    numCell = row.createCell(4);
//                }
//                numCell.setCellValue("L" + num);
                rowIndex++;
            }
            workbook.write(outputStream);
            byte[] outputBytes = outputStream.toByteArray();
            return cosUtil.getUploadUrl("用户导入失败数据_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + CommonConstant.XLSX_FILE_SUFFIX, outputBytes);
        } catch (IOException e) {
            log.error("用户导入模板失败", e);
            throw new UserImportException("用户导入模板失败");
        }
    }

    private void setCellValue(Row row, int cellIndex, String value) {
        Cell cell = row.getCell(cellIndex);
        if (cell == null) {
            cell = row.createCell(cellIndex);
        }
        cell.setCellValue(value);
    }
}
