package com.unipus.digitalbook.service.judge;

import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.question.QuestionAnswer;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.type.ChoiceQuestion;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class ChoiceJudgeStrategy implements JudgeStrategy<ChoiceQuestion> {
    @Override
    public Set<QuestionTypeEnum> supportQuestionTypes() {
        return Set.of(QuestionTypeEnum.SINGLE_CHOICE, QuestionTypeEnum.MULTI_CHOICE);
    }

    @Override
    public double judge(ChoiceQuestion choice, UserAnswer userAnswer) {
        if (userAnswer == null || userAnswer.getAnswer() == null) {
            return 0d;
        }
        int correctCount = 0;
        int wrongCount = 0;
        String answerStr = userAnswer.getAnswer();
        Set<String> userAnswers = JsonUtil.parseObject(answerStr, new TypeReference<>() {});
        for (String answer : userAnswers) {
            boolean correct = false;
            for (QuestionAnswer questionAnswer : choice.getAnswers()) {
                if (answer.equals(questionAnswer.getCorrectAnswerText())) {
                    correct = true;
                    break;
                }
            }
            if (correct) {
                correctCount++;
            } else {
                wrongCount++;
            }
        }

        if (wrongCount > 0) {
            return 0;
        }
        if (correctCount == choice.getAnswers().size() && userAnswers.size() == choice.getAnswers().size()) {
            return 1;
        }
        if (correctCount > 0) {
            return (double) correctCount /choice.getAnswers().size();
        }
        return 0;
    }
}
