package com.unipus.digitalbook.service.judge;

import com.unipus.digitalbook.model.entity.question.JudgeTaskTicket;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;

import java.util.Set;

/**
 * 定义了一个泛型接口JudgeStrategy，用于实现不同类型的题目评判策略。
 * 泛型参数T必须是Question类或其子类。
 */
public interface JudgeStrategy<T extends Question> {
    Set<QuestionTypeEnum> supportQuestionTypes();
    /**
     * 执行具体的评判逻辑。
     *
     * @param question     待评判的题目对象
     * @param userAnswer  用户的答案
     * @return 返回评判结果的准确度，取值范围为0到1之间
     */
    double judge(T question, UserAnswer userAnswer);

    /**
     * 执行开始评判逻辑。
     *
     * @param question     待评判的题目对象
     * @param userAnswer  用户的答案
     * @return 返回开始评判后的结果ID
     */
    default JudgeTaskTicket startJudgeTask(T question, UserAnswer userAnswer) {
        throw new UnsupportedOperationException("不支持的开始评判逻辑");
    }

    /**
     * 执行结束评判逻辑。
     *
     * @param userAnswer  用户的答案
     * @return 返回结束评判后的用户答案列表
     */
    default void endJudgeTask(UserAnswer userAnswer) {
        throw new UnsupportedOperationException("不支持的结束评判逻辑");
    }

    /**
     * 获取评测结果（例如评分或评语）
     * @param userAnswer 用户作答（必须包含 bizAnswerId）
     * @return 更新后的用户作答（带有评测结果）
     */
    default UserAnswer fetchJudgeResult(UserAnswer userAnswer) {
        throw new UnsupportedOperationException("不支持的评判结果逻辑");
    }
}

