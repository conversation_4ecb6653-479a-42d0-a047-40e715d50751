package com.unipus.digitalbook.service.judge;

import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.model.entity.question.JudgeTaskTicket;
import com.unipus.digitalbook.model.entity.question.QuestionAnswer;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.question.UserAnswer;
import com.unipus.digitalbook.model.entity.question.type.TranslationQuestion;
import com.unipus.digitalbook.model.enums.QuestionTypeEnum;
import com.unipus.digitalbook.service.remote.restful.engine.EngineApiManger;
import com.unipus.digitalbook.service.remote.restful.engine.EngineApiService;
import com.unipus.digitalbook.service.remote.restful.engine.model.BaseResponse;
import com.unipus.digitalbook.service.remote.restful.engine.model.CorrectResponse;
import com.unipus.digitalbook.service.remote.restful.engine.model.TranslateCorrectResult;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Component
public class TranslationJudgeStrategy implements JudgeStrategy<TranslationQuestion>{

    private static final String CACHE_KEY = "engin_%s";

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private EngineApiManger engineApiManger;

    @Resource
    private EngineApiService engineApiService;

    @Override
    public Set<QuestionTypeEnum> supportQuestionTypes() {
        return Set.of(QuestionTypeEnum.TRANSLATION);
    }

    @Override
    public double judge(TranslationQuestion question, UserAnswer userAnswer) {
        if (userAnswer == null) {
            return 0;
        }
        String bizAnswerId = userAnswer.getBizAnswerId();
        String enginICorrectKey = String.format(CACHE_KEY, bizAnswerId);
        // 获取用户作答详情
        String evaluation = stringRedisTemplate.opsForValue().get(enginICorrectKey);
        if (!StringUtils.hasText(evaluation)) {
            throw new IllegalArgumentException("用户作答失效");
        }
        userAnswer.setEvaluation(evaluation);
        // 解析作答结果
        TranslateCorrectResult translateCorrectResult = JsonUtil.parseObject(evaluation, TranslateCorrectResult.class);
        if (translateCorrectResult == null) {
            log.error("翻译作答结果数据异常: {}", bizAnswerId);
            return 0;
        }
        double total = translateCorrectResult.getScore();
        userAnswer.setScore(BigDecimal.valueOf(total));
        // 百分制
        return total / 100d;
    }

    @Override
    public JudgeTaskTicket startJudgeTask(TranslationQuestion question, UserAnswer userAnswer) {
        if (userAnswer == null) {
            throw new IllegalArgumentException("无效的用户作答");
        }
        String tgtTxt = userAnswer.getAnswer();
        QuestionText questionText = question.getQuestionText();
        List<String> refs = Optional.ofNullable(question.getAnswers()).orElse(Collections.emptyList())
                .stream().map(QuestionAnswer::getCorrectAnswerText).toList();
        // 发送评测请求
        BaseResponse<CorrectResponse> translateResponse = engineApiManger.translate(questionText.getPlainText(), tgtTxt, refs);
        log.info("questionId: {}, translateResponse:{}", question.getBizQuestionId(), translateResponse);
        if (!translateResponse.isSuccess()) {
            log.error("TranslationJudge error questionId: {}, UserAnswer: {}", question.getBizQuestionId(),userAnswer);
            throw new IllegalStateException("评测失败");
        }
        String aiTranslateId = translateResponse.getData().getAiTranslateId();
        String enginICorrectKey = String.format(CACHE_KEY, aiTranslateId);
        // 记录用户作答详情
        stringRedisTemplate.opsForValue().set(enginICorrectKey, "", Duration.ofDays(1));
        // 如何把作答的id返回给前端，让前端使用这个id进行轮训判断是否有结果，有结果之后进行评测（judge）
        userAnswer.setBizAnswerId(aiTranslateId);
        return new JudgeTaskTicket(aiTranslateId, userAnswer);
    }

    @Override
    public void endJudgeTask(UserAnswer userAnswer) {
        if (userAnswer == null) {
            throw new IllegalArgumentException("无效的用户作答");
        }
        String bizAnswerId = userAnswer.getBizAnswerId();
        BaseResponse<TranslateCorrectResult> translateResult = engineApiService.getTranslateResult(bizAnswerId);
        log.info("questionId: {}, translateResult:{}", bizAnswerId, translateResult);
        if (!translateResult.isSuccess()) {
            log.error("TranslationJudge error questionId: {},userAnswer:{}", bizAnswerId,userAnswer);
            throw new IllegalStateException("翻译引擎评测失败");
        }
        String enginICorrectKey = String.format(CACHE_KEY, bizAnswerId);
        stringRedisTemplate.opsForValue().set(enginICorrectKey, JsonUtil.toJsonString(translateResult.getData()), Duration.ofDays(1));
    }

    @Override
    public UserAnswer fetchJudgeResult(UserAnswer userAnswer) {
        if (userAnswer == null) {
            throw new IllegalArgumentException("无效的用户作答");
        }
        String enginICorrectKey = String.format(CACHE_KEY, userAnswer.getBizAnswerId());
        String evaluation = stringRedisTemplate.opsForValue().get(enginICorrectKey);
        userAnswer.setEvaluation(evaluation);
        return userAnswer;
    }
}
