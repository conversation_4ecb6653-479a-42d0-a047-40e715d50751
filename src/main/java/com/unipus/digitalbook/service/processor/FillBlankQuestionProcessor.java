package com.unipus.digitalbook.service.processor;

import com.unipus.digitalbook.model.entity.question.type.FillBlankQuestion;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import org.springframework.stereotype.Component;

import java.util.EnumSet;

@Component
public class FillBlankQuestionProcessor extends AbstractQuestionProcessor<FillBlankQuestion> {

    public FillBlankQuestionProcessor() {
        super(EnumSet.of(QuestionGroupTypeEnum.FILL_BLANKS, QuestionGroupTypeEnum.FILL_BLANKS_DROPDOWN, QuestionGroupTypeEnum.FILL_BLANKS_CHOICE));
    }

    @Override
    public FillBlankQuestion toQuestion() {
        return new FillBlankQuestion();
    }
}