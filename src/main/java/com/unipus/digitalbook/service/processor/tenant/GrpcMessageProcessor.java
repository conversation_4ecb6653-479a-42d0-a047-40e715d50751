package com.unipus.digitalbook.service.processor.tenant;

import com.alibaba.fastjson2.TypeReference;
import com.unipus.digitalbook.common.exception.tenant.TenantMessageException;
import com.unipus.digitalbook.common.utils.JsonUtil;
import com.unipus.digitalbook.conf.tenant.GrpcDiscoveryConfig;
import com.unipus.digitalbook.model.enums.ChannelEnum;
import com.unipus.digitalbook.model.enums.MessageTopicEnum;
import com.unipus.digitalbook.model.enums.ProduceResultEnum;
import com.unipus.digitalbook.model.po.tenant.TenantChannelPO;
import com.unipus.digitalbook.model.po.tenant.TenantSubscribePO;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Component
@Slf4j
public class GrpcMessageProcessor extends BaseMessageProcessor {

    @Resource
    private GrpcDiscoveryConfig grpcDiscoveryConfig;

    @Override
    public ChannelEnum supportChannel() {
        return ChannelEnum.GRPC;
    }

    @Override
    @SneakyThrows
    public <S,T> T process(TenantSubscribePO tenantSubscribePO, TenantChannelPO tenantChannelPO,
                                                  S message, TypeReference<T> targetType) {
        Long tenantId = tenantSubscribePO.getTenantId();
        String messageTopic = tenantSubscribePO.getMessageTopic();
        Pair<Method, Object> pair = grpcDiscoveryConfig.getMethod(tenantId, MessageTopicEnum.fromMessageTopic(messageTopic));

        Object bean = pair.getRight();

        try {
            Object requestBody = convert(tenantChannelPO.getRequestBodyConverter(), message, new TypeReference<>() {});
            Object response = pair.getLeft().invoke(bean, requestBody);

            if (response == null) {
                throw new TenantMessageException(ProduceResultEnum.FAIL.getMessage());
            }

            if (targetType.getType().equals(Void.class)) {
                return null;
            }

            return convert(tenantChannelPO.getResponseBodyConverter(), response, targetType);
        } catch (Exception e) {
            log.error("tenantId: {} messageTopic: {} 消息体: {}", tenantId, messageTopic, JsonUtil.toJsonString(message), e);
            if (e instanceof TenantMessageException) {
                throw e;
            }
            throw new TenantMessageException(e);
        }
    }

}
