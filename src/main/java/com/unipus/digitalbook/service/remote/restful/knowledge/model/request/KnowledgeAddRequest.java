package com.unipus.digitalbook.service.remote.restful.knowledge.model.request;

import com.unipus.digitalbook.service.remote.restful.knowledge.model.common.KnowledgeGraphInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月28日 14:51
 */
public class KnowledgeAddRequest {

    private String courseIdStr;
    private String name;
    private String description;
    private String background;
    private String userId;
    private String userName;
    private List<KnowledgeGraphInfo> graphList;


    // Getters and Setters
    public String getCourseIdStr() {
        return courseIdStr;
    }

    public void setCourseIdStr(String courseIdStr) {
        this.courseIdStr = courseIdStr;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBackground() {
        return background;
    }

    public void setBackground(String background) {
        this.background = background;
    }

    public List<KnowledgeGraphInfo> getGraphList() {
        return graphList;
    }

    public void setGraphList(List<KnowledgeGraphInfo> graphList) {
        this.graphList = graphList;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
