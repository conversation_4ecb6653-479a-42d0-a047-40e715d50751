package com.unipus.digitalbook.service.remote.restful.soe;

import com.alibaba.fastjson2.JSONObject;
import com.unipus.digitalbook.common.exception.soe.SoeException;
import com.unipus.digitalbook.common.utils.AESUtil;
import com.unipus.digitalbook.common.utils.ClioSigUtil;
import com.unipus.digitalbook.model.entity.clio.ClioSig;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SOE接口服务
 *
 * <AUTHOR>
 * @date 2025/3/25 14:13
 */
@Slf4j
@Service
public class SoeService {

    @Value("${remote.soe.appKey}")
    public String appKey;

    @Value("${remote.soe.appSecret}")
    private String appSecret;

    @Resource
    private SoeApiService soeApiService;

    /**
     * 生成认证字符串
     * <p>
     * 生成算法为：base64(appKey:ts前7位+MD5(appKey:appSecret:ts后10位)+ts后6位)
     *
     * @return 认证字符串
     */
    public String getAuth() {
        if (appKey == null || appSecret == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        // 获取当前时间戳（毫秒级）
        long timestampMillis = System.currentTimeMillis();
        String ts = String.format("%013d", timestampMillis);
        // 截取时间戳
        String tsFirst7 = ts.substring(0, 7);
        String tsLast10 = ts.substring(ts.length() - 10);
        String tsLast6 = ts.substring(ts.length() - 6);
        // 拼接最终字符串
        String authStr = appKey + ":" + tsFirst7 + DigestUtils.md5Hex(appKey + ":" + appSecret + ":" + tsLast10) + tsLast6;
        // Base64编码
        return Base64.encodeBase64String(authStr.getBytes());
    }

    /**
     * soe上报日志
     *
     * @param result
     */
    public void syncLog(String result) {
        // todo 需要加重试机制
        Map<String, Object> params = new HashMap<>();
        params.put("status", 4);
        params.put("result", result);
        ResponseEntity<JSONObject> responseEntity = soeApiService.log(getAuth(), params);
        if (!HttpStatus.OK.equals(responseEntity.getStatusCode())) {
            log.error("SOE日志上报接口调用失败，响应状态码：{}", responseEntity.getStatusCode());
        }
        JSONObject body = responseEntity.getBody();
        log.info("SOE日志上报结果：{}", body);
        if (body == null || body.getIntValue("code") != 0) {
            log.error("SOE日志上报失败，响应结果：{}", body);
        }
    }

    /**
     * soe初始化
     *
     * @return
     */
    public List<JSONObject> initialize() {
        ResponseEntity<JSONObject> responseEntity = soeApiService.initialize(getAuth(), new HashMap<>());
        if (!HttpStatus.OK.equals(responseEntity.getStatusCode())) {
            log.error("SOE初始化接口调用失败，响应状态码：{}", responseEntity.getStatusCode());
            throw new SoeException("SOE初始化接口调用失败");
        }
        JSONObject body = responseEntity.getBody();
        if (body == null || !body.containsKey("data")) {
            log.error("SOE初始化接口返回为空");
            throw new SoeException("SOE初始化接口返回为空");
        }
        List<JSONObject> dataList = body.getList("data", JSONObject.class);
        if (dataList == null || dataList.isEmpty()) {
            log.error("SOE初始化接口配置为空");
            throw new SoeException("SOE初始化接口配置为空");
        }
        return dataList;
    }

    /**
     * soe获取令牌
     *
     * @param params
     * @return
     */
    public JSONObject acquire(Map<String, Object> params) {
        ResponseEntity<JSONObject> acquireResponseEntity = soeApiService.acquire(getAuth(), params);
        if (!HttpStatus.OK.equals(acquireResponseEntity.getStatusCode())) {
            log.error("SOE获取令牌接口调用失败，响应状态码：{}", acquireResponseEntity.getStatusCode());
            throw new SoeException("SOE获取令牌接口调用失败");
        }
        JSONObject acquireBody = acquireResponseEntity.getBody();
        log.info("SOE获取令牌接口结果：{}", acquireBody);
        if (acquireBody == null || !acquireBody.containsKey("data")) {
            throw new SoeException("SOE获取令牌接口返回为空");
        }
        if (acquireBody.getIntValue("code") != 0) {
            throw new SoeException("SOE获取令牌接口请求失败");
        }
        JSONObject acquireObj = acquireBody.getJSONObject("data");
        if (acquireObj == null || !acquireObj.containsKey("token")) {
            throw new SoeException("SOE获取令牌接口token为空");
        }
        return acquireObj;
    }

    /**
     * soe释放令牌
     *
     * @param params
     */
    public void release(Map<String, Object> params) {
        ResponseEntity<JSONObject> releaseResponseEntity = soeApiService.release(getAuth(), params);
        if (!HttpStatus.OK.equals(releaseResponseEntity.getStatusCode())) {
            log.error("SOE释放令牌接口调用失败，响应状态码：{}", releaseResponseEntity.getStatusCode());
        }
        JSONObject releaseBody = releaseResponseEntity.getBody();
        log.info("SOE释放令牌接口结果：{}", releaseBody);
        if (releaseBody == null || releaseBody.getIntValue("code") != 0) {
            log.error("SOE释放令牌失败，响应结果：{}", releaseBody);
        }
    }

    public String getSoeClioConfig() {
        List<JSONObject> initializeObjList = initialize();
        JSONObject data = initializeObjList.stream()
                .filter(obj -> "3".equals(obj.getString("engine")))
                .findFirst().orElse(null);
        if (data == null || !data.containsKey("config")) {
            log.error("SOE初始化接口clio配置为空");
            throw new SoeException("SOE初始化接口clio配置为空");
        }
        return data.getString("config");
    }

    public ClioSig getClioSig(String tokenId, String openId, String soeQuestionType) {
        Map<String, Object> params = new HashMap<>();
        params.put("quesType", soeQuestionType);
        params.put("userId", openId);
        params.put("ssoId", openId);
        params.put("sceneId", 5);
        JSONObject config = acquire(params);
        // 获取clio配置
        String soeConfig = config.getString("config");
        ClioSig clioSig = getClioSig(soeConfig);
        clioSig.setTokenId(tokenId);
        // 获取限流令牌
        String soeLimit = config.getString("token");
        clioSig.setSoeLimit(soeLimit);
        return clioSig;
    }

    /**
     * 解密获取soe的clio配置
     *
     * @param soeConfig clio配置
     * @return clio配置
     */
    public ClioSig getClioSig(String soeConfig) {
        String decrypted = AESUtil.decryptByAES(appSecret, soeConfig);
        String[] config = decrypted.split(":");
        String engineKey = config[0];
        String engineSecret = config[1];
        // appJson
        ClioSig clioSig = new ClioSig();
        clioSig.setApplicationId(engineKey);
        Long timestamp = System.currentTimeMillis() / 1000;
        clioSig.setTimestamp(timestamp);
        String sig = ClioSigUtil.getClioSig(engineKey, engineSecret, timestamp);
        clioSig.setSig(sig);
        clioSig.setUserId(ClioSigUtil.USER_ID);
        return clioSig;
    }

    public void syncClioLog(String soeLimit, String openId, String result) {
        if (StringUtils.hasText(soeLimit) && StringUtils.hasText(openId)) {
            Map<String, Object> releaseParams = new HashMap<>();
            releaseParams.put("token", soeLimit);
            releaseParams.put("userId", openId);
            release(releaseParams);
        }
        if (StringUtils.hasText(result)) {
            syncLog(result);
        }
    }
}
