package com.unipus.digitalbook.service.remote.restful.sso;

import com.unipus.digitalbook.service.remote.restful.sso.response.SsoUserResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;

/**
 * @Author: 中文输入法发挥不稳定的刘川
 * @Date: 2024/10/18 上午9:57
 */
@HttpExchange("/")
public interface SsoService {

    @GetExchange("sso/serviceValidate")
    String validateServiceTicket(@RequestParam("service") String service, @RequestParam("ticket") String ticket);

    @GetExchange("sso/0.1/sso/get_user_userid")
    ResponseEntity<SsoUserResponse> getUserByOpenId(@RequestParam("userid") String userid);
}
