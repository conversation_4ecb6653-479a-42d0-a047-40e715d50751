package com.unipus.digitalbook.service.remote.restful.ucontent;

import com.unipus.digitalbook.common.utils.JsonUtil;
import org.springframework.util.StringUtils;

public class PushResponseData {
    /**
     * 1:通过 0:未通过
     */
    private int state;

    private String message;

    private String strategy;


    private StrategyNode strategyNode;

    public boolean isPass() {
        return state == 1;
    }
    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getStrategy() {
        return strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public StrategyNode getStrategyNode() {
        if (!StringUtils.hasText(strategy)) {
            return null;
        }
        return JsonUtil.parseObject(strategy, StrategyNode.class);
    }


    public static class StrategyNode {
        private long endTime;
        private long startTime;
        private boolean required;

        private double task_mini_score_pct;

        public long getEndTime() {
            return endTime;
        }

        public void setEndTime(long endTime) {
            this.endTime = endTime;
        }

        public long getStartTime() {
            return startTime;
        }

        public void setStartTime(long startTime) {
            this.startTime = startTime;
        }

        public boolean isRequired() {
            return required;
        }

        public void setRequired(boolean required) {
            this.required = required;
        }

        public double getTask_mini_score_pct() {
            return task_mini_score_pct;
        }

        public void setTask_mini_score_pct(double task_mini_score_pct) {
            this.task_mini_score_pct = task_mini_score_pct;
        }
    }
}
