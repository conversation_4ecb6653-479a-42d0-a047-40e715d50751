package com.unipus.digitalbook.service.remote.restful.ucontent;

import com.unipus.digitalbook.model.entity.question.UserAnswerNodeData;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

@HttpExchange("/api")
public interface UcontentApiService {
    /**
     * @see <a href="https://es3eflgtuw.feishu.cn/wiki/I905wykSWiumGLkXkk7c1T5DnTf#KHacdMcwSo1hlhx78x9ccQFTnQb">提交用户答案</a>
     * @param node
     * @return
     */
    @PostExchange("/v1/ipublish/submit")
    BaseResponse<PushResponseData> submit(@RequestBody UserAnswerNodeData node);
}
