package com.unipus.digitalbook.service.remote.restful.ucontent;

import com.unipus.digitalbook.service.remote.restful.BaseApiConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;

@Configuration
public class UcontentClientConfig extends BaseApiConfig {
    @Value("${remote.ucontent.host}")
    private String host;

    @Bean
    public UcontentApiService ucontentApiService(WebClient.Builder webClientBuilder) {
        // 创建 WebClient
        WebClient webClient = webClientBuilder
                .baseUrl(host)
                .build();
        // 创建 WebClientAdapter
        WebClientAdapter adapter = createWebClientAdapter(webClient);
        // 创建 HTTP 代理工厂并返回 UaiClientConfig 实例
        return createHttpServiceProxyFactory(adapter).createClient(UcontentApiService.class);
    }
}
