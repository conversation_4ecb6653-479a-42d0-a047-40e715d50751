package com.unipus.digitalbook.service.useraction.nodestrategy;

import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;

import java.util.Set;

public interface CompletionStrategy {
    /**
     * 获取节点类型
     * @return
     */
    Set<String> getTypes();
    /**
     * 判断节点是否完成
     *
     * @param node
     * @param userAction
     * @return
     */
    boolean isCompleted(ChapterNode node, UserAction userAction);

}
