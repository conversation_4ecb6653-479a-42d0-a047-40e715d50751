package com.unipus.digitalbook.service.useraction.nodestrategy;

import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Set;

@Slf4j
@Component
public class ImageNodeStrategy implements CompletionStrategy{
    @Override
    public Set<String> getTypes() {
        return Set.of("insert-image");
    }

    @Override
    public boolean isCompleted(ChapterNode node, UserAction userAction) {
        return false;
    }
}
