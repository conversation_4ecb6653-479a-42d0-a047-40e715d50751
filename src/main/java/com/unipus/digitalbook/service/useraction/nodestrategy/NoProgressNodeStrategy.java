package com.unipus.digitalbook.service.useraction.nodestrategy;

import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class NoProgressNodeStrategy implements CompletionStrategy  {
    @Override
    public Set<String> getTypes() {
        return Set.of();
    }

    @Override
    public boolean isCompleted(ChapterNode node, UserAction userAction) {
        return false;
    }
}
