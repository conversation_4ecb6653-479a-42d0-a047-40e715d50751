package com.unipus.digitalbook.service.useraction.nodestrategy;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class NodeCompletionStrategyFactory {
    private final Map<String, CompletionStrategy> STRATEGY_MAP = new HashMap<>();

    @Resource
    private NoProgressNodeStrategy  noProgressNodeStrategy;
    @Autowired
    public NodeCompletionStrategyFactory(List<CompletionStrategy> strategies) {
        for (CompletionStrategy strategy : strategies) {
            strategy.getTypes().forEach(type -> {
                STRATEGY_MAP.put(type, strategy);
            });
        }
    }

    public CompletionStrategy getStrategy(String type) {
        CompletionStrategy strategy = STRATEGY_MAP.get(type.toLowerCase());
        if (strategy == null) {
            log.debug("该节点类型不处理：{}", type);
            return noProgressNodeStrategy;
        }
        return strategy;
    }
}
