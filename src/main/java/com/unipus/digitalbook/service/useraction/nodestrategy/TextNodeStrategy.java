package com.unipus.digitalbook.service.useraction.nodestrategy;

import com.unipus.digitalbook.model.entity.action.UserAction;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Set;

@Slf4j
@Component
public class TextNodeStrategy implements CompletionStrategy{
    @Override
    public Set<String> getTypes() {
        return Set.of("base-paragraph", "list", "quote", "collapsible-block", "highlight-block");
    }

    @Override
    public boolean isCompleted(ChapterNode node, UserAction userAction) {
        double required = 0;
        if (node.getCjkWordCount() != null) {
            // 600个字符=1分钟
            required += node.getCjkWordCount() / 600.0 * 60 * 1000;
        }
        if (node.getNonCjkWordCount() != null) {
            // 100个单词=1分钟
            required += node.getNonCjkWordCount() / 100.0 * 60 * 1000;
        }
        Long duration = userAction.getDuration();
        if (duration == null || duration <= 0) return false;
        return duration >= required;
    }
}
