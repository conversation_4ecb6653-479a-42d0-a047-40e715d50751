spring:
  datasource:
    url: ******************************************************************************************
    username: ipublish_user
    password: vuZGkiPgPDVGUdS4
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: true
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin
        allow:
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 500
    # kafka配置
  kafka:
    bootstrap-servers: ************:9092,************:9092,***********:9092
    consumer:
      group-id: test-ipublish-group
      auto-offset-reset: earliest
# kafka topic配置
kafka:
  consumer:
    enable: false
  topic:
    operationLog: test-ipublish-bookOperationLog
    bookOperationLog: test-ipublish-operation-book
    chapterOperationLog: test-ipublish-operation-chapter
    clioResult: dev_clio_result_2_ipublish
    bookPublishTopic: test-ipublish-bookPublished
    userAction: test-ipublish-userAction
    uaiResourcePublish: test_resource_publish
app:
  env: test

redisson:
  nodes:
    - redis-6d67dc5e-555f-42ea-b0e8-07d0ab1dafaf.cn-north-4.dcs.myhuaweicloud.com:6379
  max-redirects: 3
  connection-timeout: 1000
  read-timeout: 1000
  scan-interval: 30

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    path: /doc/swagger-ui.html

remote:
  sso:
    url: https://utsso.unipus.cn
    serviceName: https://ipublish-test.unipus.cn
  aigc:
    url: https://llm-api.unipus.cn
    sk: sk-dp4e1ZnsS8uWKDM712289cE764C14f5aB794C2Cf2596B8Aa
  qrcode:
    url: https://testucontent-cms.unipus.cn
  soe:
    url: https://soetest.unipus.cn
    appKey: ipublish
    appSecret: EiBBCfPzEMHvLQnN
  engine:
    appKey: iPublish
    appSecret: UmGZLkXti2fg
    host: https://engine-huawei-test.unipus.cn
  translate:
    appKey: iPublish
    appSecret: uZxZq5HaKmUiji04207yZmff30z9vg8A
  ucontent:
    host: http://ucontent-api-v3.ucontent-test:80
  knowledge:
    host: http://************:30447/mindmap
    resourceLinkUrl: https://ucloud-test-hw.unipus.cn/api/tla/courseStudy/knowledge/link

grpc:
  server:
    question:
      host: mid-qs-api-test.api1024.cn
      port: 30080

logging:
  level:
    com.unipus: DEBUG

jwt:
  secret: QW4gZXJyb3Igb2NjdXJyZWQgd2hpbGUgYXR0ZW1wdGluZyB0byBlbmNvZGUgdGhlIEp3dDogRmFpbGVkIHRvIHNlbGVjdCBhIEpXSw==
  # token过期时间（天）
  expiration: 30
  # 协作token密钥
  coSecret: 5LiN5piv5ZCn5LiN5piv5ZCn77yM6L+Z5LmI54K55bel6LWE6L+Y5oOz5bmy5ZWl5ZGi
  # 协作token过期时间（天）
  coExpiration: 30

white-list:
  # 登录校验白名单
  security-urls:
    - /auth/validate
    - /auth/accessToken
    - /doc/swagger-ui/index.html
    - /doc/swagger-ui/*
    - /v3/api-docs/*
    - /v3/api-docs
    - /doc/convert-word-to-html
    - /engine/writeCorrectCallback
  # 权限校验白名单
  permission-urls:
    - /reader/**
    - /cos/getCredential
  # 内网调用白名单
  internal-urls:
    - /backend/**
  # 内网IP网段
  internal-ip-ranges:
    - **********/16
    - *************/32
    - ***********/32
    - **************/32

#测试环境腾讯云cos配置
tencent:
  cos:
    secret-id: AKID1vd9XkCdrtJZeaLwKftujRzNekjW9aQ8
    secret-key: d5NUO5av53rTs43qRq47yb1EH9qLekcH
    bucket: ipublish-test-1313083974
    region: ap-beijing
