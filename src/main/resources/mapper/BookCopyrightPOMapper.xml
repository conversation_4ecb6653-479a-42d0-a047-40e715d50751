<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookCopyrightPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.book.BookCopyrightPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="bookId" column="book_id" jdbcType="CHAR"/>
            <result property="versionNumber" column="version_number" jdbcType="CHAR"/>
            <result property="chiefEditor" column="chief_editor" jdbcType="VARCHAR"/>
            <result property="editor" column="editor" jdbcType="VARCHAR"/>
            <result property="deputyEditor" column="deputy_editor" jdbcType="VARCHAR"/>
            <result property="projectPlanner" column="project_planner" jdbcType="VARCHAR"/>
            <result property="executiveEditor" column="executive_editor" jdbcType="VARCHAR"/>
            <result property="proofreader" column="proofreader" jdbcType="VARCHAR"/>
            <result property="digitalEditor" column="digital_editor" jdbcType="VARCHAR"/>
            <result property="coverDesigner" column="cover_designer" jdbcType="VARCHAR"/>
            <result property="layoutDesigner" column="layout_designer" jdbcType="VARCHAR"/>
            <result property="publisher" column="publisher" jdbcType="VARCHAR"/>
            <result property="wordCount" column="word_count" jdbcType="VARCHAR"/>
            <result property="publishYear" column="publish_year" jdbcType="SMALLINT"/>
            <result property="publishMonth" column="publish_month" jdbcType="TINYINT"/>
            <result property="publishDay" column="publish_day" jdbcType="TINYINT"/>
            <result property="isbn" column="isbn" jdbcType="VARCHAR"/>
            <result property="price" column="price" jdbcType="DECIMAL"/>
            <result property="edition" column="edition" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,book_id,version_number,
        chief_editor,editor,deputy_editor,
        project_planner,executive_editor,proofreader,
        digital_editor,cover_designer,layout_designer,
        publisher,word_count,publish_year,
        publish_month,publish_day,isbn,
        price,edition,create_time,
        update_time,create_by,update_by,
        enable
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from book_copyright
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="selectByBookIdAndVersion" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from book_copyright
        where  book_id = #{bookId,jdbcType=BIGINT} and version_number = #{versionNumber,jdbcType=BIGINT} and enable = true
    </select>
    <select id="selectByBookVersion" resultMap="BaseResultMap">
        select
            bc.id as id,
            bc.book_id as book_id,
            bc.version_number as version_number,
            bc.chief_editor as chief_editor,
            bc.editor as editor,
            bc.deputy_editor as deputy_editor,
            bc.project_planner as project_planner,
            bc.executive_editor as executive_editor,
            bc.proofreader as proofreader,
            bc.digital_editor as digital_editor,
            bc.cover_designer as cover_designer,
            bc.layout_designer as layout_designer,
            bc.publisher as publisher,
            bc.word_count as word_count,
            bc.publish_year as publish_year,
            bc.publish_month as publish_month,
            bc.publish_day as publish_day,
            bc.isbn as isbn,
            bc.price as price,
            bc.edition as edition,
            bc.create_time as create_time,
            bc.update_time as update_time,
            bc.create_by as create_by,
            bc.update_by as update_by,
            bc.enable as enable
        from book_copyright bc
        left join book_version_info_version_relation bv on bc.id = bv.info_id
        where bv.book_version_id = #{bookVersionId}
          and bv.content_type = 3
          and bc.enable = true
          and bv.enable = true
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from book_copyright
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.book.BookCopyrightPO" useGeneratedKeys="true">
        insert into book_copyright
        ( id,book_id,version_number
        ,chief_editor,editor,deputy_editor
        ,project_planner,executive_editor,proofreader
        ,digital_editor,cover_designer,layout_designer
        ,publisher,word_count,publish_year
        ,publish_month,publish_day,isbn
        ,price,edition,create_time
        ,update_time,create_by,update_by
        ,enable)
        values (#{id,jdbcType=BIGINT},#{bookId,jdbcType=CHAR},#{versionNumber,jdbcType=CHAR}
        ,#{chiefEditor,jdbcType=VARCHAR},#{editor,jdbcType=VARCHAR},#{deputyEditor,jdbcType=VARCHAR}
        ,#{projectPlanner,jdbcType=VARCHAR},#{executiveEditor,jdbcType=VARCHAR},#{proofreader,jdbcType=VARCHAR}
        ,#{digitalEditor,jdbcType=VARCHAR},#{coverDesigner,jdbcType=VARCHAR},#{layoutDesigner,jdbcType=VARCHAR}
        ,#{publisher,jdbcType=VARCHAR},#{wordCount,jdbcType=VARCHAR},#{publishYear,jdbcType=SMALLINT}
        ,#{publishMonth,jdbcType=TINYINT},#{publishDay,jdbcType=TINYINT},#{isbn,jdbcType=VARCHAR}
        ,#{price,jdbcType=DECIMAL},#{edition,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP}
        ,#{updateTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=BIGINT},#{updateBy,jdbcType=BIGINT}
        ,#{enable,jdbcType=BIT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.book.BookCopyrightPO" useGeneratedKeys="true">
        insert into book_copyright
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="bookId != null">book_id,</if>
                <if test="versionNumber != null">version_number,</if>
                <if test="chiefEditor != null">chief_editor,</if>
                <if test="editor != null">editor,</if>
                <if test="deputyEditor != null">deputy_editor,</if>
                <if test="projectPlanner != null">project_planner,</if>
                <if test="executiveEditor != null">executive_editor,</if>
                <if test="proofreader != null">proofreader,</if>
                <if test="digitalEditor != null">digital_editor,</if>
                <if test="coverDesigner != null">cover_designer,</if>
                <if test="layoutDesigner != null">layout_designer,</if>
                <if test="publisher != null">publisher,</if>
                <if test="wordCount != null">word_count,</if>
                <if test="publishYear != null">publish_year,</if>
                <if test="publishMonth != null">publish_month,</if>
                <if test="publishDay != null">publish_day,</if>
                <if test="isbn != null">isbn,</if>
                <if test="price != null">price,</if>
                <if test="edition != null">edition,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="bookId != null">#{bookId,jdbcType=CHAR},</if>
                <if test="versionNumber != null">#{versionNumber,jdbcType=CHAR},</if>
                <if test="chiefEditor != null">#{chiefEditor,jdbcType=VARCHAR},</if>
                <if test="editor != null">#{editor,jdbcType=VARCHAR},</if>
                <if test="deputyEditor != null">#{deputyEditor,jdbcType=VARCHAR},</if>
                <if test="projectPlanner != null">#{projectPlanner,jdbcType=VARCHAR},</if>
                <if test="executiveEditor != null">#{executiveEditor,jdbcType=VARCHAR},</if>
                <if test="proofreader != null">#{proofreader,jdbcType=VARCHAR},</if>
                <if test="digitalEditor != null">#{digitalEditor,jdbcType=VARCHAR},</if>
                <if test="coverDesigner != null">#{coverDesigner,jdbcType=VARCHAR},</if>
                <if test="layoutDesigner != null">#{layoutDesigner,jdbcType=VARCHAR},</if>
                <if test="publisher != null">#{publisher,jdbcType=VARCHAR},</if>
                <if test="wordCount != null">#{wordCount,jdbcType=VARCHAR},</if>
                <if test="publishYear != null">#{publishYear,jdbcType=SMALLINT},</if>
                <if test="publishMonth != null">#{publishMonth,jdbcType=TINYINT},</if>
                <if test="publishDay != null">#{publishDay,jdbcType=TINYINT},</if>
                <if test="isbn != null">#{isbn,jdbcType=VARCHAR},</if>
                <if test="price != null">#{price,jdbcType=DECIMAL},</if>
                <if test="edition != null">#{edition,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
                <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.book.BookCopyrightPO">
        update book_copyright
        <set>
                <if test="bookId != null">
                    book_id = #{bookId,jdbcType=CHAR},
                </if>
                <if test="versionNumber != null">
                    version_number = #{versionNumber,jdbcType=CHAR},
                </if>
                <if test="chiefEditor != null">
                    chief_editor = #{chiefEditor,jdbcType=VARCHAR},
                </if>
                <if test="editor != null">
                    editor = #{editor,jdbcType=VARCHAR},
                </if>
                <if test="deputyEditor != null">
                    deputy_editor = #{deputyEditor,jdbcType=VARCHAR},
                </if>
                <if test="projectPlanner != null">
                    project_planner = #{projectPlanner,jdbcType=VARCHAR},
                </if>
                <if test="executiveEditor != null">
                    executive_editor = #{executiveEditor,jdbcType=VARCHAR},
                </if>
                <if test="proofreader != null">
                    proofreader = #{proofreader,jdbcType=VARCHAR},
                </if>
                <if test="digitalEditor != null">
                    digital_editor = #{digitalEditor,jdbcType=VARCHAR},
                </if>
                <if test="coverDesigner != null">
                    cover_designer = #{coverDesigner,jdbcType=VARCHAR},
                </if>
                <if test="layoutDesigner != null">
                    layout_designer = #{layoutDesigner,jdbcType=VARCHAR},
                </if>
                <if test="publisher != null">
                    publisher = #{publisher,jdbcType=VARCHAR},
                </if>
                <if test="wordCount != null">
                    word_count = #{wordCount,jdbcType=VARCHAR},
                </if>
                <if test="publishYear != null">
                    publish_year = #{publishYear,jdbcType=SMALLINT},
                </if>
                <if test="publishMonth != null">
                    publish_month = #{publishMonth,jdbcType=TINYINT},
                </if>
                <if test="publishDay != null">
                    publish_day = #{publishDay,jdbcType=TINYINT},
                </if>
                <if test="isbn != null">
                    isbn = #{isbn,jdbcType=VARCHAR},
                </if>
                <if test="price != null">
                    price = #{price,jdbcType=DECIMAL},
                </if>
                <if test="edition != null">
                    edition = #{edition,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=BIT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.book.BookCopyrightPO">
        update book_copyright
        set 
            book_id =  #{bookId,jdbcType=CHAR},
            version_number =  #{versionNumber,jdbcType=CHAR},
            chief_editor =  #{chiefEditor,jdbcType=VARCHAR},
            editor =  #{editor,jdbcType=VARCHAR},
            deputy_editor =  #{deputyEditor,jdbcType=VARCHAR},
            project_planner =  #{projectPlanner,jdbcType=VARCHAR},
            executive_editor =  #{executiveEditor,jdbcType=VARCHAR},
            proofreader =  #{proofreader,jdbcType=VARCHAR},
            digital_editor =  #{digitalEditor,jdbcType=VARCHAR},
            cover_designer =  #{coverDesigner,jdbcType=VARCHAR},
            layout_designer =  #{layoutDesigner,jdbcType=VARCHAR},
            publisher =  #{publisher,jdbcType=VARCHAR},
            word_count =  #{wordCount,jdbcType=VARCHAR},
            publish_year =  #{publishYear,jdbcType=SMALLINT},
            publish_month =  #{publishMonth,jdbcType=TINYINT},
            publish_day =  #{publishDay,jdbcType=TINYINT},
            isbn =  #{isbn,jdbcType=VARCHAR},
            price =  #{price,jdbcType=DECIMAL},
            edition =  #{edition,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            create_by =  #{createBy,jdbcType=BIGINT},
            update_by =  #{updateBy,jdbcType=BIGINT},
            enable =  #{enable,jdbcType=BIT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <select id="selectListByBookIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM book_copyright
        WHERE version_number = '0' AND enable = true AND book_id IN
        <foreach collection="bookIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

</mapper>
