<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookVersionPOMapper">
    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.publish.BookVersionPO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="book_id" jdbcType="CHAR" property="bookId" />
        <result column="version_num" jdbcType="VARCHAR" property="versionNum" />
        <result column="show_version_number" jdbcType="VARCHAR" property="showVersionNumber" />
        <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
        <result column="publish_status" jdbcType="INTEGER" property="publishStatus" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="enable" jdbcType="BIT" property="enable" />
    </resultMap>

    <sql id="Base_Column_List">
        id, book_id, version_num, show_version_number, sort_order, publish_status, create_time, update_time, create_by, update_by, enable
    </sql>

    <!-- 根据 ID 查询教材版本信息 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM book_version WHERE id = #{id}
    </select>

    <!-- 查询所有教材版本信息 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT * FROM book_version
    </select>
    <select id="selectLatestPublishedBookVersionByBookId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM book_version
        WHERE book_id = #{bookId, jdbcType=CHAR}
        AND enable = 1
        -- and publish_status = 1
        ORDER BY version_num DESC
        LIMIT 1
    </select>

    <select id="selectByBookIdAndVersionNum" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from book_version
        where enable = true
        and book_id = #{bookId, jdbcType=CHAR}
        and version_num = #{versionNum, jdbcType=VARCHAR}
        LIMIT 1
    </select>

    <!-- 插入教材版本信息 -->
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.publish.BookVersionPO" useGeneratedKeys="true">
        insert into book_version
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="bookId != null and bookId != ''">
                book_id,
            </if>
            <if test="versionNum != null and versionNum != ''">
                version_num,
            </if>
            <if test="showVersionNumber != null and showVersionNumber != ''">
                show_version_number,
            </if>
            <if test="sortOrder != null">
                sort_order,
            </if>
            <if test="publishStatus != null">
                publish_status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="enable != null">
                `enable`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="bookId != null and bookId != ''">
                #{bookId,jdbcType=CHAR},
            </if>
            <if test="versionNum != null and versionNum != ''">
                #{versionNum,jdbcType=VARCHAR},
            </if>
            <if test="showVersionNumber != null and showVersionNumber != ''">
                #{showVersionNumber,jdbcType=VARCHAR},
            </if>
            <if test="sortOrder != null">
                #{sortOrder,jdbcType=INTEGER},
            </if>
            <if test="publishStatus != null">
                #{publishStatus,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="enable != null">
                #{enable,jdbcType=BIT},
            </if>
        </trim>
    </insert>

    <!-- 根据 ID 更新教材版本信息 -->
    <update id="updateById" parameterType="com.unipus.digitalbook.model.po.publish.BookVersionPO">
        update book_version
        <set>
            <if test="bookId != null and bookId != ''">
                book_id = #{bookId,jdbcType=CHAR},
            </if>
            <if test="versionNum != null and versionNum != ''">
                version_num = #{versionNum,jdbcType=VARCHAR},
            </if>
            <if test="showVersionNumber != null and showVersionNumber != ''">
                show_version_number = #{showVersionNumber,jdbcType=VARCHAR},
            </if>
            <if test="sortOrder != null">
                sort_order = #{sortOrder,jdbcType=INTEGER},
            </if>
            <if test="publishStatus != null">
                publish_status = #{publishStatus,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="enable != null">
                `enable` = #{enable,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据 ID 删除教材版本信息 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM book_version WHERE id = #{id}
    </delete>

    <select id="selectBookPublishedVersionByBookId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM book_version
        WHERE book_id = #{bookId}
        AND enable = 1
        ORDER BY create_time DESC
    </select>

    <select id="selectBookPublishedVersionCountByBookId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM book_version WHERE book_id = #{bookId} AND enable = 1
    </select>

    <resultMap id="BookPublishedResultMap" type="java.util.HashMap">
        <result property="bookId" column="book_id" javaType="java.lang.String"/>
        <result property="isPublished" column="is_published" javaType="java.lang.Boolean"/>
    </resultMap>

    <resultMap id="BookPublishedTimeMap" type="java.util.HashMap">
        <result property="bookId" column="book_id" javaType="java.lang.String"/>
        <result property="maxCreateTime" column="max_create_time" javaType="java.lang.String"/>
    </resultMap>

    <select id="selectIsBookPublishedVersionByBookId" resultMap="BookPublishedResultMap">
        SELECT
        ab.book_id,
        MAX(CASE WHEN bv.enable = 1 THEN 1 ELSE 0 END) AS is_published
        FROM
        (
        <foreach collection="bookIds" item="bookId" separator=" UNION ALL ">
            SELECT #{bookId} AS book_id
        </foreach>
        ) ab
        LEFT JOIN
        book_version bv
        ON
        ab.book_id = bv.book_id
        GROUP BY
        ab.book_id
    </select>

    <select id="selectBookMaxPublishedTimeByBookId" resultMap="BookPublishedTimeMap">
        SELECT
        ab.book_id,
        max(create_time) as max_create_time
        FROM
        (
        <foreach collection="bookIds" item="bookId" separator=" UNION ALL ">
            SELECT #{bookId} AS book_id
        </foreach>
        ) ab
        LEFT JOIN
        book_version bv
        ON
        ab.book_id = bv.book_id
        GROUP BY
        ab.book_id
    </select>

    <select id="selectBookLatestBookVersionByBookIds" resultMap="BaseResultMap">
        select
        *
        from
        book_version
        where
        id in(
        select
        max(id) as id
        from
        (
        <foreach collection="bookIds" item="bookId" separator=" UNION ALL ">
            SELECT #{bookId} AS book_id
        </foreach>
        ) ab
        left join
        book_version bv
        on
        ab.book_id = bv.book_id
        group by
        ab.book_id
        )
    </select>

    <sql id="SearchPublishedBookVersionConditions">
        FROM book_version
        WHERE book_id = #{bookId}
        <if test="publishStartTime != null">
            AND create_time &gt;= #{publishStartTime}
        </if>
        <if test="publishEndTime != null">
            AND create_time &lt;= #{publishEndTime}
        </if>
        AND enable = 1
    </sql>

    <select id="selectBookPublishedVersion" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        <include refid="SearchPublishedBookVersionConditions"/>
        ORDER BY create_time DESC
        <if test="page != null">
            LIMIT #{page.offset}, #{page.limit}
        </if>
    </select>

    <select id="selectBookPublishedVersionCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        <include refid="SearchPublishedBookVersionConditions"/>
    </select>

    <select id="selectPreviousVersionById" resultMap="BaseResultMap">
        SELECT
        bv1.id,
        bv1.book_id,
        bv1.version_num,
        bv1.show_version_number,
        bv1.sort_order,
        bv1.publish_status,
        bv1.create_time,
        bv1.update_time,
        bv1.create_by,
        bv1.update_by,
        bv1.enable
        FROM
        book_version bv1
        INNER JOIN ( SELECT book_id, version_num FROM book_version WHERE id = #{id} ) bv2 ON bv1.book_id = bv2.book_id
        WHERE
        bv1.enable = true
        AND bv1.version_num &lt; bv2.version_num
        ORDER BY bv1.version_num DESC
        LIMIT 1
    </select>

    <select id="selectBookPublishedVersionSortOrderById" resultType="java.lang.Integer">
        SELECT
        COUNT(*) + 1 AS version_order
        FROM
        book_version bv,
        (SELECT book_id, version_num FROM book_version WHERE id = #{id}) target
        WHERE
            bv.book_id = target.book_id
        AND bv.version_num &lt; target.version_num
        AND bv.enable = 1
    </select>
    <select id="getBookVersionByChapterVersionId" resultMap="BaseResultMap">
        SELECT
            bv.id,
            bv.book_id,
            bv.version_num
        FROM book_version bv
        join book_version_chapter_version_relation bvcr on bv.id = bvcr.book_version_id
        where bvcr.chapter_version_id = #{chapterVersionId} and bv.enable = 1 limit 1
    </select>
</mapper>