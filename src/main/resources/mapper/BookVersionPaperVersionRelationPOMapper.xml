<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.BookVersionPaperVersionRelationPOMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.publish.BookVersionPaperVersionRelationPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="bookVersionId" column="book_version_id" jdbcType="BIGINT"/>
            <result property="paperVersionId" column="paper_version_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="enable" column="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,book_version_id,paper_version_id,create_time,update_time,create_by,
        update_by,enable
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from book_version_paper_version_relation
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from book_version_paper_version_relation
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.publish.BookVersionPaperVersionRelationPO" useGeneratedKeys="true">
        insert into book_version_paper_version_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="bookVersionId != null">book_version_id,</if>
                <if test="paperVersionId != null">paper_version_id,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="enable != null">enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="bookVersionId != null">#{bookVersionId,jdbcType=BIGINT},</if>
                <if test="paperVersionId != null">#{paperVersionId,jdbcType=BIGINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
                <if test="enable != null">#{enable,jdbcType=BIT},</if>
        </trim>
    </insert>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into book_version_paper_version_relation (
        book_version_id,
        paper_version_id,
        create_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.bookVersionId},
            #{item.paperVersionId},
            #{item.createBy}
            )
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.publish.BookVersionPaperVersionRelationPO">
        update book_version_paper_version_relation
        <set>
                <if test="bookVersionId != null">
                    book_version_id = #{bookVersionId,jdbcType=BIGINT},
                </if>
                <if test="paperVersionId != null">
                    paper_version_id = #{paperVersionId,jdbcType=BIGINT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="enable != null">
                    enable = #{enable,jdbcType=BIT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <select id="selectByPaperId" resultMap="BaseResultMap">
        SELECT
        pr.id,
        pr.book_version_id,
        pr.paper_version_id,
        pr.create_time,
        pr.update_time,
        pr.create_by,
        pr.update_by,
        pr.enable
        FROM
            question_group qg
            LEFT JOIN book_version_paper_version_relation pr ON qg.id = pr.paper_version_id
        WHERE
            qg.`enable` = TRUE
          AND pr.`enable` = TRUE
          AND qg.biz_group_id = #{paperId}
          AND pr.id IS NOT NULL
    </select>

    <select id="selectLastPublishedByPaperId" resultMap="BaseResultMap">
        SELECT
        pr.id,
        pr.book_version_id,
        pr.paper_version_id,
        pr.create_time,
        pr.update_time,
        pr.create_by,
        pr.update_by,
        pr.enable
        FROM
        question_group qg
        LEFT JOIN book_version_paper_version_relation pr ON qg.id = pr.paper_version_id
        WHERE
        qg.`enable` = TRUE
        AND pr.`enable` = TRUE
        AND qg.biz_group_id = #{paperId}
        AND pr.id IS NOT NULL
        ORDER BY
        qg.version_number DESC,
        pr.id DESC
        LIMIT 1
    </select>

    <select id="selectByBookVersionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from book_version_paper_version_relation
        where enable = true and book_version_id = #{bookVersionId,jdbcType=BIGINT}
    </select>

    <!-- 通过教材版本取得试卷版本 -->
    <select id="getPaperVersionByBookVersion" resultType="java.lang.String">
        SELECT
            qg.version_number
        FROM
            question_group qg
            JOIN book_version_paper_version_relation bvpvr ON bvpvr.paper_version_id = qg.id
            JOIN book_version bv ON bv.id = bvpvr.book_version_id
        WHERE
            bv.book_id = #{bookId}
            AND bv.version_num = #{bookVersionNumber}
            AND qg.biz_group_id = #{paperId}
    </select>

    <!-- 通过教材版本取得试卷版本 -->
    <select id="getPaperVersionByBookVersionId" resultType="PaperPO">
        SELECT
            pe.paper_version_id AS id,
            pe.paper_id AS paperId,
            pe.version_number AS versionNumber,
            pe.paper_name AS paperName,
            bv.book_id AS bookId
        FROM
            paper_extend pe
                JOIN book_version_paper_version_relation bvpvr ON bvpvr.paper_version_id = pe.paper_version_id
                JOIN book_version bv ON bv.id = bvpvr.book_version_id
        WHERE bv.id = #{bookVersionId}
    </select>

    <!-- 通过试卷版本取得教材版本 -->
    <select id="getBookVersionByPaperVersion" resultType="BookVersionPO">
        SELECT
            bv.book_id AS bookId,
            bv.version_num AS versionNum
        FROM
            paper_extend pe
            JOIN book_version_paper_version_relation bvpvr ON bvpvr.paper_version_id = pe.paper_version_id
            JOIN book_version bv ON bv.id = bvpvr.book_version_id
        WHERE pe.paper_id = #{paperId} AND pe.version_number = #{paperVersionNumber}
        LIMIT 1
    </select>
</mapper>
