<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.CourseKnowledgeInfoMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeInfoPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="courseIdStr" column="course_id_str" jdbcType="VARCHAR"/>
            <result property="courseId" column="course_id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="knowledgeId" column="knowledge_id" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="deleteStatus" column="delete_status" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,course_id_str,course_id,
        name,description,knowledge_id,
        create_time,update_time,create_by,
        update_by,delete_status
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from course_knowledge_info
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <select id="selectByCourseIdStr" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from course_knowledge_info
        where course_id_str=#{courseIdStr}
        and delete_status=0
    </select>

    <select id="selectBySelective" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM course_knowledge_info
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="courseIdStr != null">
                AND course_id_str = #{courseIdStr}
            </if>
            <if test="courseId != null">
                AND course_id = #{courseId}
            </if>
            <if test="name != null">
                AND name = #{name}
            </if>
            <if test="description != null">
                AND description = #{description}
            </if>
            <if test="knowledgeId != null">
                AND knowledge_id = #{knowledgeId}
            </if>
            <if test="createTime != null">
                AND create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                AND update_time = #{updateTime}
            </if>
            <if test="createBy != null">
                AND create_by = #{createBy}
            </if>
            <if test="updateBy != null">
                AND update_by = #{updateBy}
            </if>
            <if test="deleteStatus != null">
                AND delete_status = #{deleteStatus}
            </if>
        </where>
    </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from course_knowledge_info
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeInfoPO" useGeneratedKeys="true">
        insert into course_knowledge_info
        (course_id_str,course_id,name,
         description,knowledge_id,create_time,update_time,create_by
        ,update_by,delete_status
        )
        values (#{courseIdStr,jdbcType=VARCHAR},#{courseId,jdbcType=BIGINT}
        ,#{name,jdbcType=VARCHAR},#{description,jdbcType=VARCHAR},#{knowledgeId,jdbcType=VARCHAR}
        ,#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=BIGINT}
        ,#{updateBy,jdbcType=BIGINT},#{deleteStatus,jdbcType=BIT}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeInfoPO" useGeneratedKeys="true">
        insert into course_knowledge_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="courseIdStr != null">course_id_str,</if>
                <if test="courseId != null">course_id,</if>
                <if test="name != null">name,</if>
                <if test="description != null">description,</if>
                <if test="knowledgeId != null">knowledge_id,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="deleteStatus != null">delete_status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="courseIdStr != null">#{courseIdStr,jdbcType=VARCHAR},</if>
                <if test="courseId != null">#{courseId,jdbcType=BIGINT},</if>
                <if test="name != null">#{name,jdbcType=VARCHAR},</if>
                <if test="description != null">#{description,jdbcType=VARCHAR},</if>
                <if test="knowledgeId != null">#{knowledgeId,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
                <if test="deleteStatus != null">#{deleteStatus,jdbcType=BIT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeInfoPO">
        update course_knowledge_info
        <set>
                <if test="courseIdStr != null">
                    course_id_str = #{courseIdStr,jdbcType=VARCHAR},
                </if>
                <if test="courseId != null">
                    course_id = #{courseId,jdbcType=BIGINT},
                </if>
                <if test="name != null">
                    name = #{name,jdbcType=VARCHAR},
                </if>
                <if test="description != null">
                    description = #{description,jdbcType=VARCHAR},
                </if>
                <if test="knowledgeId != null">
                    knowledge_id = #{knowledgeId,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=BIGINT},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=BIGINT},
                </if>
                <if test="deleteStatus != null">
                    delete_status = #{deleteStatus,jdbcType=BIT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeInfoPO">
        update course_knowledge_info
        set 
            course_id_str =  #{courseIdStr,jdbcType=VARCHAR},
            course_id =  #{courseId,jdbcType=BIGINT},
            name =  #{name,jdbcType=VARCHAR},
            description =  #{description,jdbcType=VARCHAR},
            knowledge_id =  #{knowledgeId,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            create_by =  #{createBy,jdbcType=BIGINT},
            update_by =  #{updateBy,jdbcType=BIGINT},
            delete_status =  #{deleteStatus,jdbcType=BIT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
