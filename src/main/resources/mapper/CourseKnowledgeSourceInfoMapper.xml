<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.CourseKnowledgeSourceInfoMapper">

    <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <id property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="courseIdStr" column="course_id_str" jdbcType="VARCHAR"/>
        <result property="courseId" column="course_id" jdbcType="BIGINT"/>
        <result property="unitId" column="unit_id" jdbcType="VARCHAR"/>
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="startTime" column="start_time" jdbcType="INTEGER"/>
        <result property="startPictureUrl" column="start_picture_url" jdbcType="VARCHAR"/>
        <result property="endTime" column="end_time" jdbcType="INTEGER"/>
        <result property="courseKnowledgeId" column="course_knowledge_id" jdbcType="BIGINT"/>
        <result property="multimediaKey" column="multimedia_key" jdbcType="VARCHAR"/>
        <result property="multimediaIndex" column="multimedia_index" jdbcType="VARCHAR"/>
        <result property="multimediaName" column="multimedia_name" jdbcType="VARCHAR"/>
        <result property="dir" column="dir" jdbcType="VARCHAR"/>
        <result property="sourceUrl" column="source_url" jdbcType="VARCHAR"/>
        <result property="sourceHash" column="source_hash" jdbcType="VARCHAR"/>
        <result property="groupStatus" column="group_status" jdbcType="BIT"/>
        <result property="mainStatus" column="main_status" jdbcType="BIT"/>
        <result property="knowledgeId" column="knowledge_id" jdbcType="VARCHAR"/>
        <result property="graphId" column="graph_id" jdbcType="VARCHAR"/>
        <result property="graphNodeId" column="graph_node_id" jdbcType="VARCHAR"/>
        <result property="location" column="location" jdbcType="INTEGER"/>
        <result property="knowledgeSourceId" column="knowledge_source_id" jdbcType="VARCHAR"/>
        <result property="enableStatus" column="enable_status" jdbcType="BIT"/>
        <result property="deleteStatus" column="delete_status" jdbcType="BIT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,parent_id,course_id_str,course_id,
        unit_id,task_id,type,dir,location,
        start_time,start_picture_url,end_time,course_knowledge_id,
        multimedia_key,multimedia_index,multimedia_name,source_url,source_hash,
        group_status,main_status,knowledge_id,
        graph_id,graph_node_id,knowledge_source_id,
        enable_status,delete_status,create_time,
        update_time,create_by,update_by
    </sql>

    <select id="selectSelective" parameterType="com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM course_knowledge_source_info
        <where>
            AND delete_status = 0
            <if test="id != null">AND id = #{id}</if>
            <if test="parentId != null">AND parent_id = #{parentId}</if>
            <if test="courseIdStr != null">AND course_id_str = #{courseIdStr}</if>
            <if test="courseId != null">AND course_id = #{courseId}</if>
            <if test="unitId != null">AND unit_id = #{unitId}</if>
            <if test="taskId != null">AND task_id = #{taskId}</if>
            <if test="type != null">AND type = #{type}</if>
            <if test="dir != null">AND dir = #{dir}</if>
            <if test="startTime != null">AND start_time = #{startTime}</if>
            <if test="startPictureUrl != null">AND start_picture_url = #{startPictureUrl}</if>
            <if test="endTime != null">AND end_time = #{endTime}</if>
            <if test="courseKnowledgeId != null">AND course_knowledge_id = #{courseKnowledgeId}</if>
            <if test="multimediaKey != null">AND multimedia_key = #{multimediaKey}</if>
            <if test="sourceUrl != null">AND source_url = #{sourceUrl}</if>
            <if test="sourceHash != null">AND source_hash = #{sourceHash}</if>
            <if test="groupStatus != null">AND group_status = #{groupStatus}</if>
            <if test="mainStatus != null">AND main_status = #{mainStatus}</if>
            <if test="knowledgeId != null">AND knowledge_id = #{knowledgeId}</if>
            <if test="graphId != null">AND graph_id = #{graphId}</if>
            <if test="graphNodeId != null">AND graph_node_id = #{graphNodeId}</if>
            <if test="knowledgeSourceId != null">AND knowledge_source_id = #{knowledgeSourceId}</if>
            <if test="enableStatus != null">AND enable_status = #{enableStatus}</if>
            <if test="createBy != null">AND create_by = #{createBy}</if>
            <if test="updateBy != null">AND update_by = #{updateBy}</if>
            <if test="location != null">AND location = #{location}</if>
        </where>
        order by id
    </select>

    <select id="selectExistSourceUrls" parameterType="com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoExistPO"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM course_knowledge_source_info
        <where>
            AND delete_status = 0
            <if test="courseIdStr != null">AND course_id_str = #{courseIdStr}</if>
            <if test="courseId != null">AND course_id = #{courseId}</if>
            <if test="unitId != null">AND unit_id = #{unitId}</if>
            <if test="taskId != null">AND task_id = #{taskId}</if>
            <if test="type != null">AND type = #{type}</if>
            <if test="dir != null">AND dir = #{dir}</if>
            <if test="knowledgeId != null">AND knowledge_id = #{knowledgeId}</if>
            <if test="courseKnowledgeId != null">AND course_knowledge_id = #{courseKnowledgeId}</if>
            AND source_hash in
            <foreach collection="sourceHashUrls" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="ignoreIds != null">
                and id not in
                <foreach collection="ignoreIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>


    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from course_knowledge_source_info
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectAllSourceInfoById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from course_knowledge_source_info
        where id = #{id,jdbcType=BIGINT} or parent_id = #{id,jdbcType=BIGINT} and delete_status = 0
    </select>

    <update id="deleteAllSourceInfoByThirdIds">
        update course_knowledge_source_info
            set delete_status =1,
            update_by = #{userId,jdbcType=BIGINT}
        where knowledge_id=#{knowledgeId}
            and knowledge_source_id in
        <foreach collection="thirdIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="deleteAllSourceInfoById" parameterType="com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO">
        update course_knowledge_source_info
        set delete_status =1,
            update_time   = #{updateTime,jdbcType=TIMESTAMP},
            update_by     = #{updateBy,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
           or parent_id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateEnableStatusAllSourceInfoById" parameterType="com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO">
        update course_knowledge_source_info
        set enable_status =#{enableStatus},
            update_time   = #{updateTime,jdbcType=TIMESTAMP},
            update_by     = #{updateBy,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
           or parent_id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateDirAllSourceInfoById" parameterType="com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO">
        update course_knowledge_source_info
        set dir =#{dir},
            update_time   = #{updateTime,jdbcType=TIMESTAMP},
            update_by     = #{updateBy,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
           or parent_id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from course_knowledge_source_info
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <update id="deleteByPrimaryKeyList" parameterType="java.lang.Long">
        update course_knowledge_source_info
        set delete_status =1 where id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO"
            useGeneratedKeys="true">
        insert into course_knowledge_source_info
        ( id, parent_id, course_id_str, course_id
        , unit_id, task_id, type, dir,location
        , start_time, start_picture_url, end_time, course_knowledge_id
        , multimedia_key,multimedia_index,multimedia_name, source_url, source_hash
        , group_status, main_status, knowledge_id
        , graph_id, graph_node_id, knowledge_source_id
        , enable_status, delete_status, create_time
        , update_time, create_by, update_by)
        values ( #{id,jdbcType=BIGINT}, #{parentId,jdbcType=BIGINT}, #{courseIdStr,jdbcType=VARCHAR}
               , #{courseId,jdbcType=BIGINT}
               , #{unitId,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}
               , #{dir,jdbcType=VARCHAR}, #{location,jdbcType=INTEGER}
               , #{startTime,jdbcType=INTEGER}, #{startPictureUrl,jdbcType=VARCHAR}, #{endTime,jdbcType=INTEGER}
               , #{courseKnowledgeId,jdbcType=BIGINT}
               , #{multimediaKey,jdbcType=VARCHAR}, #{multimediaIndex,jdbcType=VARCHAR}, #{multimediaName,jdbcType=VARCHAR}
               , #{sourceUrl,jdbcType=VARCHAR}, #{sourceHash,jdbcType=VARCHAR}
               , #{groupStatus,jdbcType=BIT}, #{mainStatus,jdbcType=BIT}, #{knowledgeId,jdbcType=VARCHAR}
               , #{graphId,jdbcType=VARCHAR}, #{graphNodeId,jdbcType=VARCHAR}, #{knowledgeSourceId,jdbcType=VARCHAR}
               , #{enableStatus,jdbcType=BIT}, #{deleteStatus,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}
               , #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO" useGeneratedKeys="true">
        insert into course_knowledge_source_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="courseIdStr != null">course_id_str,</if>
            <if test="courseId != null">course_id,</if>
            <if test="unitId != null">unit_id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="type != null">type,</if>
            <if test="dir != null">dir,</if>
            <if test="location != null">location,</if>
            <if test="startTime != null">start_time,</if>
            <if test="startPictureUrl != null">start_picture_url,</if>
            <if test="endTime != null">end_time,</if>
            <if test="courseKnowledgeId != null">course_knowledge_id,</if>
            <if test="multimediaKey != null">multimedia_key,</if>
            <if test="multimediaIndex != null">multimedia_index,</if>
            <if test="multimediaName != null">multimedia_name,</if>
            <if test="sourceUrl != null">source_url,</if>
            <if test="sourceHash != null">source_hash,</if>
            <if test="groupStatus != null">group_status,</if>
            <if test="mainStatus != null">main_status,</if>
            <if test="knowledgeId != null">knowledge_id,</if>
            <if test="graphId != null">graph_id,</if>
            <if test="graphNodeId != null">graph_node_id,</if>
            <if test="knowledgeSourceId != null">knowledge_source_id,</if>
            <if test="enableStatus != null">enable_status,</if>
            <if test="deleteStatus != null">delete_status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId,jdbcType=BIGINT},</if>
            <if test="courseIdStr != null">#{courseIdStr,jdbcType=VARCHAR},</if>
            <if test="courseId != null">#{courseId,jdbcType=BIGINT},</if>
            <if test="unitId != null">#{unitId,jdbcType=VARCHAR},</if>
            <if test="taskId != null">#{taskId,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=INTEGER},</if>
            <if test="dir != null">#{dir,jdbcType=VARCHAR},</if>
            <if test="location != null">#{location,jdbcType=INTEGER},</if>
            <if test="startTime != null">#{startTime,jdbcType=INTEGER},</if>
            <if test="startPictureUrl != null">#{startPictureUrl,jdbcType=VARCHAR},</if>
            <if test="endTime != null">#{endTime,jdbcType=INTEGER},</if>
            <if test="courseKnowledgeId != null">#{courseKnowledgeId,jdbcType=BIGINT},</if>
            <if test="multimediaKey != null">#{multimediaKey,jdbcType=VARCHAR},</if>
            <if test="multimediaIndex != null">#{multimediaIndex,jdbcType=VARCHAR},</if>
            <if test="multimediaName != null">#{multimediaName,jdbcType=VARCHAR},</if>
            <if test="sourceUrl != null">#{sourceUrl,jdbcType=VARCHAR},</if>
            <if test="sourceHash != null">#{sourceHash,jdbcType=VARCHAR},</if>
            <if test="groupStatus != null">#{groupStatus,jdbcType=BIT},</if>
            <if test="mainStatus != null">#{mainStatus,jdbcType=BIT},</if>
            <if test="knowledgeId != null">#{knowledgeId,jdbcType=VARCHAR},</if>
            <if test="graphId != null">#{graphId,jdbcType=VARCHAR},</if>
            <if test="graphNodeId != null">#{graphNodeId,jdbcType=VARCHAR},</if>
            <if test="knowledgeSourceId != null">#{knowledgeSourceId,jdbcType=VARCHAR},</if>
            <if test="enableStatus != null">#{enableStatus,jdbcType=BIT},</if>
            <if test="deleteStatus != null">#{deleteStatus,jdbcType=BIT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">#{createBy,jdbcType=BIGINT},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=BIGINT},</if>
        </trim>
    </insert>

    <insert id="batchInsertSelective" parameterType="java.util.List">
        insert into course_knowledge_source_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].parentId != null">parent_id,</if>
            <if test="list[0].courseIdStr != null">course_id_str,</if>
            <if test="list[0].courseId != null">course_id,</if>
            <if test="list[0].unitId != null">unit_id,</if>
            <if test="list[0].taskId != null">task_id,</if>
            <if test="list[0].type != null">type,</if>
            <if test="list[0].dir != null">dir,</if>
            <if test="list[0].location != null">location,</if>
            <if test="list[0].startTime != null">start_time,</if>
            <if test="list[0].startPictureUrl != null">start_picture_url,</if>
            <if test="list[0].endTime != null">end_time,</if>
            <if test="list[0].courseKnowledgeId != null">course_knowledge_id,</if>
            <if test="list[0].multimediaKey != null">multimedia_key,</if>
            <if test="list[0].multimediaIndex != null">multimedia_index,</if>
            <if test="list[0].multimediaName != null">multimedia_name,</if>
            <if test="list[0].sourceUrl != null">source_url,</if>
            <if test="list[0].sourceHash != null">source_hash,</if>
            <if test="list[0].groupStatus != null">group_status,</if>
            <if test="list[0].mainStatus != null">main_status,</if>
            <if test="list[0].knowledgeId != null">knowledge_id,</if>
            <if test="list[0].graphId != null">graph_id,</if>
            <if test="list[0].graphNodeId != null">graph_node_id,</if>
            <if test="list[0].knowledgeSourceId != null">knowledge_source_id,</if>
            <if test="list[0].enableStatus != null">enable_status,</if>
            <if test="list[0].deleteStatus != null">delete_status,</if>
            <if test="list[0].createTime != null">create_time,</if>
            <if test="list[0].updateTime != null">update_time,</if>
            <if test="list[0].createBy != null">create_by,</if>
            <if test="list[0].updateBy != null">update_by,</if>
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.parentId != null">#{item.parentId,jdbcType=BIGINT},</if>
                <if test="item.courseIdStr != null">#{item.courseIdStr,jdbcType=VARCHAR},</if>
                <if test="item.courseId != null">#{item.courseId,jdbcType=BIGINT},</if>
                <if test="item.unitId != null">#{item.unitId,jdbcType=VARCHAR},</if>
                <if test="item.taskId != null">#{item.taskId,jdbcType=VARCHAR},</if>
                <if test="item.type != null">#{item.type,jdbcType=INTEGER},</if>
                <if test="item.dir != null">#{item.dir,jdbcType=VARCHAR},</if>
                <if test="item.location != null">#{item.location,jdbcType=INTEGER},</if>
                <if test="item.startTime != null">#{item.startTime,jdbcType=INTEGER},</if>
                <if test="item.startPictureUrl != null">#{item.startPictureUrl,jdbcType=VARCHAR},</if>
                <if test="item.endTime != null">#{item.endTime,jdbcType=INTEGER},</if>
                <if test="item.courseKnowledgeId != null">#{item.courseKnowledgeId,jdbcType=BIGINT},</if>
                <if test="item.multimediaKey != null">#{item.multimediaKey,jdbcType=VARCHAR},</if>
                <if test="item.multimediaIndex != null">#{item.multimediaIndex,jdbcType=VARCHAR},</if>
                <if test="item.multimediaName != null">#{item.multimediaName,jdbcType=VARCHAR},</if>
                <if test="item.sourceUrl != null">#{item.sourceUrl,jdbcType=VARCHAR},</if>
                <if test="item.sourceHash != null">#{item.sourceHash,jdbcType=VARCHAR},</if>
                <if test="item.groupStatus != null">#{item.groupStatus,jdbcType=BIT},</if>
                <if test="item.mainStatus != null">#{item.mainStatus,jdbcType=BIT},</if>
                <if test="item.knowledgeId != null">#{item.knowledgeId,jdbcType=VARCHAR},</if>
                <if test="item.graphId != null">#{item.graphId,jdbcType=VARCHAR},</if>
                <if test="item.graphNodeId != null">#{item.graphNodeId,jdbcType=VARCHAR},</if>
                <if test="item.knowledgeSourceId != null">#{item.knowledgeSourceId,jdbcType=VARCHAR},</if>
                <if test="item.enableStatus != null">#{item.enableStatus,jdbcType=BIT},</if>
                <if test="item.deleteStatus != null">#{item.deleteStatus,jdbcType=BIT},</if>
                <if test="item.createTime != null">#{item.createTime,jdbcType=TIMESTAMP},</if>
                <if test="item.updateTime != null">#{item.updateTime,jdbcType=TIMESTAMP},</if>
                <if test="item.createBy != null">#{item.createBy,jdbcType=BIGINT},</if>
                <if test="item.updateBy != null">#{item.updateBy,jdbcType=BIGINT},</if>
            </trim>
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO">
        update course_knowledge_source_info
        <set>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="courseIdStr != null">
                course_id_str = #{courseIdStr,jdbcType=VARCHAR},
            </if>
            <if test="courseId != null">
                course_id = #{courseId,jdbcType=BIGINT},
            </if>
            <if test="unitId != null">
                unit_id = #{unitId,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="dir != null">
                dir = #{dir,jdbcType=VARCHAR},
            </if>
            <if test="location != null">
                location = #{location,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=INTEGER},
            </if>
            <if test="startPictureUrl != null">
                start_picture_url = #{startPictureUrl,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=INTEGER},
            </if>
            <if test="courseKnowledgeId != null">
                course_knowledge_id = #{courseKnowledgeId,jdbcType=BIGINT},
            </if>
            <if test="multimediaKey != null">
                multimedia_key = #{multimediaKey,jdbcType=VARCHAR},
            </if>
            <if test="multimediaIndex != null">
                multimedia_index = #{multimediaIndex,jdbcType=VARCHAR},
            </if>
            <if test="multimediaName != null">
                multimedia_name = #{multimediaName,jdbcType=VARCHAR},
            </if>
            <if test="sourceUrl != null">
                source_url = #{sourceUrl,jdbcType=VARCHAR},
            </if>
            <if test="sourceHash != null">
                source_hash = #{sourceHash,jdbcType=VARCHAR},
            </if>
            <if test="groupStatus != null">
                group_status = #{groupStatus,jdbcType=BIT},
            </if>
            <if test="mainStatus != null">
                main_status = #{mainStatus,jdbcType=BIT},
            </if>
            <if test="knowledgeId != null">
                knowledge_id = #{knowledgeId,jdbcType=VARCHAR},
            </if>
            <if test="graphId != null">
                graph_id = #{graphId,jdbcType=VARCHAR},
            </if>
            <if test="graphNodeId != null">
                graph_node_id = #{graphNodeId,jdbcType=VARCHAR},
            </if>
            <if test="knowledgeSourceId != null">
                knowledge_source_id = #{knowledgeSourceId,jdbcType=VARCHAR},
            </if>
            <if test="enableStatus != null">
                enable_status = #{enableStatus,jdbcType=BIT},
            </if>
            <if test="deleteStatus != null">
                delete_status = #{deleteStatus,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
        </set>
        where   id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO">
        update course_knowledge_source_info
        set course_id_str       = #{courseIdStr,jdbcType=VARCHAR},
            parent_id           = #{parentId,jdbcType=BIGINT},
            course_id           = #{courseId,jdbcType=BIGINT},
            unit_id             = #{unitId,jdbcType=VARCHAR},
            task_id             = #{taskId,jdbcType=VARCHAR},
            dir                 = #{dir,jdbcType=VARCHAR},
            location            = #{location,jdbcType=INTEGER},
            type                = #{type,jdbcType=INTEGER},
            start_time          = #{startTime,jdbcType=INTEGER},
            start_picture_url   = #{startPictureUrl,jdbcType=VARCHAR},
            end_time            = #{endTime,jdbcType=INTEGER},
            course_knowledge_id = #{courseKnowledgeId,jdbcType=BIGINT},
            multimedia_key      = #{multimediaKey,jdbcType=VARCHAR},
            multimedia_index    = #{multimediaIndex,jdbcType=VARCHAR},
            multimedia_name     = #{multimediaName,jdbcType=VARCHAR},
            source_url          = #{sourceUrl,jdbcType=VARCHAR},
            source_hash         = #{sourceHash,jdbcType=VARCHAR},
            group_status        = #{groupStatus,jdbcType=BIT},
            main_status         = #{mainStatus,jdbcType=BIT},
            knowledge_id        = #{knowledgeId,jdbcType=VARCHAR},
            graph_id            = #{graphId,jdbcType=VARCHAR},
            graph_node_id       = #{graphNodeId,jdbcType=VARCHAR},
            knowledge_source_id = #{knowledgeSourceId,jdbcType=VARCHAR},
            enable_status       = #{enableStatus,jdbcType=BIT},
            delete_status       = #{deleteStatus,jdbcType=BIT},
            create_time         = #{createTime,jdbcType=TIMESTAMP},
            update_time         = #{updateTime,jdbcType=TIMESTAMP},
            create_by           = #{createBy,jdbcType=BIGINT},
            update_by           = #{updateBy,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
