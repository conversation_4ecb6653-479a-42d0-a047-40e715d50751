<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.CustomContentQuestionGroupRelationPOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.content.CustomContentQuestionGroupRelationPO">
    <!--@mbg.generated-->
    <!--@Table custom_content_question_group_relation-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="content_id" jdbcType="BIGINT" property="contentId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="enable" jdbcType="BIT" property="enable" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, content_id, group_id, tenant_id, create_time, update_time, create_by, update_by, 
    `enable`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from custom_content_question_group_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from custom_content_question_group_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.content.CustomContentQuestionGroupRelationPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into custom_content_question_group_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contentId != null">
        content_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contentId != null">
        #{contentId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert">
    insert into custom_content_question_group_relation (content_id, group_id, create_by, update_by)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.contentId,jdbcType=BIGINT}, #{item.groupId,jdbcType=BIGINT}, #{item.createBy,jdbcType=BIGINT}, #{item.updateBy,jdbcType=BIGINT})
    </foreach>
  </insert>
  <update id="deleteByContentId">
    update custom_content_question_group_relation
    set enable = 0, update_by = #{userId,jdbcType=BIGINT}, update_time = now()
    where content_id = #{contentId,jdbcType=BIGINT} and tenant_id = #{tenantId,jdbcType=BIGINT} and enable = 1
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.content.CustomContentQuestionGroupRelationPO">
    <!--@mbg.generated-->
    update custom_content_question_group_relation
    <set>
      <if test="contentId != null">
        content_id = #{contentId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update custom_content_question_group_relation
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="content_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.contentId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.contentId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="group_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.groupId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.groupId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="tenant_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tenantId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.tenantId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`enable` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.enable != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.enable,jdbcType=BIT}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>