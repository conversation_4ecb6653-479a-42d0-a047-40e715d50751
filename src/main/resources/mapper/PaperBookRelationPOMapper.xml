<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.unipus.digitalbook.dao.PaperBookRelationPOMapper">

    <!-- 定义结果映射 -->
    <resultMap id="PaperBookRelationResultMap" type="PaperBookRelationPO">
        <id property="id" column="id" />
        <result property="paperId" column="paper_id" />
        <result property="bookId" column="book_id" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="createBy" column="create_by" />
        <result property="updateBy" column="update_by" />
        <result property="enable" column="enable" />
    </resultMap>

    <!-- 字段列表 -->
    <sql id="Base_Column_List">
        id, paper_id, book_id, create_time, update_time, create_by, update_by, enable
    </sql>

    <!-- 插入或更新关系 -->
    <insert id="insertOrUpdate" parameterType="PaperBookRelationPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO paper_book_relation (
            paper_id,
            book_id,
            create_by,
            update_by,
            enable
        )
        VALUES (
            #{paperId},
            #{bookId},
            #{createBy},
            #{updateBy},
            #{enable}
        )
        ON DUPLICATE KEY UPDATE
            update_by = #{updateBy},
            update_time = CURRENT_TIMESTAMP,
            enable = #{enable}
    </insert>

    <!-- 更新关系 -->
    <update id="update" parameterType="PaperBookRelationPO">
        UPDATE paper_book_relation
        <set>
            <if test="paperId != null">
                paper_id = #{paperId},
            </if>
            <if test="bookId != null">
                book_id = #{bookId},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="enable != null">
                enable = #{enable},
            </if>
            update_time = CURRENT_TIMESTAMP
        </set>
        WHERE
            enable = 1
        <if test="id != null">
            AND id = #{id}
        </if>
        <if test="paperId != null">
            AND paper_id = #{paperId}
        </if>
        <if test="bookId != null">
            AND book_id = #{bookId}
        </if>
    </update>

    <!-- 获取教材的所有试卷 -->
    <select id="selectList" resultMap="PaperBookRelationResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM paper_book_relation
        WHERE enable = 1 AND book_id = #{bookId}
    </select>

    <!-- 获取试卷的教材 -->
    <select id="selectBookIdByPaperId" resultType="java.lang.String">
        SELECT book_id
        FROM paper_book_relation
        WHERE paper_id = #{paperId}
        LIMIT 1
    </select>

</mapper>