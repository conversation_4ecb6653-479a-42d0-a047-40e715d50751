<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.unipus.digitalbook.dao.PaperChapterReferencePOMapper">

    <!-- 定义结果映射 -->
    <resultMap id="PaperResultMap" type="PaperChapterReferencePO">
        <id property="id" column="id" />
        <result property="paperId" column="paper_id" />
        <result property="bookId" column="book_id" />
        <result property="chapterId" column="chapter_id" />
        <result property="position" column="position" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="createBy" column="create_by" />
        <result property="updateBy" column="update_by" />
        <result property="enable" column="enable" />
    </resultMap>

    <!-- 字段列表 -->
    <sql id="Base_Column_List">
        id, paper_id, book_id, chapter_id, position, create_time, update_time, create_by, update_by, enable
    </sql>

    <insert id="batchSave" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO paper_chapter_reference (
            paper_id,
            book_id,
            chapter_id,
            position,
            create_by,
            update_by,
            enable
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.paperId},
                #{item.bookId},
                #{item.chapterId},
                #{item.position},
                #{item.createBy},
                #{item.updateBy},
                #{item.enable}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            position = VALUES(position),
            update_by = VALUES(update_by),
            enable = VALUES(enable)
    </insert>

    <!-- 插入引用 -->
    <insert id="insert" parameterType="PaperChapterReferencePO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO paper_chapter_reference (
        paper_id,
        book_id,
        chapter_id,
        position,
        create_by,
        update_by,
        enable
        )
        VALUES (
        #{paperId},
        #{bookId},
        #{chapterId},
        #{position},
        #{createBy},
        #{updateBy},
        #{enable}
        )
    </insert>

    <!-- 删除引用 -->
    <update id="delete" parameterType="PaperChapterReferencePO">
        UPDATE paper_chapter_reference
        SET
            update_by = #{userId},
            enable = 0,
            update_time = CURRENT_TIMESTAMP
        WHERE
            id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
            AND enable = 1
    </update>

    <!-- 删除指定章节中的所有试卷引用 -->
    <update id="removeReferenceInChapter">
        UPDATE paper_chapter_reference
        SET
            update_by = #{userId},
            enable = 0,
            update_time = CURRENT_TIMESTAMP
        WHERE enable = 1
            AND book_id = #{bookId}
            AND chapter_id = #{chapterId}
    </update>

    <!-- 获取启用的所有引用列表 -->
    <select id="selectList" parameterType="PaperChapterReferencePO" resultMap="PaperResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM paper_chapter_reference
        WHERE enable = 1
        <if test="paperId != null">
            AND paper_id = #{paperId}
        </if>
        <if test="bookId != null">
            AND book_id = #{bookId}
        </if>
        <if test="chapterId != null">
            AND chapter_id = #{chapterId}
        </if>
        <if test="position != null">
            AND position = #{position}
        </if>
    </select>

    <!-- 获取启用的所有引用列表 -->
    <select id="selectLatestReferenceByBookId" resultType="PaperChapterReferencePO">
        SELECT
            default_pcr.id AS id,
            default_pcr.paper_id AS paperId,
            default_pcr.book_id AS bookId,
            default_pcr.chapter_id AS chapterId,
            default_pcr.position AS position,
            default_pcr.create_time AS createTime,
            qg.question_text AS paperName,
            qg.id AS paperPrimaryId
        FROM paper_chapter_reference default_pcr
        JOIN chapter ON chapter.id = default_pcr.chapter_id AND chapter.enable = 1
        JOIN question_group qg ON qg.biz_group_id = default_pcr.paper_id
            AND qg.enable = 1
            AND qg.version_number = '0'
        WHERE default_pcr.enable = 1
            AND default_pcr.book_id = #{bookId}
        ORDER BY chapter.chapter_number, qg.question_text;
    </select>

    <!-- 根据试卷ID列表批量取得试卷引用 -->
    <select id="selectReferenceByPaperIds" resultMap="PaperResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM paper_chapter_reference
        WHERE enable = 1
        <if test="paperIds != null and paperIds.size > 0">
            AND paper_id IN
            <foreach collection="paperIds" item="paperId" open="(" separator="," close=")">
                #{paperId}
            </foreach>
        </if>
    </select>

</mapper>