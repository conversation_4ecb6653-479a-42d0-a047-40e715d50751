<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.PaperQuestionRelationPOMapper">

    <resultMap id="BaseResultMap" type="PaperQuestionRelationPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="paper_id" property="paperId" jdbcType="VARCHAR"/>
        <result column="biz_question_id_base" property="bizQuestionIdBase" jdbcType="VARCHAR"/>
        <result column="biz_question_id_target" property="bizQuestionIdTarget" jdbcType="VARCHAR"/>
        <result column="paper_version_number" property="paperVersionNumber" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="BIGINT"/>
        <result column="update_by" property="updateBy" jdbcType="BIGINT"/>
        <result column="enable" property="enable" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, paper_id, biz_question_id_base, biz_question_id_target, paper_version_number,
        create_time, update_time, create_by, update_by, enable
    </sql>

    <insert id="insert" parameterType="PaperQuestionRelationPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO paper_question_relation
        (paper_id, biz_question_id_base, biz_question_id_target, paper_version_number, create_by, update_by, enable)
        VALUES
        (#{paperId}, #{bizQuestionIdBase}, #{bizQuestionIdTarget}, #{paperVersionNumber}, #{createBy}, #{updateBy}, #{enable})
    </insert>

    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO paper_question_relation
        (paper_id, biz_question_id_base, biz_question_id_target, paper_version_number, create_by, update_by, enable)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.paperId}, #{item.bizQuestionIdBase}, #{item.bizQuestionIdTarget}, #{item.paperVersionNumber},
            #{item.createBy}, #{item.updateBy}, #{item.enable})
        </foreach>
        ON DUPLICATE KEY UPDATE
        biz_question_id_target = VALUES(biz_question_id_target),
        update_by = VALUES(update_by),
        enable = VALUES(enable)
    </insert>

    <update id="removePaperQuestionRelation">
        UPDATE paper_question_relation
        SET enable = 0, update_by = #{updateBy}
        WHERE paper_id = #{paperId}
          AND paper_version_number = #{paperVersionNumber}
          AND enable = 1
    </update>

    <!-- 批量更新关联关系 -->
    <!-- 如果 baseIdsToKeep 为空，则上面的 <if> 不会拼接，此SQL会软删除该试卷版本下所有关系 -->
    <update id="softDeleteRelationsNotInSet">
        UPDATE paper_question_relation
        SET enable = 0, update_by = #{userId}
        WHERE
            paper_id = #{paperId}
        AND paper_version_number = #{versionNumber}
        AND enable = 1
        <if test="baseIdsToKeep != null and !baseIdsToKeep.isEmpty()">
            AND biz_question_id_base NOT IN
            <foreach item="id" collection="baseIdsToKeep" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List"/>
        FROM paper_question_relation
        WHERE id = #{id}
    </select>

    <select id="selectByBizQuestionIdBase" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM paper_question_relation
        WHERE enable = 1 AND biz_question_id_base = #{bizQuestionIdBase}
    </select>

    <select id="selectByBizQuestionIdTarget" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM paper_question_relation
        WHERE enable = 1 AND biz_question_id_target = #{bizQuestionIdTarget}
    </select>

    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM paper_question_relation
        WHERE enable = 1
        <if test="paperId != null">
            AND paper_id = #{paperId}
        </if>
        <if test="bizQuestionIdBase != null">
            AND biz_question_id_base = #{bizQuestionIdBase}
        </if>
        <if test="bizQuestionIdTarget != null">
            AND biz_question_id_target = #{bizQuestionIdTarget}
        </if>
        <if test="paperVersionNumber != null">
            AND paper_version_number = #{paperVersionNumber}
        </if>
    </select>

</mapper>