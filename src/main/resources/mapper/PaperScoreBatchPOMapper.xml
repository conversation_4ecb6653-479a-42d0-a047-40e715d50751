<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.PaperScoreBatchPOMapper">

    <resultMap id="BaseResultMap" type="PaperScoreBatchPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="paper_id" property="paperId" jdbcType="VARCHAR"/>
        <result column="paper_version_number" property="paperVersionNumber" jdbcType="VARCHAR"/>
        <result column="paper_type" property="paperType" jdbcType="INTEGER"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="open_id" property="openId" jdbcType="VARCHAR"/>
        <result column="user_score" property="userScore" jdbcType="DECIMAL"/>
        <result column="standard_score" property="standardScore" jdbcType="DECIMAL"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="score_submit_date" property="scoreSubmitDate" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="enable" property="enable" jdbcType="BIT"/>
    </resultMap>

    <!-- 字段列表 -->
    <sql id="Base_Column_List">
        id, paper_id, paper_version_number, paper_type, tenant_id,
        open_id, user_score, standard_score, status, score_submit_date,
        create_time, update_time, create_by, update_by, enable
    </sql>

    <!-- 新增或更新记录 -->
    <insert id="insertOrUpdate" parameterType="PaperScoreBatchPO">
        INSERT INTO paper_score_batch (
            id, paper_id, paper_version_number, paper_type, tenant_id, open_id,
            user_score, standard_score, status, score_submit_date, create_by, update_by, enable
        ) VALUES (
            #{id},
            #{paperId},
            #{paperVersionNumber},
            #{paperType},
            #{tenantId},
            #{openId},
            #{userScore},
            #{standardScore},
            #{status},
            #{scoreSubmitDate},
            #{createBy},
            #{updateBy},
            #{enable}
        )
        ON DUPLICATE KEY UPDATE
            paper_id = VALUES(paper_id),
            paper_version_number = VALUES(paper_version_number),
            paper_type = VALUES(paper_type),
            tenant_id = VALUES(tenant_id),
            open_id = VALUES(open_id),
            user_score = VALUES(user_score),
            standard_score = VALUES(standard_score),
            status = VALUES(status),
            score_submit_date = VALUES(score_submit_date),
            update_by = VALUES(update_by),
            update_time = NOW(),
            enable = VALUES(enable)
    </insert>

    <!-- 根据ID更新记录 -->
    <update id="updateById" parameterType="PaperScoreBatchPO">
        UPDATE paper_score_batch
        <set>
            <if test="paperId != null">paper_id = #{paperId},</if>
            <if test="paperVersionNumber != null">paper_version_number = #{paperVersionNumber},</if>
            <if test="paperType != null">paper_type = #{paperType},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="userScore != null">user_score = #{userScore},</if>
            <if test="standardScore != null">standard_score = #{standardScore},</if>
            <if test="status != null">status = #{status},</if>
            <if test="scoreSubmitDate != null">score_submit_date = #{scoreSubmitDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="enable != null">enable = #{enable},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据条件查询记录 -->
    <select id="selectByCondition" parameterType="PaperScoreBatchPO" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM paper_score_batch
        WHERE enable = 1
            <if test="id != null">AND id = #{id}</if>
            <if test="paperId != null">AND paper_id = #{paperId}</if>
            <if test="paperVersionNumber != null">AND paper_version_number = #{paperVersionNumber}</if>
            <if test="paperType != null">AND paper_type = #{paperType}</if>
            <if test="tenantId != null">AND tenant_id = #{tenantId}</if>
            <if test="openId != null and openId != ''">AND open_id = #{openId}</if>
            <if test="status != null">AND status = #{status}</if>
            <if test="scoreSubmitDate != null">
                AND DATE(score_submit_date) = DATE(#{scoreSubmitDate})
            </if>
        ORDER BY score_submit_date DESC
    </select>

    <!-- 根据条件查询最新的记录 -->
    <select id="getLatestPaperScoreBatch" parameterType="PaperScoreBatchPO" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM paper_score_batch
        WHERE enable = 1
            <if test="id != null">AND id = #{id}</if>
            <if test="paperId != null">AND paper_id = #{paperId}</if>
            <if test="paperVersionNumber != null">AND paper_version_number = #{paperVersionNumber}</if>
            <if test="paperType != null">AND paper_type = #{paperType}</if>
            <if test="tenantId != null">AND tenant_id = #{tenantId}</if>
            <if test="openId != null and openId != ''">AND open_id = #{openId}</if>
            <if test="status != null">AND status = #{status}</if>
            <if test="scoreSubmitDate != null">
                AND DATE(score_submit_date) = DATE(#{scoreSubmitDate})
            </if>
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!--通过试卷实例ID取得试卷成绩批次记录 -->
    <select id="gePaperScoreBatchByInstanceId" resultMap="BaseResultMap">
        SELECT
            psb.id,
            psb.paper_id,
            psb.paper_version_number,
            psb.paper_type,
            psb.tenant_id,
            psb.open_id,
            psb.user_score,
            psb.standard_score,
            psb.status,
            psb.score_submit_date,
            psb.create_time,
            psb.update_time,
            psb.create_by,
            psb.update_by,
            psb.enable
        FROM paper_score_batch psb
        JOIN paper_round pr ON pr.score_batch_id = psb.id
        WHERE pr.id = #{instanceId}
        <if test="status != null">AND psb.status = #{status}</if>
        AND psb.enable = 1
    </select>

    <!-- 删除预览卷作答记录 -->
    <update id="deletePreviewInfo">
        UPDATE paper_score_batch
        SET enable = 0
        WHERE paper_id = #{paperId}
            AND paper_version_number = #{versionNumber}
            AND open_id = #{openId}
            AND tenant_id = #{tenantId}
    </update>
</mapper>