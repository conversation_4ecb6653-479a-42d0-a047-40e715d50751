<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipus.digitalbook.dao.UserCustomContentProgressPOMapper">
  <resultMap id="BaseResultMap" type="com.unipus.digitalbook.model.po.UserCustomContentProgressPO">
    <!--@mbg.generated-->
    <!--@Table user_custom_content_progress-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="content_id" jdbcType="BIGINT" property="contentId" />
    <result column="progress_bit" jdbcType="BLOB" property="progressBit" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, open_id, tenant_id, content_id, progress_bit, `enable`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from user_custom_content_progress
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from user_custom_content_progress
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.unipus.digitalbook.model.po.UserCustomContentProgressPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into user_custom_content_progress
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="openId != null and openId != ''">
        open_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="contentId != null">
        content_id,
      </if>
      <if test="progressBit != null">
        progress_bit,
      </if>
      <if test="enable != null">
        `enable`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="openId != null and openId != ''">
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="contentId != null">
        #{contentId,jdbcType=BIGINT},
      </if>
      <if test="progressBit != null">
        #{progressBit,jdbcType=BLOB},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.unipus.digitalbook.model.po.UserCustomContentProgressPO">
    <!--@mbg.generated-->
    update user_custom_content_progress
    <set>
      <if test="openId != null and openId != ''">
        open_id = #{openId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="contentId != null">
        content_id = #{contentId,jdbcType=BIGINT},
      </if>
      <if test="progressBit != null">
        progress_bit = #{progressBit,jdbcType=BLOB},
      </if>
      <if test="enable != null">
        `enable` = #{enable,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update user_custom_content_progress
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="open_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.openId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.openId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tenant_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tenantId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.tenantId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="content_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.contentId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.contentId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="progress_bit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.progressBit != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.progressBit,jdbcType=BLOB}
          </if>
        </foreach>
      </trim>
      <trim prefix="`enable` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.enable != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.enable,jdbcType=BIT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>