package com.unipus.digitalbook.mapper;

import com.unipus.digitalbook.dao.CourseKnowledgeSourceInfoMapper;
import com.unipus.digitalbook.model.enums.StatusEnum;
import com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeSourceInfoPO;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class CourseKnowledgeSourceInfoMapperTest {

    @Autowired
    private CourseKnowledgeSourceInfoMapper mapper;

    @Test
    public void testInsert() {
        String url = "KppdGIvOSj-UriUboqlpiQ/ee58ed8c-bc35-4997-ad0b-d170d4334ca9/EDITOR_ANCHOR_GqOv5B6SyPWPInBg";
        String urlHash = DigestUtils.md5Hex("mainsourseUrl");
        String urls = "[" + url + "]";
        String urlsHash = "[" + urlHash + "]";
        CourseKnowledgeSourceInfoPO record = new CourseKnowledgeSourceInfoPO();
        record.setCourseIdStr("cource:v2:xxxxxxxxxx");
//        record.setCourseId(1L);
        record.setUnitId("unit1");
//        record.setTaskId();
        record.setType(1);
//        record.setStartTime();
//        record.setEndTime();
        record.setCourseKnowledgeId(1L);
//        record.setMainSourceUrl(url);
//        record.setMainSourceHash(urlHash);
//        record.setUrls(urls);
//        record.setUrlsHash(urlsHash);
        record.setKnowledgeId("图谱Id");
        record.setGraphId("子图Id");
        record.setGraphNodeId("节点Id");
//        record.setKnowledgeSourseId("资源Id");
//        record.setEnable();
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setCreateBy(1l);
        record.setUpdateBy(1l);


        int result = mapper.insertSelective(record);
        assertEquals(1, result);
    }

    @Test
    public void testSelectByPrimaryKey() {
        CourseKnowledgeSourceInfoPO record = mapper.selectByPrimaryKey(1L);
        assertNotNull(record);
    }

    @Test
    public void testUpdateByPrimaryKeySelective() {
        CourseKnowledgeSourceInfoPO record = new CourseKnowledgeSourceInfoPO();
        record.setId(1L);
//        record.setEnable(StatusEnum.DISABLE.getCode());
        int result = mapper.updateByPrimaryKeySelective(record);
        assertEquals(1, result);
        record = mapper.selectByPrimaryKey(1L);
//        assertEquals(StatusEnum.DISABLE.getCode(), record.getEnable());
    }

    @Test
    public void testDeleteByPrimaryKey() {
        int result = mapper.deleteByPrimaryKey(1L);
        assertEquals(1, result);
        CourseKnowledgeSourceInfoPO record = mapper.selectByPrimaryKey(1L);
        assertNull(record);
    }

    public static void main(String[] args) {
        String url = "KppdGIvOSj-UriUboqlpiQ/ee58ed8c-bc35-4997-ad0b-d170d4334ca9/EDITOR_ANCHOR_GqOv5B6SyPWPInBg";
        String urlHash = DigestUtils.md5Hex(url);
        System.out.println(urlHash);
        urlHash = DigestUtils.md5Hex(url);
        System.out.println(urlHash);

    }
}
