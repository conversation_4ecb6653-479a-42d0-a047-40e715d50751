package com.unipus.digitalbook.mapper;

import com.unipus.digitalbook.dao.CourseKnowledgeInfoMapper;
import com.unipus.digitalbook.model.enums.DeleteEnum;
import com.unipus.digitalbook.model.enums.EnableEnum;
import com.unipus.digitalbook.model.enums.KnowledgeStatusEnum;
import com.unipus.digitalbook.model.po.knowledge.CourseKnowledgeInfoPO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;

@SpringBootTest
class KnowledgeServiceImplTest {
    @Resource
    private CourseKnowledgeInfoMapper courseKnowledgeInfoMapper;

    @Test
    void saveQuestionGroup() {

    }

    @Test
    void testInsert() {
        CourseKnowledgeInfoPO insertKnowledgeInfo = new CourseKnowledgeInfoPO();
        insertKnowledgeInfo.setCourseIdStr("xxxxxx");
        insertKnowledgeInfo.setName("params.getName()");
        insertKnowledgeInfo.setDescription("params.getBackground()");
        insertKnowledgeInfo.setKnowledgeId("knowledgeId");
        insertKnowledgeInfo.setBackgroundUrl(null);
        insertKnowledgeInfo.setCreateBy(1071L);
        insertKnowledgeInfo.setUpdateBy(1071L);
        insertKnowledgeInfo.setCreateTime(new Date());
        insertKnowledgeInfo.setUpdateTime(new Date());
        courseKnowledgeInfoMapper.insertSelective(insertKnowledgeInfo);
    }
}