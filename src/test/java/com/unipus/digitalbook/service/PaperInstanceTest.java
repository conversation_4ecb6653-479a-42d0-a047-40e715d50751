package com.unipus.digitalbook.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class PaperInstanceTest {

    Integer challengeRoundCount = 10;
    Integer countPerRound = 3;
    Integer bankQuestionCount = 20;
    Map<Long, Integer> baseCountMap = new HashMap<>();
    List<Long> ids = new ArrayList<>();

    @BeforeEach
    void init() {
        // 使用简单循环初始化题目ID与提交次数映射
        // LongStream.range(0,bankQuestionCount).forEach(i->baseCountMap.put(i, 0));
        baseCountMap.put(0L, 4);
        baseCountMap.put(1L, 4);
        baseCountMap.put(2L, 2);
        baseCountMap.put(3L, 2);
        baseCountMap.put(4L, 1);
        baseCountMap.put(5L, 1);

        // 直接转换Set为List初始化ids列表
        ids = new ArrayList<>(baseCountMap.keySet());
    }

    @Test
    void test() {
        IntStream.range(0,challengeRoundCount).forEach(r->{
            List<Long> drawnQuestions = getCurrentRoundQuestions();
            drawnQuestions.forEach(d -> baseCountMap.merge(d, 1, Integer::sum));
            outputLog(baseCountMap, drawnQuestions, r);
        });
    }

    private void outputLog(Map<Long, Integer> baseCountMap, List<Long> drawnQuestions, Integer r){
        StringBuilder sb = new StringBuilder();
        sb.append(MessageFormat.format("round:{0,number,00}-> ", r));
        baseCountMap.forEach((k, v) -> {
            sb.append("  ");
            sb.append(MessageFormat.format("[{0}]:{1}", k, v));
            sb.append(drawnQuestions.contains(k) ? "*" : " ");
        });
        System.out.println(sb);
    }

    private List<Long> getCurrentRoundQuestions(){
        // 按提交次数对题目进行分组，使用次数升序排序(TreeMap默认排序)
        Map<Integer, List<Long>> groupedQuestions = ids.stream()
                .collect(Collectors.groupingBy(q -> baseCountMap.getOrDefault(q, 0), TreeMap::new, Collectors.toList()));
        
        // 对每组内的题目进行随机排序，然后扁平化处理生成按提交次数升序排列的题目列表
        List<Long> sortedQuestions = groupedQuestions.values().stream()
                .peek(Collections::shuffle) // 对同一组内的数据进行随机处理
                .flatMap(List::stream)
                .collect(Collectors.toList());
                
        // 取出指定数量的题目（考虑列表实际长度）
        List<Long> drawnQuestions = new ArrayList<>(sortedQuestions.subList(0, Math.min(countPerRound, sortedQuestions.size())));
        // 对抽取到的题目列表进行随机排序
        Collections.shuffle(drawnQuestions);
        return drawnQuestions;
    }


}
