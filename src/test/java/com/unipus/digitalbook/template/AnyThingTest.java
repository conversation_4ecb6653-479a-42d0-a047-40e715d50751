package com.unipus.digitalbook.template;

import com.unipus.digitalbook.dao.PaperQuestionRelationPOMapper;
import com.unipus.digitalbook.model.entity.paper.QuestionTag;
import com.unipus.digitalbook.model.po.paper.PaperQuestionRelationPO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class AnyThingTest {

    @Resource
    private PaperQuestionRelationPOMapper paperQuestionRelationPOMapper;

    @org.junit.jupiter.api.Test
    public void testSaveQuestionRelation() {
        List<QuestionTag> questionTags = List.of(new QuestionTag());
        String paperId = "1";
        String versionNumber = "1";
        Long userId = 1L;
        saveQuestionRelation(questionTags, paperId, versionNumber, userId);
    }

    private void saveQuestionRelation(List<QuestionTag> questionTags, String paperId, String versionNumber, Long userId) {
        // 1: 根据输入参数，准备好本次操作最终需要存在于数据库的关系列表。
        List<PaperQuestionRelationPO> relationsToUpsert = CollectionUtils.isEmpty(questionTags) ? Collections.emptyList()
                : questionTags.stream()
                .filter(qt -> qt.getBaseResourceId() != null)
                .map(questionTag -> new PaperQuestionRelationPO(questionTag, paperId, versionNumber, userId))
                .toList();

        // 2: 从需要保留的关系中，提取出所有基础题目ID (base_question_id)。
        Set<String> baseIdsToKeep = relationsToUpsert.stream()
                .map(PaperQuestionRelationPO::getBizQuestionIdBase)
                .collect(Collectors.toSet());

        // 3: 【核心优化点】直接在数据库中软删除那些不再需要的关系。
        // 即：更新所有 paperId 和 versionNumber 匹配，但 biz_question_id_base 不在 'baseIdsToKeep' 集合中的记录。
        // 这个操作避免了将旧数据全部查询到应用内存中，性能开销极小。
        // 注意：如果 baseIdsToKeep 为空，则会软删除该试卷版本下的所有关系，逻辑正确。

        paperQuestionRelationPOMapper.softDeleteRelationsNotInSet(paperId, versionNumber, userId, baseIdsToKeep);

        // 4: 如果有需要保存的关系，则执行批量插入或更新。
        if (!relationsToUpsert.isEmpty()) {
            paperQuestionRelationPOMapper.batchInsertOrUpdate(relationsToUpsert);
        }
    }


}
