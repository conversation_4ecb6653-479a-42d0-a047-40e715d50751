package com.unipus.digitalbook.template;


import com.unipus.digitalbook.template.model.BatchProcessRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 批量处理模板抽象类
 * 实现模板方法模式，定义批量处理的标准流程
 */
@Slf4j
abstract class BatchProcessTemplate<T> {

    /**
     * 批量处理模板方法
     * 定义标准的批量处理流程：查询现有数据 -> 确定需要保留的数据 -> 删除多余数据 -> 批量插入或更新数据
     *
     * @param request 批量处理请求对象
     */
    public final void batchProcessTemplate(BatchProcessRequest<T> request) {
        if (request == null || request.getData() == null) {
            throw new IllegalArgumentException("批量处理请求不能为空");
        }

        List<T> newData = request.getData();
        String parentId = request.getParentId();

        log.info("1. 查询现有数据...");
        // 1. 根据parentId查询现有数据
        List<T> existingData = queryExistingData(parentId);
        log.info("1.找到现有数据 {} 条", existingData.size());

        log.info("2. 确定需要保留的数据...");
        // 2. 根据新数据中指定的key，过滤要保留的数据的needKeepIds
        Set<String> needKeepKeys = newData.parallelStream()
                .map(request.getKeyExtractor())
                .collect(Collectors.toSet());
        log.info("   需要保留的key: {}", needKeepKeys);

        List<Object> needKeepIds = existingData.parallelStream()
                .filter(item -> needKeepKeys.contains(request.getKeyExtractor().apply(item)))
                .map(this::extractId)
                .collect(Collectors.toList());
        log.info("   需要保留的ID: {}", needKeepIds);

        log.info("3. 删除多余数据...");
        // 3. 删除needKeepIds以外的数据
        if (!existingData.isEmpty()) {
            deleteByParentIdExcludeIds(parentId, needKeepIds);
            log.info("   删除操作完成");
        }

        log.info("4. 批量插入或更新数据...");
        // 4. 批量插入或更新数据
        if (!newData.isEmpty()) {
            batchInsertOrUpdate(newData, parentId);
            log.info("   插入/更新操作完成");
        }
    }

    /**
     * 查询现有数据
     * @param parentId 父级ID
     * @return 现有数据列表
     */
    protected abstract List<T> queryExistingData(String parentId);

    /**
     * 提取数据的ID
     * @param data 数据对象
     * @return ID值
     */
    protected abstract Object extractId(T data);

    /**
     * 根据父级ID删除数据，排除指定的ID列表
     * @param parentId 父级ID
     * @param excludeIds 需要排除的ID列表
     */
    protected abstract void deleteByParentIdExcludeIds(String parentId, List<Object> excludeIds);

    /**
     * 批量插入或更新数据
     * @param data 数据列表
     * @param parentId 父级ID
     */
    protected abstract void batchInsertOrUpdate(List<T> data, String parentId);
}