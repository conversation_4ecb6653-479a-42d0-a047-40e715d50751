package com.unipus.digitalbook.template;

import org.junit.jupiter.api.Test;

/**
 * 一次性完成数据库的增删改查
 */
public class BatchSaveTemplateTest {

    @Test
    public void batchSaveTemplate() {
        //
    }

    static class BatchProcessTemplate {

        public <T> void batchProcessTemplate(T data) {
            // 根据data中指定的key，过滤要保留的数据的needKeepIds

            // 删除needKeepIds以外的数据

            // 批量插入或更新数据data

        }
    }
}
