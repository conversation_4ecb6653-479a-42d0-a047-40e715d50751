package com.unipus.digitalbook.template;


import com.unipus.digitalbook.template.model.TestDataPO;

import java.util.*;

/**
 * 测试批量处理器
 * 继承批量处理模板，实现具体的数据库操作逻辑
 */
class CustomBatchProcessor extends BatchProcessTemplate<TestDataPO> {
    private final List<TestDataPO> mockDatabase = new ArrayList<>();
    private Long nextId = 1L;

    public void setExistingData(List<TestDataPO> existingData) {
        this.mockDatabase.clear();
        this.mockDatabase.addAll(existingData);
        // 设置下一个ID
        this.nextId = existingData.stream()
                .mapToLong(TestDataPO::getId)
                .max()
                .orElse(0L) + 1;
    }

    public List<TestDataPO> getCurrentData() {
        return new ArrayList<>(mockDatabase);
    }

    @Override
    protected List<TestDataPO> queryExistingData(String parentId) {
        // 模拟根据parentId查询现有数据
        // 在实际应用中，这里会查询数据库中parentId匹配的数据
        // 为了演示，我们假设所有现有数据都属于同一个parentId
        return new ArrayList<>(mockDatabase);
    }

    @Override
    protected Object extractId(TestDataPO data) {
        return data.getId();
    }

    @Override
    protected void deleteByParentIdExcludeIds(String parentId, List<Object> excludeIds) {
        // 模拟删除操作：删除除了excludeIds以外的所有数据
        // 在实际应用中，这里会执行SQL删除语句
        mockDatabase.removeIf(data -> !excludeIds.contains(data.getId()));
    }

    @Override
    protected void batchInsertOrUpdate(List<TestDataPO> data, String parentId) {
        // 模拟批量插入或更新操作
        for (TestDataPO newData : data) {
            // 设置parentId
            newData.setParentId(parentId);

            // 根据businessKey查找是否已存在
            Optional<TestDataPO> existing = mockDatabase.stream()
                    .filter(d -> Objects.equals(d.getBusinessKey(), newData.getBusinessKey()))
                    .findFirst();

            if (existing.isPresent()) {
                // 更新现有数据
                TestDataPO existingData = existing.get();
                existingData.setName(newData.getName());
                existingData.setUpdateTime(new Date());
                existingData.setEnable(newData.getEnable());
            } else {
                // 插入新数据
                newData.setId(nextId++);
                newData.setCreateTime(new Date());
                newData.setUpdateTime(new Date());
                mockDatabase.add(newData);
            }
        }
    }
}