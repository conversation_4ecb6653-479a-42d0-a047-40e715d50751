package com.unipus.digitalbook.template.model;


import java.util.Date;

/**
 * 测试数据PO类
 * 模拟项目中常见的PO类结构
 */
public class TestDataPO {
    private Long id;
    private String businessKey;
    private String name;
    private String parentId;
    private Date createTime;
    private Date updateTime;
    private Long createBy;
    private Long updateBy;
    private Boolean enable;

    public TestDataPO() {
    }

    public TestDataPO(Long id, String businessKey, String name, Boolean enable) {
        this.id = id;
        this.businessKey = businessKey;
        this.name = name;
        this.enable = enable;
        this.createTime = new Date();
        this.updateTime = new Date();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    @Override
    public String toString() {
        return "TestDataPO{" +
                "id=" + id +
                ", businessKey='" + businessKey + '\'' +
                ", name='" + name + '\'' +
                ", parentId='" + parentId + '\'' +
                ", enable=" + enable +
                '}';
    }
}
